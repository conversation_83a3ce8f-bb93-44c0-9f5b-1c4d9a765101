<?php
require_once 'vendor/autoload.php';
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_db.php';
require_once 'inc/cfg_session.php';
require_once 'inc/cfg_functions.php';
require_once 'inc/cfg_app_settings.php';

// Check if user has IT specialist or Administrator role
$hasAccess = AccessControl::hasRole($SESSION_id_rol, ['IT', 'Administrator']);
if (!$hasAccess) {
    header('Location: index.php');
    exit;
}
?>

<div class="container-xxl py-5">
    <div class="container">
        <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
            <h1 class="mb-3">Despre aplicație</h1>
            <p>Informații despre versiunea curentă și istoricul modificărilor</p>
        </div>

        <div class="row mb-5 g-4">
            <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.1s">
                <div class="service-item bg-light rounded h-100 p-4">
                    <div class="d-inline-flex align-items-center justify-content-center bg-white rounded-circle mb-4" style="width: 65px; height: 65px;">
                        <i class="fa fa-code-branch text-primary fs-4"></i>
                    </div>
                    <h4 class="mb-3"><?php echo $APP_NAME; ?></h4>
                    <p class="mb-2">
                        <strong><?php echo getAppVersionString(); ?></strong>
                    </p>
                    <p class="mb-2">
                        Data lansare: <?php echo $APP_VERSION['date']; ?>
                    </p>
                      <p class="mb-0">
                        <small class="text-primary fw-semibold" style="letter-spacing: 0.03em; display: inline-flex; align-items: center;">
                            <i class="fa fa-laptop-code me-2" style="opacity: 0.85;"></i>
                            <span class="fst-italic">Direcția Tehnologia Informației</span>
                        </small>
                    </p>
                    <p class="mb-0 mt-2">
                        <small class="text-primary fw-semibold" style="letter-spacing: 0.03em; display: inline-flex; align-items: center;">
                            <i class="fa fa-balance-scale me-2" style="opacity: 0.85;"></i>
                            <span class="fst-italic">Ministerul Justiției</span>
                        </small>
                    </p>
                </div>
            </div>

            <div class="col-lg-8 col-md-6 wow fadeInUp" data-wow-delay="0.3s">
                <div class="service-item bg-light rounded h-100 p-4">
                    <div class="d-inline-flex align-items-center justify-content-center bg-white rounded-circle mb-4" style="width: 65px; height: 65px;">
                        <i class="fa fa-history text-primary fs-4"></i>
                    </div>
                    <h4 class="mb-3">Istoric versiuni</h4>

                    <div class="accordion" id="versionHistory">
                        <?php
                        // Sort versions in descending order by version number
                        usort($APP_VERSION_HISTORY, function($a, $b) {
                            return version_compare($b['version'], $a['version']);
                        });

                        foreach ($APP_VERSION_HISTORY as $index => $version):
                        ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                                <button class="accordion-button <?php echo ($index === 0) ? '' : 'collapsed'; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $index; ?>" aria-expanded="<?php echo ($index === 0) ? 'true' : 'false'; ?>" aria-controls="collapse<?php echo $index; ?>">
                                    <span class="me-2">V<?php echo $version['version']; ?></span>
                                    <?php if (isset($version['current']) && $version['current']): ?>
                                    <span class="badge bg-success me-2">Curentă</span>
                                    <?php endif; ?>
                                    <small class="text-muted">Data: <?php echo $version['date']; ?></small>
                                </button>
                            </h2>
                            <div id="collapse<?php echo $index; ?>" class="accordion-collapse collapse <?php echo ($index === 0) ? 'show' : ''; ?>" aria-labelledby="heading<?php echo $index; ?>" data-bs-parent="#versionHistory">
                                <div class="accordion-body">
                                    <ul class="list-group list-group-flush">
                                        <?php foreach ($version['changes'] as $change): ?>
                                        <li class="list-group-item bg-transparent">
                                            <i class="fa fa-check-circle text-success me-2"></i>
                                            <?php
                                            if (is_array($change)) {
                                                echo $change['text'];
                                                if (!empty($change['manual_pages'])) {
                                                    echo '<div class="ms-4 mt-1">';
                                                    echo '<small class="text-muted">Pagini manual: ';
                                                    foreach ($change['manual_pages'] as $page) {
                                                        echo '<span class="badge bg-light text-dark me-1">Pag. ' . $page . '</span>';
                                                    }
                                                    echo '</small>';
                                                    echo '</div>';
                                                }
                                            } else {
                                                echo $change;
                                            }
                                            ?>
                                        </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'inc/cfg_footer.php';
?>
