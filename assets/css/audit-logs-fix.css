/*
 * <PERSON><PERSON><PERSON> pentru a rezolva problema cu overflow-y în coloana Date din audit_logs.php
 */

.filter-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.audit-table th, .audit-table td {
    font-size: 0.9rem;
}

.pagination {
    margin-top: 20px;
}

.json-data {
    max-height: 150px;
    overflow-y: auto;
    font-size: 0.8rem;
    background-color: #f8f9fa;
    padding: 5px;
    border-radius: 3px;
}

.badge-info {
    background-color: #17a2b8;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
}

/* Resetare completă pentru celulele din coloana Date */
.audit-table td:nth-child(10) {
    position: relative;
    vertical-align: top;
    max-width: 300px; /* Limitează lățimea coloanei */
    width: 300px;
    padding: 8px !important;
}

/* Resetare pentru div-urile din celule */
.audit-table td:nth-child(10) > div {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
}

/* Stiluri complet noi pentru json-data */
.audit-table td:nth-child(10) .json-data {
    max-height: 150px !important;
    height: auto !important;
    overflow-y: scroll !important; /* Folosim scroll în loc de auto pentru a forța scrollbar-ul */
    overflow-x: hidden !important;
    font-size: 0.8rem !important;
    background-color: #f8f9fa !important;
    padding: 5px !important;
    border-radius: 3px !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    border: 1px solid #ddd !important;
    word-break: break-word !important;
}

/* Stiluri pentru scrollbar - compatibile cu mai multe browsere */
.audit-table td:nth-child(10) .json-data::-webkit-scrollbar {
    width: 8px !important;
}

.audit-table td:nth-child(10) .json-data::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.audit-table td:nth-child(10) .json-data::-webkit-scrollbar-thumb {
    background: #888 !important;
    border-radius: 3px !important;
}

.audit-table td:nth-child(10) .json-data::-webkit-scrollbar-thumb:hover {
    background: #555 !important;
}

/* Stiluri pentru Firefox */
.audit-table td:nth-child(10) .json-data {
    scrollbar-width: thin !important;
    scrollbar-color: #888 #f1f1f1 !important;
}

/* Asigură că textul din json-data este formatat corect */
.audit-table td:nth-child(10) .json-data pre {
    margin: 0 !important;
    white-space: pre-wrap !important;
}

/* Adaugă un indicator vizual pentru a arăta că există conținut care poate fi derulat */
.audit-table td:nth-child(10) .json-data::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(to top, rgba(248, 249, 250, 0.9), rgba(248, 249, 250, 0));
    pointer-events: none;
    display: block;
}

/* Stiluri pentru a evidenția când utilizatorul face hover pe celulă */
.audit-table td:nth-child(10):hover .json-data {
    border-color: #aaa !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1) !important;
}
