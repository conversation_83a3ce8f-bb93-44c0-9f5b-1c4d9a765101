/* Expert info container */
.expert-info {
    border-left: 3px solid var(--primary);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.expert-info:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}


.specializare-group {
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
    padding-bottom: 5px;
}

.specializare-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Badge styling */
.badge.rounded-pill {
    font-weight: normal;
    letter-spacing: 0.3px;
}

/* Progress bar styling */
.progress {
    height: 8px !important;
    border-radius: 4px;
    overflow: hidden;
}

/* Tooltip styling */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    max-width: 300px;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.85);
    border-radius: 4px;
}
