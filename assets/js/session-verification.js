/**
 * Session verification script
 * This script adds proactive session checking before user interactions
 * and periodically checks session status
 */

// Global variables to track session status and user activity
let sessionActive = true;
let lastUserActivity = Date.now();
// Use the SESSION_TIMEOUT_MS constant from session-config.php
// which gets the value from inc/cfg_session.php: define('SESSION_TIMEOUT', 3600)
let inactivityTimeout = SESSION_TIMEOUT_MS; // Value from PHP constant
let sessionCheckInterval = 5 * 60 * 1000; // Check every 5 minutes
let lastServerUpdate = Date.now();
let updateDebounceTime = 30 * 1000; // Update server at most once every 30 seconds
let activityUpdateTimer = null;

// Function to update last activity timestamp on server
function updateLastActivity() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: 'controller/update_activity.php',
            type: 'POST',
            dataType: 'json',
            cache: false,
            success: function(response) {
                resolve(response);
            },
            error: function(xhr, status, error) {
                console.error('Error updating activity timestamp:', error);
                reject(error);
            }
        });
    });
}

// Function to check session status
function checkSessionStatus() {
    return new Promise((resolve, reject) => {
        // Check if user has been inactive for too long
        const currentTime = Date.now();
        if (currentTime - lastUserActivity > inactivityTimeout) {
            sessionActive = false;
            showSessionExpiredMessage();
            resolve(false);
            return;
        }

        $.ajax({
            url: 'controller/check_session.php',
            type: 'GET',
            dataType: 'json',
            cache: false,
            success: function(response) {
                if (response.status === 'valid') {
                    sessionActive = true;
                    resolve(true);
                } else {
                    sessionActive = false;
                    showSessionExpiredMessage();
                    resolve(false);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error checking session status:', error);
                // Assume session is invalid on error
                sessionActive = false;
                reject(error);
            }
        });
    });
}

// Function to show session expired message
function showSessionExpiredMessage() {
    Swal.fire({
        title: 'Sesiune expirată',
        text: 'Sesiunea dumneavoastră a expirat. Vă rugăm să vă autentificați din nou.',
        icon: 'warning',
        confirmButtonText: 'Autentificare',
        allowOutsideClick: false
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = 'login.php';
        }
    });
}

// Function to track user activity
function trackUserActivity() {
    const now = Date.now();
    lastUserActivity = now;

    // Clear any existing timer
    if (activityUpdateTimer) {
        clearTimeout(activityUpdateTimer);
    }

    // Only update server-side activity if session is active and enough time has passed
    if (sessionActive && (now - lastServerUpdate >= updateDebounceTime)) {
        lastServerUpdate = now;
        updateLastActivity().catch(error => {
            console.error('Failed to update activity timestamp:', error);
        });
    } else {
        // Schedule an update after the debounce period if no more activity occurs
        activityUpdateTimer = setTimeout(() => {
            if (sessionActive) {
                lastServerUpdate = Date.now();
                updateLastActivity().catch(error => {
                    console.error('Failed to update activity timestamp:', error);
                });
            }
        }, updateDebounceTime);
    }
}

// Last time we checked the session status during user interaction
let lastInteractionCheck = Date.now();
let interactionCheckInterval = 60 * 1000; // Check at most once per minute during interactions

// Function to handle user interactions
function handleUserInteraction(event) {
    // Track user activity
    trackUserActivity();

    // If we already know session is inactive, redirect immediately
    if (!sessionActive) {
        event.preventDefault();
        event.stopPropagation();
        window.location.href = 'login.php';
        return false;
    }

    const now = Date.now();
    // Only check session status if enough time has passed since last check
    if (now - lastInteractionCheck >= interactionCheckInterval) {
        lastInteractionCheck = now;

        // Check session status
        checkSessionStatus().then(isValid => {
            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        }).catch(error => {
            console.error('Error during session check:', error);
            event.preventDefault();
            event.stopPropagation();
            return false;
        });
    }
}

$(document).ready(function() {
    // Initial session check
    checkSessionStatus();

    // Set up periodic session check (every minute)
    setInterval(checkSessionStatus, sessionCheckInterval);

    // Add event listeners for user interactions
    $(document).on('mousedown', 'a, button, input, select, .clickable', handleUserInteraction);
    $(document).on('submit', 'form', handleUserInteraction);
    $(document).on('click', trackUserActivity);
    $(document).on('keypress', trackUserActivity);
    $(document).on('scroll', trackUserActivity);

    // Add event listener for AJAX requests
    $(document).ajaxSend(function(event, jqXHR, settings) {
        // Skip session check for the session check endpoint itself
        if (settings.url === 'controller/check_session.php' ||
            settings.url === 'controller/update_activity.php') {
            return;
        }

        // Check session before sending AJAX request
        if (!sessionActive) {
            jqXHR.abort();
            window.location.href = 'login.php';
            return false;
        }
    });

    // Track initial activity
    trackUserActivity();

    // Add visibility change detection
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            // Page became visible again, check session immediately
            checkSessionStatus();
        }
    });
});
