/**
 * Global AJAX error handler for session expiration
 * This script adds a global AJAX handler to detect session expiration responses
 * and redirect the user to the login page or show an appropriate message
 *
 * Enhanced with proactive session checking and handling of non-JSON responses
 */

$(document).ready(function() {
    // Add a global AJAX error handler
    $(document).ajaxComplete(function(event, xhr, settings) {
        // Skip session check for the session check endpoint itself
        if (settings.url === 'controller/check_session.php') {
            return;
        }

        // Check if the response is JSON
        try {
            if (xhr.responseText && xhr.getResponseHeader('content-type') &&
                xhr.getResponseHeader('content-type').includes('application/json')) {
                const response = JSON.parse(xhr.responseText);

                // Check if this is a session expiration response
                if (response.code === 'session_expired') {
                    // Show a message to the user
                    Swal.fire({
                        title: 'Sesiune expirată',
                        text: response.message || 'Sesiunea dumneavoastră a expirat. Vă rugăm să vă autentificați din nou.',
                        icon: 'warning',
                        confirmButtonText: 'Autentificare',
                        allowOutsideClick: false
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Redirect to login page
                            window.location.href = 'login.php';
                        }
                    });

                    // Prevent other handlers from processing this response
                    event.stopImmediatePropagation();
                    return false;
                }
            }
        } catch (e) {
            // Not JSON or parsing error, check if it's a login redirect
            console.log('Response is not JSON or has parsing error');

            // Check if the response contains a login page redirect
            if (xhr.status === 302 ||
                (xhr.responseText && xhr.responseText.toLowerCase().includes('login.php'))) {
                // Session likely expired, redirect to login
                window.location.href = 'login.php';
                return false;
            }
        }
    });

    // Handle 404 errors for controller/login.php which indicates session issues
    $(document).ajaxError(function(event, jqXHR, settings, thrownError) {
        // If we get a 404 for login.php, it means we need to redirect to the main login page
        if (settings.url.includes('controller/login.php') ||
            (jqXHR.status === 404 && settings.url.includes('controller/'))) {
            console.log('Detected invalid session with 404 response');
            window.location.href = 'login.php';
            return false;
        }
    });
});
