$(document).ready(function () {
    $('#tabelExperti').on('init.dt', function () {
        var searchDelay = null;
        $('#tabelExperti_filter input').off().on('input', function () {
            var self = this;
            clearTimeout(searchDelay);
            searchDelay = setTimeout(function () {
                table.search($(self).val()).draw();
            }, 1000);
        });
    });


    let exportFormatter = {
        format: {
            body: function (data, row, column, node) {
                var tempElement = document.createElement('div');
                tempElement.innerHTML = data;
                var divToRemove = tempElement.querySelector('.removeElement');
                if (divToRemove) {
                    divToRemove.parentNode.removeChild(divToRemove);
                }
                var divToRemove = tempElement.querySelector('select');
                if (divToRemove) {
                    divToRemove.parentNode.removeChild(divToRemove);
                }
                var divToRemove = tempElement.querySelector('input');
                if (divToRemove) {
                    divToRemove.parentNode.removeChild(divToRemove);
                }
                data = tempElement.innerHTML.replace(/<[^>]+>/g, '');
                return data;
            }
        }
    };


    var table = $('#tabelExperti').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "controller/_cVizualizare.php",
            "type": "POST",
            "dataSrc": "data",
            "error": function (xhr, error, thrown) {
                console.error('DataTable Error:', error, xhr.responseText);
            }
        },
        "lengthMenu": [[30, 50, 100], [30, 50, 100]],
        "pageLength": 30,
        "columns": [
            {"data": "expert"},
            {"data": "judet"},
            {"data": "adresa"},
            {"data": "telefon"},
            {"data": "incarcatura"},
            {"data": "specializare"}
        ],
        "orderCellsTop": true,
        "dom": "<'row'<'col-sm-12 col-md-4'l><'col-sm-12 col-md-4'B><'col-sm-12 col-md-4'f>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                text: 'Reset Filters',
                className: 'btn btn-outline-danger',
                action: function () {
                    table.search('').columns().search('').draw();
                    table.page.len(50).draw();
                    $('.select2').val('').trigger('change');
                }
            },
            {
                text: 'Export Excel',
                className: 'btn btn-outline-success',
                action: function (e, dt, button, config) {
                    // Get the selected specializare option and its optgroup
                    const specializareSelect = $('.NSpecializari');
                    const selectedOption = specializareSelect.find('option:selected');
                    const optgroup = selectedOption.parent('optgroup');

                    // Get current sorting information
                    const order = table.order();
                    const sortColumn = table.settings().init().columns[order[0][0]].data;
                    const sortDirection = order[0][1];

                    var filters = {
                        search: table.search(),
                        judet: $('.NJudete').val(),
                        specializare: optgroup.length ?
                            optgroup.attr('id') + ',' + specializareSelect.val() :
                            specializareSelect.val(),
                        length: table.page.len(),
                        start: table.page() * table.page.len(),
                        orderColumn: sortColumn,
                        orderDir: sortDirection
                    };

                    // Create form for POST submission
                    var $form = $('<form>', {
                        'method': 'POST',
                        'action': 'controller/exportData.php',
                        'target': '_blank'
                    });

                    // Add export type
                    $form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'exportType',
                        'value': 'excel'
                    }));

                    // Add all filters to form
                    $.each(filters, function (key, value) {
                        $form.append($('<input>', {
                            'type': 'hidden',
                            'name': key,
                            'value': value
                        }));
                    });

                    // Submit the form
                    $('body').append($form);
                    $form.submit();
                    $form.remove();
                }
            },
            {
                text: 'Export PDF',
                className: 'btn btn-outline-success',
                action: function (e, dt, button, config) {
                    // Get the selected specializare option and its optgroup
                    const specializareSelect = $('.NSpecializari');
                    const selectedOption = specializareSelect.find('option:selected');
                    const optgroup = selectedOption.parent('optgroup');

                    // Get current sorting information
                    const order = table.order();
                    const sortColumn = table.settings().init().columns[order[0][0]].data;
                    const sortDirection = order[0][1];

                    var filters = {
                        search: table.search(),
                        judet: $('.NJudete').val(),
                        specializare: optgroup.length ?
                            optgroup.attr('id') + ',' + specializareSelect.val() :
                            specializareSelect.val(),
                        length: table.page.len(),
                        start: table.page() * table.page.len(),
                        orderColumn: sortColumn,
                        orderDir: sortDirection
                    };

                    var $form = $('<form>', {
                        'method': 'POST',
                        'action': 'controller/exportData.php',
                        'target': '_blank'
                    });

                    // Add export type
                    $form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'exportType',
                        'value': 'pdf'
                    }));

                    // Add all filters to form
                    $.each(filters, function (key, value) {
                        $form.append($('<input>', {
                            'type': 'hidden',
                            'name': key,
                            'value': value
                        }));
                    });

                    // Submit the form
                    $('body').append($form);
                    $form.submit();
                    $form.remove();
                }
                , attr: {
                    'title': 'Limitat la un număr de 200 pagini. Folosiți filtrele pentru restrângerea rezultatelor returnate',
                    'data-bs-toggle': 'tooltip',
                    'data-bs-html': 'true',
                    'data-bs-placement': 'bottom'
                }
            }],
        "initComplete": function () {
            $(document).find('[data-bs-toggle="tooltip"]').tooltip();
            var table = this.api();

            function addDropdownFilter(column, url, placeholderClass, placeholderText) {
                var select = $('<select class="form-control form-control-sm select2 ' + placeholderClass + '">' +
                    '<option value="">' + placeholderText + '</option></select>')
                    .appendTo($(column.header()).empty())
                    .on('change', function () {
                        var val = $.fn.dataTable.util.escapeRegex($(this).val());
                        column.search(val ? '^' + val + '$' : '', true, false).draw();
                    });

                // Mark as loading to prevent multiple AJAX calls
                select.attr('data-loading', 'true');

                var postForm = new FormData();
                postForm.append('selectFiltru', 1);

                $.ajax({
                    url: url,
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: postForm,
                    type: 'POST',
                    dataType: 'json',
                    success: function (response) {
                        $.each(response.data, function (i, item) {
                            select.append('<option value="' + item.id + '">' + item.nume + '</option>');
                        });
                    },
                    error: function (xhr, status, error) {
                        console.error("Error loading filters:", error);
                    }
                });
            }

            function addSpecializariFilter(column, url, placeholderClass, placeholderText) {
                // Check if filter already exists to prevent duplicates
                var existingSelect = $(column.header()).find('select.' + placeholderClass);
                if (existingSelect.length > 0) {
                    console.log('Filter already exists for:', placeholderClass);
                    // Destroy existing Select2 to prevent conflicts
                    if (existingSelect.hasClass('select2-hidden-accessible')) {
                        existingSelect.select2('destroy');
                    }
                    existingSelect.remove();
                }

                // Clear the header completely and add a unique identifier
                $(column.header()).empty().attr('data-filter-loaded', 'true');

                var select = $('<select class="form-control form-control-sm select2 ' + placeholderClass + '">' +
                    '<option value="">' + placeholderText + '</option></select>')
                    .appendTo($(column.header()))
                    .on('change', function () {
                        var val = $.fn.dataTable.util.escapeRegex($(this).val());
                        column.search(val ? '^' + val + '$' : '', true, false).draw();
                    });

                var postForm = new FormData();
                postForm.append('selectFiltru', 1);

                $.ajax({
                    url: url,
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: postForm,
                    type: 'POST',
                    dataType: 'json',
                    success: function (response) {
                        console.log('Loading specializations for filter:', placeholderClass);
                        console.log('Response data length:', response.data.length);

                        // Clear existing options except the placeholder
                        select.find('option:not(:first)').remove();
                        select.find('optgroup').remove();

                        // Group the specializations and subspecializations
                        var specializari = {};
                        var allSubspecIds = []; // Track all subspecialization IDs globally

                        $.each(response.data, function (i, item) {
                            var id_specializare = item.id_specializare;
                            var nume_specializare = item.nume_specializare;
                            var id_subspecializare = item.id_subspecializare;
                            var nume_subspecializare = item.nume_subspecializare;

                            if (!specializari[id_specializare]) {
                                specializari[id_specializare] = {
                                    nume: nume_specializare,
                                    subspecializari: []
                                };
                            }

                            if (id_subspecializare && id_subspecializare !== '0') {
                                // Check if subspecialization already exists globally to prevent duplicates
                                if (allSubspecIds.indexOf(id_subspecializare) === -1) {
                                    allSubspecIds.push(id_subspecializare);
                                    specializari[id_specializare].subspecializari.push({
                                        id: id_subspecializare,
                                        nume: nume_subspecializare
                                    });
                                }
                            }
                        });

                        // Build the select options with optgroups
                        $.each(specializari, function (id_specializare, spec) {
                            if (spec.subspecializari.length === 0) {
                                // If no subspecializations, add the specialization as a direct option
                                select.append('<option value="' + id_specializare + '">' + spec.nume + '</option>');
                            } else {
                                // Create optgroup for specialization
                                var optgroup = $('<optgroup label="' + spec.nume + '" id="' + id_specializare + '"></optgroup>');

                                // Add subspecializations as options in the optgroup
                                $.each(spec.subspecializari, function (i, sub) {
                                    optgroup.append('<option value="' + sub.id + '">' + sub.nume + '</option>');
                                });

                                select.append(optgroup);
                            }
                        });

                        // Remove loading flag and initialize Select2
                        select.removeAttr('data-loading');
                        setTimeout(function() {
                            select.select2({
                                width: '100%',
                                placeholder: placeholderText,
                                allowClear: true,
                                language: {
                                    searching: function() {
                                        return "Căutare...";
                                    },
                                    noResults: function() {
                                        return "Nu s-au găsit rezultate";
                                    }
                                }
                            });
                        }, 100);
                    },
                    error: function (xhr, status, error) {
                        console.error("Error loading specializations:", error);
                    }
                });
            }


            table.column(1).every(function () {
                addDropdownFilter(this, 'controller/getJudete.php', 'NJudete', 'Toate județele');
            });

// Replace the specialization filter with the new function
            table.column(5).every(function () {
                addSpecializariFilter(this, 'controller/getSpecializari.php', 'NSpecializari', 'Toate specializările');
            });


            setTimeout(() => {
                $('.NJudete').select2({width: '100%'});
                // NSpecializari is initialized in addSpecializariFilter function
            }, 0);

            $('#tabelExperti thead').on('click', 'th', function (e) {
                if ($(e.target).is('select') || $(e.target).closest('.select2').length) {
                    e.stopImmediatePropagation();
                }
            });

            $('#tabelExperti thead th').each(function () {
                if ($(this).find('select').length > 0) {
                    $(this).removeClass('sorting sorting_asc sorting_desc').off('click');
                }
            });
        },
        "language": {
            "processing": "Procesare...",
            "search": "Caută:",
            "lengthMenu": "Afișează _MENU_ înregistrări pe pagină",
            "info": "Afișate de la _START_ la _END_ din _TOTAL_ înregistrări",
            "infoEmpty": "Afișate de la 0 la 0 din 0 înregistrări",
            "infoFiltered": "(filtrate din _MAX_ înregistrări totale)",
            "loadingRecords": "Se încarcă...",
            "zeroRecords": "Nu au fost găsite înregistrări",
            "emptyTable": "Nu există date în tabel",
            "paginate": {
                "first": '<i class="fas fa-angle-double-left"></i>',
                "previous": '<i class="fas fa-angle-left"></i>',
                "next": '<i class="fas fa-angle-right"></i>',
                "last": '<i class="fas fa-angle-double-right"></i>'
            }
        }
    });

    function showLoadingSwal() {
        let swalInstance = Swal.fire({
            title: "Se încarcă datele...",
            html: "Un moment, lucrăm la asta.",
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        return swalInstance;
    }

    $('#tabelExperti').on('preXhr.dt', function () {
        let swalLoading = showLoadingSwal();

        table.one('xhr.dt', function () {
            swalLoading.close();
        });
    });


    // Initialize modal DataTable when modal is shown
    $('#expertiSuspendati').on('shown.bs.modal', function () {
        // Check if DataTable already exists and destroy it
        if ($.fn.DataTable.isDataTable('#tabelExpertiSuspendati')) {
            $('#tabelExpertiSuspendati').DataTable().destroy();
        }

        // Initialize the DataTable for suspended experts
        var suspendedTable = $('#tabelExpertiSuspendati').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "controller/_cVizualizareSuspendati.php",
                "type": "POST",
                "dataSrc": "data",
                "error": function (xhr, error, thrown) {
                    console.error('DataTable Error:', error, xhr.responseText);
                }
            },
            "lengthMenu": [[30, 50, 100], [30, 50, 100]],
            "pageLength": 30,
            "columns": [
                {"data": "expert"},
                {"data": "judet"},
                {"data": "adresa"},
                {"data": "telefon"},
                {"data": "specializare"}
            ],
            "orderCellsTop": true,
            // "dom": "<'row'<'col-sm-12 col-md-4'l><'col-sm-12 col-md-4'B><'col-sm-12 col-md-4'f>>" +
            //     "<'row'<'col-sm-12'tr>>" +
            //     "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            // "buttons": [
            //     {
            //         text: 'Reset Filters',
            //         className: 'btn btn-outline-danger',
            //         action: function () {
            //             suspendedTable.search('').columns().search('').draw();
            //             suspendedTable.page.len(50).draw();
            //             $('#tabelExpertiSuspendati .select2').val('').trigger('change');
            //         }
            //     },
            //     {
            //         text: 'Export Excel',
            //         className: 'btn btn-outline-success',
            //         action: function (e, dt, button, config) {
            //             // Get the selected specializare option and its optgroup for suspended table
            //             const specializareSelect = $('#tabelExpertiSuspendati .NSpecializari');
            //             const selectedOption = specializareSelect.find('option:selected');
            //             const optgroup = selectedOption.parent('optgroup');
            //
            //             // Get current sorting information
            //             const order = suspendedTable.order();
            //             const sortColumn = suspendedTable.settings().init().columns[order[0][0]].data;
            //             const sortDirection = order[0][1];
            //
            //             var filters = {
            //                 search: suspendedTable.search(),
            //                 judet: $('#tabelExpertiSuspendati .NJudete').val(),
            //                 specializare: optgroup.length ?
            //                     optgroup.attr('id') + ',' + specializareSelect.val() :
            //                     specializareSelect.val(),
            //                 length: suspendedTable.page.len(),
            //                 start: suspendedTable.page() * suspendedTable.page.len(),
            //                 orderColumn: sortColumn,
            //                 orderDir: sortDirection,
            //                 exportSuspended: true // Flag to indicate this is for suspended experts
            //             };
            //
            //             // Create form for POST submission
            //             var $form = $('<form>', {
            //                 'method': 'POST',
            //                 'action': 'controller/exportData.php',
            //                 'target': '_blank'
            //             });
            //
            //             // Add export type
            //             $form.append($('<input>', {
            //                 'type': 'hidden',
            //                 'name': 'exportType',
            //                 'value': 'excel'
            //             }));
            //
            //             // Add all filters to form
            //             $.each(filters, function (key, value) {
            //                 $form.append($('<input>', {
            //                     'type': 'hidden',
            //                     'name': key,
            //                     'value': value
            //                 }));
            //             });
            //
            //             // Submit the form
            //             $('body').append($form);
            //             $form.submit();
            //             $form.remove();
            //         }
            //     },
            //     {
            //         text: 'Export PDF',
            //         className: 'btn btn-outline-success',
            //         action: function (e, dt, button, config) {
            //             // Get the selected specializare option and its optgroup for suspended table
            //             const specializareSelect = $('#tabelExpertiSuspendati .NSpecializari');
            //             const selectedOption = specializareSelect.find('option:selected');
            //             const optgroup = selectedOption.parent('optgroup');
            //
            //             // Get current sorting information
            //             const order = suspendedTable.order();
            //             const sortColumn = suspendedTable.settings().init().columns[order[0][0]].data;
            //             const sortDirection = order[0][1];
            //
            //             var filters = {
            //                 search: suspendedTable.search(),
            //                 judet: $('#tabelExpertiSuspendati .NJudete').val(),
            //                 specializare: optgroup.length ?
            //                     optgroup.attr('id') + ',' + specializareSelect.val() :
            //                     specializareSelect.val(),
            //                 length: suspendedTable.page.len(),
            //                 start: suspendedTable.page() * suspendedTable.page.len(),
            //                 orderColumn: sortColumn,
            //                 orderDir: sortDirection,
            //                 exportSuspended: true // Flag to indicate this is for suspended experts
            //             };
            //
            //             var $form = $('<form>', {
            //                 'method': 'POST',
            //                 'action': 'controller/exportData.php',
            //                 'target': '_blank'
            //             });
            //
            //             // Add export type
            //             $form.append($('<input>', {
            //                 'type': 'hidden',
            //                 'name': 'exportType',
            //                 'value': 'pdf'
            //             }));
            //
            //             // Add all filters to form
            //             $.each(filters, function (key, value) {
            //                 $form.append($('<input>', {
            //                     'type': 'hidden',
            //                     'name': key,
            //                     'value': value
            //                 }));
            //             });
            //
            //             // Submit the form
            //             $('body').append($form);
            //             $form.submit();
            //             $form.remove();
            //         },
            //         attr: {
            //             'title': 'Limitat la un număr de 200 pagini. Folosiți filtrele pentru restrângerea rezultatelor returnate',
            //             'data-bs-toggle': 'tooltip',
            //             'data-bs-html': 'true',
            //             'data-bs-placement': 'bottom'
            //         }
            //     }],
            "initComplete": function () {
                $(document).find('[data-bs-toggle="tooltip"]').tooltip();
                var suspendedTableApi = this.api();

                function addDropdownFilter(column, url, placeholderClass, placeholderText) {
                    var select = $('<select class="form-control form-control-sm select2 ' + placeholderClass + '">' +
                        '<option value="">' + placeholderText + '</option></select>')
                        .appendTo($(column.header()).empty())
                        .on('change', function () {
                            var val = $.fn.dataTable.util.escapeRegex($(this).val());
                            column.search(val ? '^' + val + '$' : '', true, false).draw();
                        });

                    var postForm = new FormData();
                    postForm.append('selectFiltru', 1);

                    $.ajax({
                        url: url,
                        cache: false,
                        contentType: false,
                        processData: false,
                        data: postForm,
                        type: 'POST',
                        dataType: 'json',
                        success: function (response) {
                            $.each(response.data, function (i, item) {
                                select.append('<option value="' + item.id + '">' + item.nume + '</option>');
                            });
                        },
                        error: function (xhr, status, error) {
                            console.error("Error loading filters:", error);
                        }
                    });
                }

                function addSpecializariFilter(column, url, placeholderClass, placeholderText) {
                    // Check if filter already exists to prevent duplicates
                    var existingSelect = $(column.header()).find('select.' + placeholderClass);
                    if (existingSelect.length > 0) {
                        console.log('Filter already exists for suspended table:', placeholderClass);
                        // Destroy existing Select2 to prevent conflicts
                        if (existingSelect.hasClass('select2-hidden-accessible')) {
                            existingSelect.select2('destroy');
                        }
                        existingSelect.remove();
                    }

                    var select = $('<select class="form-control form-control-sm select2 ' + placeholderClass + '">' +
                        '<option value="">' + placeholderText + '</option></select>')
                        .appendTo($(column.header()).empty())
                        .on('change', function () {
                            var val = $.fn.dataTable.util.escapeRegex($(this).val());
                            column.search(val ? '^' + val + '$' : '', true, false).draw();
                        });

                    // Mark as loading to prevent multiple AJAX calls
                    select.attr('data-loading', 'true');

                    var postForm = new FormData();
                    postForm.append('selectFiltru', 1);

                    $.ajax({
                        url: url,
                        cache: false,
                        contentType: false,
                        processData: false,
                        data: postForm,
                        type: 'POST',
                        dataType: 'json',
                        success: function (response) {
                            console.log('Loading specializations for suspended table filter:', placeholderClass);
                            console.log('Response data length:', response.data.length);

                            // Clear existing options except the placeholder
                            select.find('option:not(:first)').remove();
                            select.find('optgroup').remove();

                            // Group the specializations and subspecializations
                            var specializari = {};
                            var allSubspecIds = []; // Track all subspecialization IDs globally

                            $.each(response.data, function (i, item) {
                                var id_specializare = item.id_specializare;
                                var nume_specializare = item.nume_specializare;
                                var id_subspecializare = item.id_subspecializare;
                                var nume_subspecializare = item.nume_subspecializare;

                                if (!specializari[id_specializare]) {
                                    specializari[id_specializare] = {
                                        nume: nume_specializare,
                                        subspecializari: []
                                    };
                                }

                                if (id_subspecializare && id_subspecializare !== '0') {
                                    // Check if subspecialization already exists globally to prevent duplicates
                                    if (allSubspecIds.indexOf(id_subspecializare) === -1) {
                                        allSubspecIds.push(id_subspecializare);
                                        specializari[id_specializare].subspecializari.push({
                                            id: id_subspecializare,
                                            nume: nume_subspecializare
                                        });
                                    }
                                }
                            });

                            // Build the select options with optgroups
                            $.each(specializari, function (id_specializare, spec) {
                                if (spec.subspecializari.length === 0) {
                                    // If no subspecializations, add the specialization as a direct option
                                    select.append('<option value="' + id_specializare + '">' + spec.nume + '</option>');
                                } else {
                                    // Create optgroup for specialization
                                    var optgroup = $('<optgroup label="' + spec.nume + '" id="' + id_specializare + '"></optgroup>');

                                    // Add subspecializations as options in the optgroup
                                    $.each(spec.subspecializari, function (i, sub) {
                                        optgroup.append('<option value="' + sub.id + '">' + sub.nume + '</option>');
                                    });

                                    select.append(optgroup);
                                }
                            });

                            // Remove loading flag and initialize Select2
                            select.removeAttr('data-loading');
                            setTimeout(function() {
                                select.select2({
                                    width: '100%',
                                    placeholder: placeholderText,
                                    allowClear: true,
                                    language: {
                                        searching: function() {
                                            return "Căutare...";
                                        },
                                        noResults: function() {
                                            return "Nu s-au găsit rezultate";
                                        }
                                    }
                                });
                            }, 100);
                        },
                        error: function (xhr, status, error) {
                            console.error("Error loading specializations:", error);
                        }
                    });
                }


                suspendedTableApi.column(1).every(function () {
                    addDropdownFilter(this, 'controller/getJudete.php', 'NJudete', 'Toate județele');
                });

                // Replace the specialization filter with the new function
                suspendedTableApi.column(5).every(function () {
                    addSpecializariFilter(this, 'controller/getSpecializari.php', 'NSpecializari', 'Toate specializările');
                });

                setTimeout(() => {
                    $('#tabelExpertiSuspendati .NJudete').select2({width: '100%'});
                    // NSpecializari is initialized in addSpecializariFilter function
                }, 0);

                $('#tabelExpertiSuspendati thead').on('click', 'th', function (e) {
                    if ($(e.target).is('select') || $(e.target).closest('.select2').length) {
                        e.stopImmediatePropagation();
                    }
                });

                $('#tabelExpertiSuspendati thead th').each(function () {
                    if ($(this).find('select').length > 0) {
                        $(this).removeClass('sorting sorting_asc sorting_desc').off('click');
                    }
                });
            },
            "language": {
                "processing": "Procesare...",
                "search": "Caută:",
                "lengthMenu": "Afișează _MENU_ înregistrări pe pagină",
                "info": "Afișate de la _START_ la _END_ din _TOTAL_ înregistrări",
                "infoEmpty": "Afișate de la 0 la 0 din 0 înregistrări",
                "infoFiltered": "(filtrate din _MAX_ înregistrări totale)",
                "loadingRecords": "Se încarcă...",
                "zeroRecords": "Nu au fost găsite înregistrări",
                "emptyTable": "Nu există date în tabel",
                "paginate": {
                    "first": '<i class="fas fa-angle-double-left"></i>',
                    "previous": '<i class="fas fa-angle-left"></i>',
                    "next": '<i class="fas fa-angle-right"></i>',
                    "last": '<i class="fas fa-angle-double-right"></i>'
                }
            }
        });
    });

});
