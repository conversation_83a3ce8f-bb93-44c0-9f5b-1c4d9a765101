const Toast = Swal.mixin({
    toast: true,
    position: "top-end",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        hideTooltips();
        toast.onmouseenter = Swal.stopTimer;
        toast.onmouseleave = Swal.resumeTimer;
    },
    didClose: () => {
        hideTooltips();
    }
});

function hideTooltips() {
    $('[data-bs-toggle="tooltip"]').each(function () {
        try {
            const tooltip = bootstrap.Tooltip.getInstance(this);
            if (tooltip) {
                tooltip.hide();
            }
        } catch (e) {
            console.warn('Error hiding tooltip:', e);
        }
    });
}

$(document).ready(function () {
    $(document).on('click', function (e) {
        hideTooltips();
    });

    $(document).on('mouseleave', '[data-bs-toggle="tooltip"]', function () {
        try {
            const tooltip = bootstrap.Tooltip.getInstance(this);
            if (tooltip) {
                tooltip.hide();
            }
        } catch (e) {
            console.warn('Error on tooltip mouseleave:', e);
        }
    });

    $(document).off('click', '#btnExpAleatoriu').on('click', '#btnExpAleatoriu', function () {
        $('div.mesajeHandle').html('');
        $(document).find('.afisare4').hide();
        eid = $(document).find('#expertizaId').val();

        $.ajax({
            url: 'controller/desemnare.php',
            type: 'POST',
            data: {
                inlocuireExpertInComisie: true,
                inlocuireRandomExpert: true,
                eid: eid,
            },
            success: function (response) {
                if (response.status === 'error') {
                    Swal.fire({
                        title: "Eroare",
                        html: response.message,
                        icon: "error"
                    });
                    return;
                } else if (response.status === 'ok') {
                    $(document).find('.afisare3').closest('.row').find('div.col-lg-2 p.afisare3 i').addClass('fa').removeClass('far');

                    let timerInterval;
                    Swal.fire({
                        icon: "success",
                        title: 'A fost desemnat <b>aleatoriu</b> expertul. Fisierul PDF a fost generat.',
                        timer: 2000,
                        timerProgressBar: true,
                        willClose: () => {
                            clearInterval(timerInterval);
                        }
                    }).then(() => {
                        $(document).find('.afisare4').hide();
                        $(document).find('div.afisare4').css('display', 'block').html(response.message);
                        $(document).find('#btnExpAleatoriu, #btnExpPropus').remove();
                    });

                }
            },
            error: function (response) {
                Swal.fire({
                    title: "Eroare generare PDF",
                    html: response.message,
                    icon: "error"
                });
            }
        });

    });

    $(document).off('click', '#btnExpPropus').on('click', '#btnExpPropus', function () {
        $('div.mesajeHandle').html('');
        const eid = new URLSearchParams(window.location.search).get('eid');
        Swal.fire({
            title: "CNP expert",
            input: "text",
            inputAttributes: {
                autocapitalize: "off"
            },
            showCancelButton: true,
            confirmButtonText: "Căutare",
            showLoaderOnConfirm: true,
            preConfirm: (cnpInput) => {
                return new Promise((resolve, reject) => {
                    const formData = new FormData();
                    formData.append('inlocuireExpertInComisie', true);
                    formData.append('inlocuireConvenitExpert', true);
                    formData.append('cnp', cnpInput);
                    formData.append('eid', eid);
                    $.ajax({
                        url: 'controller/desemnare.php',
                        type: 'POST',
                        data: formData,
                        processData: false,   // required for FormData
                        contentType: false,    // required for FormData
                        dataType: 'json',      // expecting JSON response
                        success: function (data) {
                            if(data.status === 'ok') {
                                let timerInterval;
                                Swal.fire({
                                    icon: "success",
                                    title: 'A fost desemnat expertul <b>convenit</b>. Fisierul PDF a fost generat.',
                                    timer: 2000,
                                    timerProgressBar: true,
                                    willClose: () => {
                                        clearInterval(timerInterval);
                                    }
                                }).then(() => {
                                    // Update the display
                                    $('div.afisare4').hide().css('display', 'block').html(data.message);
                                    $('#btnExpAleatoriu, #btnExpPropus').remove();
                                });
                                resolve(true);
                            }else {
                                Swal.fire({
                                    title: "Eroare",
                                    html: data.message,
                                    icon: "error"
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Ajax error:', { status: status, error: error, xhr: xhr });
                            reject(`Request failed: ${error}`);
                        }
                    });
                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        });
    });

});

