let table;

function loadInstante(identificator) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: 'controller/getInstante.php',
            type: 'POST',
            dataType: 'json',
            success: function (response) {
                var select = $(identificator);
                select.empty().append('<option value="0">Selectează instanța</option>');
                $.each(response.data, function (i, item) {
                    select.append($('<option>', {
                        value: item.id,
                        text: item.den
                    }));
                });
                select.append('<option value="00">Neasignat</option>');
                resolve();
            },
            error: function (xhr, status, error) {
                console.error('Eroare la încărcarea instanțelor:', error);
                reject(error);
            }
        });
    });
}

function loadRoluri(identificator) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: 'controller/getRoluri.php',
            type: 'POST',
            dataType: 'json',
            success: function (response) {
                var select = $(identificator);
                select.empty().append('<option value="">Selectează rolul</option>');
                $.each(response.data, function (i, item) {
                    select.append($('<option>', {
                        value: item.id,
                        text: item.tip
                    }));
                });
                resolve();
            },
            error: function (xhr, status, error) {
                console.error('Eroare la încărcarea rolurilor:', error);
                reject(error);
            }
        });
    });
}

function loadSectii(selectElement, idInstanta, selectedSectieId = null) {
    if (!idInstanta) return;

    return $.ajax({
        url: 'controller/administrareSectii.php',
        type: 'POST',
        data: {
            action: 'getSectiiActive',
            idInstanta: idInstanta
        },
        dataType: 'json',
        success: function (response) {
            let select = $(selectElement);
            select.empty().append('<option value="">Selectează secția</option>');

            if (response.success && response.data) {
                response.data.forEach(function (sectie) {
                    select.append(`<option value="${sectie.id}">${sectie.denumire}</option>`);
                });

                if (selectedSectieId && response.data.some(sectie => sectie.id == selectedSectieId)) {
                    select.val(selectedSectieId).trigger('change');
                } else if (response.data.length === 1) {
                    select.val(response.data[0].id).trigger('change');
                }
            }
        },
        error: function () {
            console.error('Eroare la încărcarea secțiilor');
        }
    });
}

function deactivateUser(userId) {
    $.ajax({
        url: 'controller/administrareUseri.php',
        type: 'POST',
        data: {
            action: 'deactivateUser',
            userId: userId
        },
        dataType: 'json',
        success: function (response) {
            if (response.status === 'success') {
                Swal.fire({
                    icon: 'success',
                    title: 'Succes!',
                    text: 'Contul a fost dezactivat cu succes.'
                }).then(() => {
                    table.ajax.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: response.message
                });
            }
        },
        error: function () {
            Swal.fire({
                icon: 'error',
                title: 'Eroare!',
                text: 'A apărut o eroare la comunicarea cu serverul.'
            });
        }
    });
}

function activateUser(userId) {
    $.ajax({
        url: 'controller/administrareUseri.php',
        type: 'POST',
        data: {
            action: 'activateUser',
            userId: userId
        },
        dataType: 'json',
        success: function (response) {
            if (response.status === 'success') {
                Swal.fire({
                    icon: 'success',
                    title: 'Succes!',
                    text: 'Contul a fost activat cu succes.'
                }).then(() => {
                    if (table && table.ajax) {
                        table.ajax.reload();
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: response.message
                });
            }
        },
        error: function () {
            Swal.fire({
                icon: 'error',
                title: 'Eroare!',
                text: 'A apărut o eroare la comunicarea cu serverul.'
            });
        }
    });
}

function showLoadingSwal() {
    let swalInstance = Swal.fire({
        title: "Se încarcă datele...",
        html: "Un moment, lucrăm la asta.",
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    return swalInstance;
}

function loadJudete()
{
    console.log('loadJudete() a fost apelată'); // test vizual
    return new Promise((resolve, reject) =>
    {
        $.ajax({
            url: 'controller/getJudete2.php',
            type: 'POST',
            dataType: 'json',
            success: function (response) {
                var select = $('#judetSelect');
                select.empty().append('<option value="">Selectează Județ</option>');
                $.each(response.data, function (i, item) {
                    select.append($('<option>', {
                        value: item.idJudet,
                        text: item.numeJudet
                    }));
                });
                resolve();
            },
            error: function (xhr, status, error) {
                console.error('Eroare la încărcarea judetelor:', error);
                reject(error);
            }
        });
    });
}


function loadUnassignedUsers() {
    // Check if DataTable already exists and destroy it to prevent the warning
    if ($.fn.DataTable.isDataTable('#tabelUnasignedUsers')) {
        $('#tabelUnasignedUsers').DataTable().destroy();
    }

    // Initialize the DataTable for unassigned users
    $('#tabelUnasignedUsers').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "controller/administrareUseri.php",
            "type": "POST",
            "data": {
                "action": "getUnassignedUsers"
            },
            "dataSrc": "data",
            "error": function (xhr, error, thrown) {
                console.error('DataTable Error:', error, xhr.responseText);
            }
        },
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "pageLength": 10,
        "columns": [
            {"data": "utilizator"},
            {"data": "actiuni"}
        ],
        "language": {
            "emptyTable": "Nu există utilizatori neasignați",
            "info": "Afișare _START_ până la _END_ din _TOTAL_ utilizatori",
            "infoEmpty": "Afișare 0 până la 0 din 0 utilizatori",
            "infoFiltered": "(filtrat din _MAX_ utilizatori totali)",
            "lengthMenu": "Afișează _MENU_ utilizatori",
            "loadingRecords": "Se încarcă...",
            "processing": "Se procesează...",
            "search": "Caută:",
            "zeroRecords": "Nu s-au găsit utilizatori potriviți",
            "paginate": {
                "first": "Prima",
                "last": "Ultima",
                "next": "Următoarea",
                "previous": "Precedenta"
            }
        }
    });
}

function assignUser(userId) {
    $.ajax({
        url: 'controller/administrareUseri.php',
        type: 'POST',
        data: {
            action: 'assignUser',
            userId: userId
        },
        dataType: 'json',
        success: function (response) {
            if (response.status === 'success') {
                Swal.fire({
                    icon: 'success',
                    title: 'Succes!',
                    text: 'Utilizatorul a fost asignat cu succes.'
                }).then(() => {
                    // Reload the unassigned users table
                    $('#tabelUnasignedUsers').DataTable().ajax.reload();
                    // Also reload the main users table if it exists
                    if (table && table.ajax) {
                        table.ajax.reload();
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: response.message || 'A apărut o eroare la asignarea utilizatorului.'
                });
            }
        },
        error: function () {
            Swal.fire({
                icon: 'error',
                title: 'Eroare!',
                text: 'A apărut o eroare la comunicarea cu serverul.'
            });
        }
    });
}

// The loadUnassignedUsers function is implemented above

function assignUser(userId) {
    $.ajax({
        url: 'controller/administrareUseri.php',
        type: 'POST',
        data: {
            action: 'assignUser',
            userId: userId
        },
        dataType: 'json',
        success: function (response) {
            if (response.status === 'success') {
                Swal.fire({
                    icon: 'success',
                    title: 'Succes!',
                    text: 'Contul a fost asignat cu succes.'
                }).then(() => {
                    $('#manageUnasignedUsersModal').modal('hide');
                    table.ajax.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: response.message
                });
            }
        },
        error: function () {
            Swal.fire({
                icon: 'error',
                title: 'Eroare!',
                text: 'A apărut o eroare la comunicarea cu serverul.'
            });
        }
    });
}

$(document).ready(function () {

    table = $('#tabelExperti').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "controller/administrare.php",
            "type": "POST",
            "dataSrc": "data",
            "error": function (xhr, error, thrown) {
                console.error('DataTable Error:', error, xhr.responseText);
            }
        },
        "lengthMenu": [[50, 100, 250, 500], [50, 100, 250, 500]],
        "pageLength": 50,
        "columns": [
            {"data": "user"},
            {"data": "instanta"},
            {"data": "rol"},
            {"data": "actiuni"}
        ],
        "orderCellsTop": true,
        "dom": "<'row'<'col-sm-12 col-md-4'l><'col-sm-12 col-md-4'B><'col-sm-12 col-md-4'f>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                text: 'Reset Filters',
                className: 'btn btn-outline-danger',
                action: function () {
                    table.search('').columns().search('').draw();
                    table.page.len(10).draw();
                    $('.select2').val('').trigger('change');
                }
            },
        ],
        "initComplete": function () {
            $(document).find('[data-bs-toggle="tooltip"]').tooltip();
            var table = this.api();

            function addDropdownFilter(column, url, placeholderClass, placeholderText) {
                var select = $('<select class="form-control form-control-sm select2 ' + placeholderClass + '">' +
                    '<option value="">' + placeholderText + '</option></select>')
                    .appendTo($(column.header()).empty())
                    .on('change', function () {
                        var val = $.fn.dataTable.util.escapeRegex($(this).val());
                        column.search(val ? '^' + val + '$' : '', true, false).draw();
                    });

                var postForm = new FormData();
                postForm.append('selectFiltru', 1);

                $.ajax({
                    url: url,
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: postForm,
                    type: 'POST',
                    dataType: 'json',
                    success: function (response) {
                        $.each(response.data, function (i, item) {
                            select.append('<option value="' + item.id + '">' + item.den + '</option>');
                        });
                        select.append('<option value="00">Neasignat</option>');
                    },
                    error: function (xhr, status, error) {
                        console.error("Error loading filters:", error);
                    }
                });
            }

            table.column(1).every(function () {
                addDropdownFilter(this, 'controller/getInstante.php', 'z_instante', 'Toate instanțele');
            });

            setTimeout(() => {
                $('.z_instante').select2({width: '100%'});
            }, 0);

            $('#tabelExperti thead').on('click', 'th', function (e) {
                if ($(e.target).is('select') || $(e.target).closest('.select2').length) {
                    e.stopImmediatePropagation();
                }
            });

            $('#tabelExperti thead th').each(function () {
                if ($(this).find('select').length > 0) {
                    $(this).removeClass('sorting sorting_asc sorting_desc').off('click');
                }
            });
        }
    });


    $('<style>')
        .text(`
            .text-muted s {
                opacity: 0.6;
            }
        `)
        .appendTo('head');


    $('#tabelExperti').on('init.dt', function () {
        var searchDelay = null;
        $('#tabelExperti_filter input').off().on('input', function () {
            var self = this;
            clearTimeout(searchDelay);
            searchDelay = setTimeout(function () {
                table.search($(self).val()).draw();
            }, 1000);
        });
    });

    $('#tabelExperti').on('preXhr.dt', function () {
        let swalLoading = showLoadingSwal();
        table.one('xhr.dt', function () {
            swalLoading.close();
        });
    });


    let exportFormatter = {
        format: {
            body: function (data, row, column, node) {
                var tempElement = document.createElement('div');
                tempElement.innerHTML = data;
                var divToRemove = tempElement.querySelector('.removeElement');
                if (divToRemove) {
                    divToRemove.parentNode.removeChild(divToRemove);
                }
                var divToRemove = tempElement.querySelector('select');
                if (divToRemove) {
                    divToRemove.parentNode.removeChild(divToRemove);
                }
                var divToRemove = tempElement.querySelector('input');
                if (divToRemove) {
                    divToRemove.parentNode.removeChild(divToRemove);
                }
                data = tempElement.innerHTML.replace(/<[^>]+>/g, '');
                return data;
            }
        }
    };


    $('#tabelExperti').on('preXhr.dt', function () {
        let swalLoading = showLoadingSwal();

        table.one('xhr.dt', function () {
            swalLoading.close();
        });
    });


    $('#idInstanta, #idRol').select2({
        width: '100%',
        dropdownParent: $('#addUserModal')
    });


    $('#addUserModal').on('show.bs.modal', function () {
        loadInstante('#idInstanta');
        loadRoluri('#idRol');
        loadSectii('#idSectie');
        $('#idSectie').select2({
            width: '100%',
            dropdownParent: $('#addUserModal')
        });
    });


    $(document).off('change', '#idRol').on('change', '#idRol', function () {
        var selectedRol = $(this).val();
        if (selectedRol === '5') {
            $('#idSectie').closest('div.mb-3').hide();
        } else {
            $('#idSectie').closest('div.mb-3').show();
        }
    });

    $('#saveUser').on('click', function () {
        var formData = {
            action: 'addUser',
            utilizator: $('#utilizator').val(),
            idInstanta: $('#idInstanta').val(),
            idSectie: $('#idSectie').val(),
            idRol: $('#idRol').val()
        };

        $.ajax({
            url: 'controller/administrareUseri.php',
            type: 'POST',
            data: formData,
            dataType: 'json', // Add this to ensure proper JSON parsing
            success: function (response) {
                try {
                    // If response is a string, try to parse it
                    if (typeof response === 'string') {
                        response = JSON.parse(response);
                    }

                    if (response.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Succes!',
                            html: 'Utilizatorul a fost adăugat cu succes.'
                        }).then(() => {
                            $('#addUserModal').modal('hide');
                            $('#addUserForm')[0].reset();
                            table.ajax.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Eroare!',
                            html: response.message // Use the actual error message from the server
                        });
                    }
                } catch (e) {
                    console.error('JSON parsing error:', e);
                    Swal.fire({
                        icon: 'error',
                        title: 'Eroare!',
                        text: 'A apărut o eroare la procesarea răspunsului.'
                    });
                }
            },
            error: function (xhr, status, error) {
                console.error('AJAX error:', status, error);
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: 'A apărut o eroare la comunicarea cu serverul.'
                });
            }
        });
    });

    let currentEditingInstantaId = null;

    $(document).on('click', '.btn-edit-user', function () {
        const userId = $(this).data('user-id');
        const userData = $(this).data('user-info');

        currentEditingInstantaId = userData.id_instanta;
        $('#editIdInstanta, #editIdRol, #editIdSectie').select2({
            width: '100%',
            dropdownParent: $('#editUserModal')
        });

        $('#editUserModal').modal('show');

        Promise.all([
            loadInstante('#editIdInstanta'),
            loadRoluri('#editIdRol')
        ]).then(() => {
            $('#editIdInstanta').val(userData.id_instanta).trigger('change');
            $('#editUtilizator').val(userData.utilizator);
            $('#editUserId').val(userId);
            $('#editIdRol').val(userData.id_rol).trigger('change');

            loadSectii('#editIdSectie', userData.id_instanta, userData.id_sectie);
        });
    });

    $(document).on('click', '.btn-inactivate-user', function () {
        const userId = $(this).data('user-id');
        const username = $(this).data('username');

        Swal.fire({
            title: `Sunteți sigur că doriți să inactivați contul utilizatorului <strong>${username}</strong>?`,
            html: 'Atenție!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Da, inactivează!',
            cancelButtonText: 'Anulează',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                deactivateUser(userId);
            }
        });
    });

    $('#saveEditUser').on('click', function () {
        const formData = {
            action: 'editUser',
            userId: $('#editUserId').val(),
            utilizator: $('#editUtilizator').val(),
            idInstanta: $('#editIdInstanta').val(),
            idSectie: $('#editIdSectie').val(),
            idRol: $('#editIdRol').val()
        };

        $.ajax({
            url: 'controller/administrareUseri.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function (response) {
                if (response.status === 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: 'Succes!',
                        text: 'Informațiile au fost actualizate cu succes.'
                    }).then(() => {
                        $('#editUserModal').modal('hide');
                        table.ajax.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Eroare!',
                        text: response.message
                    });
                }
            },
            error: function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: 'A apărut o eroare la comunicarea cu serverul.'
                });
            }
        });
    });

    $(document).off('click', '.btn-asignate-user').on('click', '.btn-asignate-user', function () {
        const userId = $(this).data('user-id');
        const username = $(this).data('username');

        Swal.fire({
            title: 'Confirmare activare cont',
            html: `Sunteți sigur că doriți să activați userul <strong>${username}</strong>?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Da, activează',
            cancelButtonText: 'Anulează',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                activateUser(userId);
            }
        });
    });

    let tabelSectii = $('#tabelSectii').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "controller/administrareSectii.php",
            "type": "POST",
            "data": function(d) {
                d.action = 'getSectii';
                d.showInactive = $('#showInactiveSectii').is(':checked');
                d.instantaFilter = $('#tabelSectii thead tr:eq(1) .instantaFilter').val();
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "denumire"},
            {"data": "instanta"},
            {
                "data": "data_inactivare",
                "render": function(data, type, row) {
                    return data === null ?
                        '<span class="badge bg-success">Activ</span>' :
                        '<span class="badge bg-danger">Inactiv</span>';
                }
            },
            {
                "data": "actiuni",
                "orderable": false,
                "searchable": false
            }
        ],
        "orderCellsTop": true,
        "initComplete": function() {
            var api = this.api();
            var thead = $('#tabelSectii thead');

            // Adăugăm rândul pentru filtre
            thead.append('<tr class="filters">' +
                '<th></th>' +                    // ID
                '<th></th>' +                    // Denumire
                '<th>' +                         // Instanță
                    '<select class="form-control form-control-sm select2 instantaFilter">' +
                        '<option value="">Toate instanțele</option>' +
                    '</select>' +
                '</th>' +
                '<th></th>' +                    // Status
                '<th></th>' +                    // Acțiuni
            '</tr>');

            loadInstante('.instantaFilter');
            $('.instantaFilter').on('change', function() {
                api.ajax.reload();
            });

            thead.on('click', 'tr.filters th', function(e) {
                e.stopPropagation();
            });
        },
        "language": {
            "processing": "Procesare...",
            "search": "Caută:",
            "lengthMenu": "Afișează _MENU_ înregistrări pe pagină",
            "info": "Afișate de la _START_ la _END_ din _TOTAL_ înregistrări",
            "infoEmpty": "Afișate de la 0 la 0 din 0 înregistrări",
            "infoFiltered": "(filtrate din _MAX_ înregistrări totale)",
            "loadingRecords": "Se încarcă...",
            "zeroRecords": "Nu au fost găsite înregistrări",
            "emptyTable": "Nu există date în tabel",
            "paginate": {
                "first": '<i class="fas fa-angle-double-left"></i>',
                "previous": '<i class="fas fa-angle-left"></i>',
                "next": '<i class="fas fa-angle-right"></i>',
                "last": '<i class="fas fa-angle-double-right"></i>'
            }
        }
    });

    $('<style>')
        .prop('type', 'text/css')
        .html(`
            #tabelSectii thead tr.filters th {
                padding: 4px;
            }
            #tabelSectii thead .select2-container .select2-selection--single {
                height: 30px;
                padding: 2px;
            }
            #tabelSectii thead .select2-container--default .select2-selection--single .select2-selection__rendered {
                line-height: 24px;
            }
            #tabelSectii thead .select2-container--default .select2-selection--single .select2-selection__arrow {
                height: 28px;
            }
        `)
        .appendTo('head');


    $('#showInactiveSectii').on('change', function () {
        tabelSectii.ajax.reload();
    });

    $(document).on('click', '.btn-activate-sectie', function () {
        let sectieId = $(this).data('sectie-id');
        let sectieDenumire = $(this).data('sectie-denumire');

        Swal.fire({
            title: 'Confirmare activare secție',
            html: `Sunteți sigur că doriți să activați secția <strong>${sectieDenumire}</strong>?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Da, activează',
            cancelButtonText: 'Anulează',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: 'controller/administrareSectii.php',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'activateSectie',
                        id: sectieId
                    },
                    success: function (response) {
                        if (response.success === true) {
                            tabelSectii.ajax.reload();
                            Swal.fire(
                                'Activată!',
                                response.message,
                                'success'
                            );
                        } else {
                            Swal.fire(
                                'Eroare!',
                                response.message,
                                'error'
                            );
                        }
                    },
                    error: function () {
                        Swal.fire(
                            'Eroare!',
                            'A apărut o eroare la procesarea cererii.',
                            'error'
                        );
                    }
                });
            }
        });
    });

    $('#addSectieBtn').on('click', function () {
        $('#sectieId').val('');
        $('#sectieForm')[0].reset();
        $('#idInstantaSectie').prop('disabled', false);
        $('#sectieFormModalLabel').text('Adaugă secție');
        $('#sectieFormModal').modal('show');
        loadInstante('#idInstantaSectie');
    });

    $('#addSectieFromUser').on('click', function () {
        // Salvăm instanța selectată în modal
        const selectedInstanta = $('#editIdInstanta').val();

        // Resetăm formularul de secție
        $('#sectieId').val('');
        $('#sectieForm')[0].reset();

        // Deschidem modalul de secție
        $('#sectieFormModalLabel').text('Adaugă secție');
        $('#sectieFormModal').modal('show');

        // Încărcăm și setăm instanța
        loadInstante('#idInstantaSectie').then(() => {
            $('#idInstantaSectie')
                .val(selectedInstanta)
                .trigger('change')
                .prop('disabled', true);
        });
    });

    $('#saveSectie').on('click', function () {
        let formData = {
            action: $('#sectieId').val() ? 'updateSectie' : 'addSectie',
            id: $('#sectieId').val(),
            denumire: $('#denumireSectie').val(),
            idInstanta: $('#idInstantaSectie').val()
        };

        if (formData.action === 'updateSectie') {
            formData.originalInstanta = $('#idInstantaSectie').val();
        }

        $.ajax({
            url: 'controller/administrareSectii.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function (response) {
                if (response.success === true) {
                    $('#sectieFormModal').modal('hide');

                    // Reîncărcăm secțiile în modalul de editare utilizator
                    if ($('#editUserModal').is(':visible')) {
                        const selectedSectie = $('#editIdSectie').val();
                        loadSectii('#editIdSectie', currentEditingInstantaId, selectedSectie);
                    }

                    // Reîncărcăm tabelul de secții dacă există
                    if (typeof tabelSectii !== 'undefined') {
                        tabelSectii.ajax.reload();
                    }

                    Swal.fire({
                        icon: 'success',
                        title: 'Succes!',
                        text: response.message
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Eroare!',
                        text: response.message || 'A apărut o eroare neașteptată.'
                    });
                }
            },
            error: function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Eroare!',
                    text: 'A apărut o eroare la procesarea cererii.'
                });
            }
        });
    });

    $(document).on('click', '.btn-edit-sectie', function () {
        let data = $(this).data('sectie');
        $('#sectieId').val(data.id);
        $('#denumireSectie').val(data.denumire);
        loadInstante('#idInstantaSectie').then(() => {
            $('#idInstantaSectie')
                .val(data.idInstanta)
                .trigger('change')
                .prop('disabled', true);
        });

        $('#sectieFormModalLabel').text('Editare secție');
        $('#sectieFormModal').modal('show');
    });

    $(document).on('click', '.btn-deactivate-sectie', function () {
        let sectieId = $(this).data('sectie-id');
        let sectieDenumire = $(this).data('sectie-denumire');

        Swal.fire({
            title: 'Confirmare dezactivare secție',
            html: `Sunteți sigur că doriți să dezactivați secția <strong>${sectieDenumire}</strong>?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Da, dezactivează',
            cancelButtonText: 'Anulează',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: 'controller/administrareSectii.php',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'deactivateSectie',
                        id: sectieId
                    },
                    success: function (response) {
                        if (response.success === true) {  // Verificare strictă
                            tabelSectii.ajax.reload();
                            Swal.fire(
                                'Dezactivată!',
                                response.message,
                                'success'
                            );
                        } else {
                            Swal.fire(
                                'Eroare!',
                                response.message || 'A apărut o eroare neașteptată.',
                                'error'
                            );
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Eroare!',
                            text: 'A apărut o eroare la procesarea cererii.'
                        });
                    }
                });
            }
        });
    });

    $('#idInstanta').on('change', function () {
        loadSectii('#idSectie', $(this).val());
    });

    $('#editIdInstanta').on('change', function () {
        loadSectii('#editIdSectie', $(this).val());
    });





    let manageBLETWasOpen = false;

    $('#manageBLET').on('shown.bs.modal', function () {
        console.log("Modal deschis - trimit AJAX");
        $.ajax({
            url: 'controller/administrareBLET.php',
            type: 'POST',
            data: { action: 'getBletEmails' },
            success: function (raspuns) {
                try {
                    const date = JSON.parse(raspuns);
                    $('#tabelEmailBlet tbody').empty();
                    if (date.success && Array.isArray(date.emails)) {
                        date.emails.forEach(function (emailObj) {
                            $('#tabelEmailBlet tbody').append(`
                            <tr>
                                <td>${emailObj.numeJudet}</td>
                                <td>${emailObj.email}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary editEmailBLET" data-id="${emailObj.id}" data-email="${emailObj.email}" data-id_judet="${emailObj.id_judet}" data-nume_judet="${emailObj.numeJudet}"><i class="fa fa-edit"></i></button>
                                    <button class="btn btn-sm btn-outline-danger deleteEmailBLET" data-id="${emailObj.id}"><i class="fa fa-trash"></i></button>
                                </td>
                            </tr>
                        `);
                        });
                    } else {
                        $('#tabelEmailBlet tbody').append(`
                        <tr><td colspan="3" class="text-center text-muted">${date.message || 'Nu s-au găsit email-uri'}</td></tr>
                    `);
                    }
                } catch (e) {
                    console.error("Eroare parsare JSON:", e);
                    console.log("Răspunsul serverului:", raspuns);
                }
            },
            error: function (xhr, status, error) {
                console.error('Eroare la cererea getEmail:', error);
            }
        });
    });

    $(document).on('click', '.deleteEmailBLET', function () {
        const id = $(this).data('id');

        Swal.fire({
            title: 'Ești sigur?',
            text: "Emailul va fi șters definitiv!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Da, șterge-l!',
            cancelButtonText: 'Anulează'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: 'controller/administrareBLET.php',
                    type: 'POST',
                    data: {
                        action: 'deleteBletEmail',
                        id: id
                    },
                    success: function (raspuns) {
                        try {
                            const date = JSON.parse(raspuns);

                            if (date.success) {
                                Swal.fire(
                                    'Șters!',
                                    'Emailul a fost șters.',
                                    'success'
                                );
                                $('#manageBLET').trigger('shown.bs.modal');
                            } else {
                                Swal.fire(
                                    'Eroare!',
                                    date.message || 'Emailul nu a putut fi șters.',
                                    'error'
                                );
                            }
                        } catch (e) {
                            console.error("Eroare parsare JSON:", e);
                            console.log("Răspuns server:", raspuns);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("Eroare AJAX ștergere:", error);
                    }
                });
            }
        });
    });

    $(document).on('click', '.editEmailBLET', function () {
        const id = $(this).data('id');
        const email = $(this).data('email');
        const id_judet = $(this).data('id_judet');
        const nume_judet = $(this).data('nume_judet');

        if ($('#manageBLET').hasClass('show')) {
            manageBLETWasOpen = true;
            $('#manageBLET').modal('hide');
        } else {
            manageBLETWasOpen = false;
        }


        $('#editEmailModal #emailInput').val(email);
        $('#editEmailModal').data('id', id).data('id_judet', id_judet).modal('show');

        $('#editEmailModalLabel').text(`Editare Email - ${nume_judet}`);

    });

    $('#editEmailModal').on('hidden.bs.modal', function () {

        $('#emailInput').val('');
        $('#emailId').val('');
        $('#idJudetInput').val('');
        $('#editEmailModalLabel').text('Editare Email');

        if (manageBLETWasOpen) {
            $('#manageBLET').modal('show');
            manageBLETWasOpen = false;
        }
    });


    $('#saveEmailButton').on('click', function () {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const id = $('#editEmailModal').data('id');
        const email = $('#emailInput').val();
        const id_judet = $('#editEmailModal').data('id_judet');

        if (!email || !emailRegex.test(email)) {
            Swal.fire({
                icon: 'error',
                title: 'Email invalid',
                text: 'Vă rugăm să introduceți un email valid.',
            });
            return;
        }

        console.log('Datele trimise:', { id, email, id_judet });

        $.ajax({
            url: 'controller/administrareBLET.php',
            type: 'POST',
            data: {
                action: 'editBletEmail',
                id: id,
                email: email,
                id_judet: id_judet
            },
            success: function (raspuns) {
                try {
                    const date = JSON.parse(raspuns);

                    if (date.success) {
                        Swal.fire(
                            'Salvat!',
                            'Emailul a fost actualizat cu succes.',
                            'success'
                        );
                        $('#editEmailModal').modal('hide');
                        $('#manageBLET').trigger('shown.bs.modal');
                    } else {
                        Swal.fire(
                            'Eroare!',
                            date.message || 'Emailul nu a putut fi actualizat.',
                            'error'
                        );
                    }
                } catch (e) {
                    console.error("Eroare la procesarea răspunsului:", e);
                }
            },
            error: function (xhr, status, error) {
                console.error('Eroare AJAX:', error);
            }
        });
    });


    $('#addEmailBLET').on('click', function () {
        if ($('#manageBLET').hasClass('show')) {
            manageBLETWasOpen = true;
            $('#manageBLET').modal('hide');
        } else {
            manageBLETWasOpen = false;
        }
        $('#addEmailModal').modal('show');
    });

    $('#addEmailModal').on('shown.bs.modal', function () {
        loadJudete();
    });

    $('#addEmailModal').on('hidden.bs.modal', function () {
        if (manageBLETWasOpen) {
            $('#manageBLET').modal('show');
            manageBLETWasOpen = false;
        }
    });

    $('#addEmailButton').on('click', function () {
        var email = $('#newEmailInput').val();
        var judetId = $('#judetSelect').val();

        console.log("Trimitem:", { email, judetId });

        $.ajax({
            url: 'controller/administrareBLET.php',
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'insertBletEmail',
                email: email,
                judetId: judetId
            },
            success: function (response) {
                if (response.status === 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: response.title,
                        text: response.text
                    });
                    $('#newEmailInput').val('');
                    $('#judetSelect').val('');
                    $('#addEmailModal').modal('hide');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: response.title,
                        text: response.text
                    });
                }
            },
            error: function (xhr, status, error) {
                console.error('Eroare la trimiterea datelor:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'A apărut o eroare',
                    text: 'Te rugăm să încerci din nou!'
                });
            }
        });
    });



    $('#manageUnasignedUsersModal').on('shown.bs.modal', function () {
        loadUnassignedUsers();
    });


});
