const Toast = Swal.mixin({
    toast: true,
    position: "top-end",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        hideTooltips();
        toast.onmouseenter = Swal.stopTimer;
        toast.onmouseleave = Swal.resumeTimer;
    },
    didClose: () => {
        hideTooltips();
    }
});

function getSpecializari() {
    const formData = new FormData();
    formData.append('getSpecializari', 1);

    $.ajax({
        method: 'post',
        processData: false,
        contentType: false,
        cache: false,
        data: formData,
        enctype: 'multipart/form-data',
        url: 'controller/desemnare.php',
        success: function (response) {
            handleSpecializariResponse(response);
            render2Select();
        },
        error: handleAjaxError
    });
}

function handleSpecializariResponse(response) {
    const data = typeof response === "string" ? JSON.parse(response) : response;

    if (data.status === 'ok') {
        const $select = $('.select_specializari');
        $select.empty().append($(data.message)).trigger('change');
        setTimeout(() => {
            $select.select2({
                placeholder: 'Specializări',
                width: '100%'
            });
        }, 0);
    } else {
        showError("Eroare", data.message);
    }
}

function handleAjaxError(data) {
    showError("Oops...", data.message, 'Ceva nu a mers bine!');
}

function showError(title, message, footer = '') {
    Swal.fire({
        icon: "error",
        title: title,
        html: message,
        footer: footer
    });
}

function checkNrDosar(valoare) {
    const regex = /^[0-9]+\/[0-9]+\/(19[0-9]{2}|20[0-9]{2})([^\s].*)?$/;
    return regex.test(valoare);
}

function render2Select() {
    $(document).find('.select_specializari').select2({
        placeholder: 'Specializări',
        width: '100%'
    });
}

function hideTooltips() {
    $('[data-bs-toggle="tooltip"]').each(function () {
        try {
            const tooltip = bootstrap.Tooltip.getInstance(this);
            if (tooltip) {
                tooltip.hide();
            }
        } catch (e) {
            console.warn('Error hiding tooltip:', e);
        }
    });
}

function getSessionIdInstanta(id_instanta) {
    return new Promise(function(resolve, reject) {
        const formData=new FormData();
        formData.append('verificareIDinstanta', 1);
        formData.append('id_instanta', id_instanta);

        $.ajax({
            method: 'post',
            processData: false,
            contentType: false,
            cache: false,
            data: formData,
            enctype: 'multipart/form-data',
            url: 'controller/getSessionIdInstanta.php',
            success: function (response) {
                resolve(response);
            },
            error: function(xhr, status, error) {
                console.error('Eroare la obținerea datelor:', status, error);
                reject(error);
            }
        });

    });
}


$(document).ready(function () {
    $(document).on('click', function (e) {
        hideTooltips();
    });

    $(document).on('mouseleave', '[data-bs-toggle="tooltip"]', function () {
        try {
            const tooltip = bootstrap.Tooltip.getInstance(this);
            if (tooltip) {
                tooltip.hide();
            }
        } catch (e) {
            console.warn('Error on tooltip mouseleave:', e);
        }
    });

    $(document).find('.afisare1, .afisare2, .afisare3, .afisare4').hide();
    getSpecializari();
    render2Select();

    $(document).off('click', '#btnVerificaDosar').on('click', '#btnVerificaDosar', function () {
        $('div.mesajeHandle').html('');
        const inputValue = $('#numarDosar').val();


        if (!checkNrDosar(inputValue)) {
            console.log('Checking nr dosar...');
            $(document).find('.afisare1, .afisare2, .afisare3, .afisare4').hide();
            $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare0 i').removeClass('fa').addClass('far');
            return;
        }

        console.log("Valid format nr dosar!");

        verificareInstanta();

    });

    $(document).off('input', '#numarDosar').on('input', '#numarDosar', function () {
        $('div.mesajeHandle').html('');
        const inputValue = $('#numarDosar').val();

             $(document).find('.afisare1, .afisare2, .afisare3, .afisare4').hide();
             $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare0 i').removeClass('fa').addClass('far');
    });

    $(document).off('change', 'select[name="nrExperti"]').on('change', 'select[name="nrExperti"]', function () {
        render2Select();
        const selectedValue = $(this).val()
            , $select = $(document).find('.select_specializari')
            , selectedOptions = $select.val() || [];

        // ascundere/afișare buton în funcție de nrExperti
        if (selectedValue == 3) {
            $('#btnExpPropus').hide();
        } else {
            $('#btnExpPropus').show();
        }

        if (selectedValue) {
            $(document).find('.afisare2').show();
            $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare1 i').addClass('fa').removeClass('far');
        } else {
            $(document).find('.afisare2').hide();
            $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare1 i').addClass('far').removeClass('fa');
        }

        if ((selectedValue == 1 && selectedOptions.length > 1) || (selectedValue == 3 && selectedOptions.length > 3) || (selectedValue == 11 && selectedOptions.length > 3)) {
            Swal.fire({
                title: "Ați depășit numărul maxim permis de specializări expertul ales!",
                text: "Vă rugăm să refaceți selecția!",
                icon: "warning",
                didOpen: () => {
                    hideTooltips();
                    $(".swal2-confirm")
                        .attr("data-bs-toggle", "tooltip")
                        .attr("data-bs-html", "true")
                        .attr("title", "<b>Puteți alege cel mult 1 specializare / expert</b><br>sau<br><b>Puteți alege cel mult 3 specializări / 3 experți</b><br>sau<br><b>Puteți alege 1 expert cu cel mult 3 specializări</b>")
                        .tooltip({html: true});
                },
                didClose: () => {
                    hideTooltips();
                }
            });
        }
    });

    let lastSelected = {};
    $(document).off('focus', '.select_specializari').on('focus', '.select_specializari', function () {
        lastSelected[this.name] = $(this).val() || [];
    });

    $(document).off('change', '.select_specializari').on('change', '.select_specializari', function () {
        var $select = $(this),
            selectedOptions = $select.val() || [],
            nrExperti = $('select[name="nrExperti"]').val();

        if ((nrExperti == 1 && selectedOptions.length > 1) || (nrExperti == 3 && selectedOptions.length > 1) || (nrExperti == 11 && selectedOptions.length > 3)) {
            Swal.fire({
                title: "Ați depășit numărul maxim permis de specializări!",
                text: "Vă rugăm să refaceți selecția!",
                icon: "warning",
                didOpen: () => {
                    hideTooltips();
                    $(".swal2-confirm")
                        .attr("data-bs-toggle", "tooltip")
                        .attr("data-bs-html", "true")
                        .attr("title", "<b>Puteți alege cel mult 1 specializare / expert</b><br>sau<br><b>Puteți alege cel mult 1 specializare / 3 experți</b><br>sau<br><b>Puteți alege 1 expert cu cel mult 3 specializări</b>")
                        .tooltip({html: true});
                },
                didClose: () => {
                    hideTooltips();
                }
            });

            let lastSelectedValue = selectedOptions.find(value => !lastSelected[this.name].includes(value));
            if (lastSelectedValue) {
                $select.find(`option[value="${lastSelectedValue}"]`).prop('selected', false);
                $select.trigger('change.select2');
            }
        }

        lastSelected[this.name] = $select.val() || []; // Update stored selection
        if ((nrExperti == 1 && selectedOptions.length == 1) || (nrExperti == 3 && selectedOptions.length <= 3) || (nrExperti == 11 && selectedOptions.length > 3)) {
            $(document).find('.afisare2').show();
            $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare1 i').addClass('fa').removeClass('far');
        }
    });

    $(document).find('#onorariu').on('input', function (e) {
        const selectedValues = $(this).val();

        if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
            return false;
        }

        setTimeout(function () {
            let value = $('#onorariu').val();
            $('#onorariu').val(Math.floor(value));
        }, 0);

        let value = $(this).val();
        if (value) {
            $(this).val(Math.floor(value));
        }

        if (selectedValues && selectedValues.length > 0) {
            $(document).find('.afisare3').show();
            $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare2 i').addClass('fa').removeClass('far');
            //auditare completare formular de catre user, fara click pe btn, in acest moment
            $.ajax({
                url: 'controller/audit.php',
                type: 'POST',
                data: {
                    action: 'completare_formular',
                    form_name: 'desemnare',
                    campuri_form: {
                        numar_dosar: $('#numarDosar').val(),
                        numar_experti: $('select[name="nrExperti"]').val(),
                        specializari_alese: $('.select_specializari').val(),
                        onorariu: $('#onorariu').val()
                    }
                },
                success: function (response) {
                    console.log(response);
                },
                error: function (error) {
                    console.error(error);
                }
            });
        } else {
            $(document).find('.afisare3').hide();
            $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare2 i').addClass('far').removeClass('fa');
        }
    });

    $(document).off('click', '#btnExpAleatoriu').on('click', '#btnExpAleatoriu', function () {
        $('div.mesajeHandle').html('');
        $(document).find('.afisare4').hide();
        $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare4 i').addClass('fa').removeClass('far');
        const nrDosar = $(document).find('#numarDosar').val();
        const nrExperti = $(document).find('select[name="nrExperti"] option:selected').val();

        const specializariAlese = [];
        const uniquePairs = new Set();
        const processedValues = new Set(); // Track all values we've processed
        const selectElement = $(document).find('select[name="specializari[]"]');
        const currentSelections = selectElement.select2('data');
        // console.log("Current selections:", currentSelections);
        $.each(currentSelections, function (index, selection) {
            const optionValue = parseInt(selection.id);
            processedValues.add(optionValue); // Mark this value as processed
            const optionElement = selectElement.find(`option[value="${selection.id}"]`);
            const optgroupElement = optionElement.parent('optgroup');
            const optgroupId = optgroupElement.length ? parseInt(optgroupElement.attr('id')) : 0;
            let firstValue, secondValue;
            if (optgroupId > 0) {
                firstValue = optgroupId;
                secondValue = optionValue;
            } else {
                firstValue = optionValue;
                secondValue = 0;
            }
            const pairKey = `${firstValue},${secondValue}`;
            if (!uniquePairs.has(pairKey)) {
                specializariAlese.push([firstValue, secondValue]);
                uniquePairs.add(pairKey);
                // console.log("Added pair:", [firstValue, secondValue]);
            }
        });
        const singleSpecialtyElement = $(document).find('.select_specializari');
        if (singleSpecialtyElement.length && singleSpecialtyElement.val()) {
            let singleValue;
            if (singleSpecialtyElement.hasClass('select2-hidden-accessible')) {
                const selectData = singleSpecialtyElement.select2('data');
                if (selectData && selectData.length > 0) {
                    const singleData = selectData[0];
                    singleValue = parseInt(singleData.id);
                }
            } else {
                singleValue = parseInt(singleSpecialtyElement.val());
            }
            if (!processedValues.has(singleValue)) {
                const singlePairKey = `${singleValue},0`;
                if (!uniquePairs.has(singlePairKey)) {
                    specializariAlese.push([singleValue, 0]);
                    uniquePairs.add(singlePairKey);
                    // console.log("Added single specialty:", [singleValue, 0]);
                }
            } else {
                // console.log("Skipping single specialty as it's already in the main selection:", singleValue);
            }
        }
        // console.log("Final specializariAlese array:", specializariAlese);

        const onorariu = $(document).find('#onorariu').val();

        $.ajax({
            url: 'controller/desemnare.php',
            type: 'POST',
            dataType: 'json',
            data: {
                checkDosar: true,
                nrDosar: nrDosar,
                nrExperti: nrExperti,
                specializariAlese: JSON.stringify(specializariAlese)
            },
            success: function (checkResponse) {
                if (checkResponse.status === 'ok') {
                    $.ajax({
                        url: 'controller/desemnare.php',
                        type: 'POST',
                        data: {
                            randomExpert: true,
                            nrDosar: nrDosar,
                            nrExperti: nrExperti,
                            specializariAlese: JSON.stringify(specializariAlese),
                            onorariu: onorariu
                        },
                        xhrFields: {responseType: 'blob'},
                        success: function (response) {
                            var blob = new Blob([response], {type: 'application/pdf'});
                            var link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = 'expert_aleatoriu.pdf';
                            link.click();
                            window.URL.revokeObjectURL(link.href);

                            $(document).find('.afisare3').closest('.row').find('div.col-lg-2 p.afisare3 i').addClass('fa').removeClass('far');

                            let timerInterval;
                            Swal.fire({
                                icon: "success",
                                title: 'A fost desemnat <b>aleatoriu</b> expertul. Fisierul PDF a fost generat.',
                                timer: 2000,
                                timerProgressBar: true,
                                willClose: () => {
                                    clearInterval(timerInterval);
                                }
                            }).then(() => {
                                $(document).find('.afisare4').hide();
                                $(document).find('div.afisare4').html('');
                                formularReset();
                            });
                        },
                        error: function (response) {
                            Swal.fire({
                                title: "Eroare generare PDF",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    });
                } else if (checkResponse.status === 'warning') {
                    // Expert deja alocat, cere confirmare pentru înlocuire
                    Swal.fire({
                        title: 'Expert deja alocat!',
                        html: checkResponse.message,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Înlocuiește expertul',
                        cancelButtonText: 'Anulează'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Continuă cu desemnarea
                            $.ajax({
                                url: 'controller/desemnare.php',
                                type: 'POST',
                                data: {
                                    randomExpert: true,
                                    nrDosar: nrDosar,
                                    nrExperti: nrExperti,
                                    specializariAlese: JSON.stringify(specializariAlese),
                                    onorariu: onorariu
                                },
                                xhrFields: {responseType: 'blob'},
                                success: function (response) {
                                    var blob = new Blob([response], {type: 'application/pdf'});
                                    var link = document.createElement('a');
                                    link.href = window.URL.createObjectURL(blob);
                                    link.download = 'expert_aleatoriu.pdf';
                                    link.click();
                                    window.URL.revokeObjectURL(link.href);

                                    $(document).find('.afisare3').closest('.row').find('div.col-lg-2 p.afisare3 i').addClass('fa').removeClass('far');

                                    let timerInterval;
                                    Swal.fire({
                                        icon: "success",
                                        title: 'A fost desemnat <b>aleatoriu</b> expertul. Fisierul PDF a fost generat.',
                                        timer: 2000,
                                        timerProgressBar: true,
                                        willClose: () => {
                                            clearInterval(timerInterval);
                                        }
                                    }).then(() => {
                                        $(document).find('.afisare4').hide();
                                        $(document).find('div.afisare4').html('');
                                        formularReset();
                                    });
                                },
                                error: function (xhr, status, error) {
                                    console.error("AJAX ERROR", xhr.responseText, status, error);
                                    Swal.fire({
                                        title: "Eroare",
                                        text: "Nu există un alt expert cu care să fie făcută înlocuirea.",  // mesajul asta ar trebui sa fie intors din lottery daca expertigasiti-expertialocati=0
                                        icon: "error"
                                    });
                                }

                            });
                        }
                    });
                } else {
                    // Altă eroare (lipsă specializări, lipsă experți)
                    Swal.fire({
                        title: "Eroare",
                        text: checkResponse.message,
                        icon: "error"
                    });
                }
                $('div.mesajeHandle').html(checkResponse.message).css('white-space', 'pre-line');
            },
            error: function () {
                Swal.fire({
                    title: "Eroare",
                    text: "Posibil sesiune expirată. Reautentificați-vă.",
                    icon: "error"
                });
            }
        });
    });

    $(document).off('click', '#btnExpPropus').on('click', '#btnExpPropus', function () {
        $('div.mesajeHandle').html('');
        $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare4 i').addClass('fa').removeClass('far');
        const nrDosar = $(document).find('#numarDosar').val();
        const nrExperti = $(document).find('select[name="nrExperti"] option:selected').val();
        const onorariu = $(document).find('#onorariu').val();

        const specializariAlese = [];
        const uniquePairs = new Set();
        const processedValues = new Set();
        const selectElement = $(document).find('select[name="specializari[]"]');
        const currentSelections = selectElement.select2('data');

        $.each(currentSelections, function (index, selection) {
            const optionValue = parseInt(selection.id);
            processedValues.add(optionValue);
            const optionElement = selectElement.find(`option[value="${selection.id}"]`);
            const optgroupElement = optionElement.parent('optgroup');
            const optgroupId = optgroupElement.length ? parseInt(optgroupElement.attr('id')) : 0;
            let firstValue = optgroupId > 0 ? optgroupId : optionValue;
            let secondValue = optgroupId > 0 ? optionValue : 0;
            const pairKey = `${firstValue},${secondValue}`;
            if (!uniquePairs.has(pairKey)) {
                specializariAlese.push([firstValue, secondValue]);
                uniquePairs.add(pairKey);
            }
        });

        const singleSpecialtyElement = $(document).find('.select_specializari');
        if (singleSpecialtyElement.length && singleSpecialtyElement.val()) {
            let singleValue;
            if (singleSpecialtyElement.hasClass('select2-hidden-accessible')) {
                const selectData = singleSpecialtyElement.select2('data');
                if (selectData && selectData.length > 0) {
                    const singleData = selectData[0];
                    singleValue = parseInt(singleData.id);
                }
            } else {
                singleValue = parseInt(singleSpecialtyElement.val());
            }
            if (!processedValues.has(singleValue)) {
                const singlePairKey = `${singleValue},0`;
                if (!uniquePairs.has(singlePairKey)) {
                    specializariAlese.push([singleValue, 0]);
                    uniquePairs.add(singlePairKey);
                }
            }
        }
        // Verificare checkDosar înainte de Swal
        $.ajax({
            url: 'controller/desemnare.php',
            type: 'POST',
            dataType: 'json',
            data: {
                checkDosar: true,
                nrDosar: nrDosar,
                nrExperti: nrExperti,
                specializariAlese: JSON.stringify(specializariAlese)
            },
            success: function (checkResponse) {
                if (checkResponse.status === 'warning') {
                    Swal.fire({
                        title: 'Există deja un expert alocat!',
                        html: checkResponse.message,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Înlocuiește expertul',
                        cancelButtonText: 'Renunță'
                    }).then(result => {
                        if (result.isConfirmed) {
                            Swal.fire({
                                title: "CNP expert",
                                input: "text",
                                inputAttributes: {
                                    autocapitalize: "off"
                                },
                                showCancelButton: true,
                                confirmButtonText: "Căutare",
                                showLoaderOnConfirm: true,
                                preConfirm: (cnpInput) => {
                                    return new Promise((resolve, reject) => {
                                        const formData = new FormData();
                                        formData.append('getExpertConvenit', 1);
                                        formData.append('nrDosar', nrDosar);
                                        formData.append('nrExperti', nrExperti);
                                        formData.append('specializariAlese', JSON.stringify(specializariAlese));
                                        formData.append('onorariu', onorariu);
                                        formData.append('cnp', cnpInput);

                                        $.ajax({
                                            url: 'controller/desemnare.php',
                                            type: 'POST',
                                            data: formData,
                                            processData: false,
                                            contentType: false,
                                            xhrFields: {
                                                responseType: 'arraybuffer'
                                            },
                                            dataType: 'binary',
                                            converters: {
                                                'binary arraybuffer': function (data) {
                                                    return data;
                                                }
                                            },
                                            success: function (response, status, xhr) {
                                                const contentType = xhr.getResponseHeader('Content-Type');

                                                if (contentType && contentType.includes('application/json')) {
                                                    const decoder = new TextDecoder('utf-8');
                                                    const jsonString = decoder.decode(response);
                                                    try {
                                                        const jsonResponse = JSON.parse(jsonString);
                                                        if (jsonResponse.status === 'error') {
                                                            $(document).find('.afisare4').show();
                                                            $(document).find('div.afisare4').html(jsonResponse.message);
                                                            resolve({
                                                                isConfirmed: false,
                                                                message: jsonResponse.message
                                                            });
                                                        } else {
                                                            resolve(true);
                                                        }
                                                    } catch (e) {
                                                        console.error('JSON parsing error:', e);
                                                        reject('Invalid JSON response');
                                                    }
                                                } else if (contentType && contentType.includes('application/pdf')) {
                                                    const blob = new Blob([response], {type: 'application/pdf'});
                                                    const link = document.createElement('a');
                                                    link.href = window.URL.createObjectURL(blob);
                                                    link.download = `expert_convenit_${nrDosar}.pdf`;
                                                    link.click();
                                                    window.URL.revokeObjectURL(link.href);

                                                    let timerInterval;
                                                    Swal.fire({
                                                        icon: "success",
                                                        title: 'A fost desemnat expertul <b>convenit</b>. Fisierul PDF a fost generat.',
                                                        timer: 2000,
                                                        timerProgressBar: true,
                                                        willClose: () => {
                                                            clearInterval(timerInterval);
                                                        }
                                                    }).then(() => {
                                                        $(document).find('.afisare4').hide();
                                                        $(document).find('div.afisare4').html('');
                                                        formularReset();
                                                    });
                                                    resolve(true);
                                                } else {
                                                    reject('Unexpected response type: ' + (contentType || 'unknown'));
                                                }
                                            },
                                            error: function (xhr, status, error) {
                                                console.error('Ajax error:', {
                                                    status: status,
                                                    error: error,
                                                    xhr: xhr
                                                });
                                                reject(`Request failed: ${error}`);
                                            }
                                        });
                                    });
                                },
                                allowOutsideClick: () => !Swal.isLoading()
                            }).then((result) => {
                                if (result && result.isConfirmed === false && result.message) {
                                    $('#numarDosar').closest('.row')
                                        .find('div.col-lg-2 p.afisare4 i')
                                        .addClass('fa')
                                        .removeClass('far');
                                } else if (result && result.isConfirmed) {
                                    $('#numarDosar').closest('.row')
                                        .find('div.col-lg-2 p.afisare4 i')
                                        .addClass('fa')
                                        .removeClass('far');
                                }
                            }).catch(error => {
                                console.error('Error:', error);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Oops...',
                                    text: 'A apărut o eroare în procesarea cererii.'
                                });
                            });
                        }
                    });
                } else if (checkResponse.status === 'ok') {
                    // Deschide input CNP doar dacă dosarul e valid sau expertul trebuie înlocuit
                    Swal.fire({
                        title: "CNP expert",
                        input: "text",
                        inputAttributes: {
                            autocapitalize: "off"
                        },
                        showCancelButton: true,
                        confirmButtonText: "Căutare",
                        showLoaderOnConfirm: true,
                        preConfirm: (cnpInput) => {
                            return new Promise((resolve, reject) => {
                                const formData = new FormData();
                                formData.append('getExpertConvenit', 1);
                                formData.append('nrDosar', nrDosar);
                                formData.append('nrExperti', nrExperti);
                                formData.append('specializariAlese', JSON.stringify(specializariAlese));
                                formData.append('onorariu', onorariu);
                                formData.append('cnp', cnpInput);

                                $.ajax({
                                    url: 'controller/desemnare.php',
                                    type: 'POST',
                                    data: formData,
                                    processData: false,
                                    contentType: false,
                                    xhrFields: {
                                        responseType: 'arraybuffer'
                                    },
                                    dataType: 'binary',
                                    converters: {
                                        'binary arraybuffer': function (data) {
                                            return data;
                                        }
                                    },
                                    success: function (response, status, xhr) {
                                        const contentType = xhr.getResponseHeader('Content-Type');

                                        if (contentType && contentType.includes('application/json')) {
                                            const decoder = new TextDecoder('utf-8');
                                            const jsonString = decoder.decode(response);
                                            try {
                                                const jsonResponse = JSON.parse(jsonString);
                                                if (jsonResponse.status === 'error') {
                                                    $(document).find('.afisare4').show();
                                                    $(document).find('div.afisare4').html(jsonResponse.message);
                                                    resolve({
                                                        isConfirmed: false,
                                                        message: jsonResponse.message
                                                    });
                                                    Toast.fire({
                                                        icon: 'error',
                                                        html: jsonResponse.message
                                                    });
                                                } else {
                                                    resolve(true);
                                                }
                                            } catch (e) {
                                                console.error('JSON parsing error:', e);
                                                reject('Invalid JSON response');
                                            }
                                        } else if (contentType && contentType.includes('application/pdf')) {
                                            const blob = new Blob([response], {type: 'application/pdf'});
                                            const link = document.createElement('a');
                                            link.href = window.URL.createObjectURL(blob);
                                            link.download = `expert_convenit_${nrDosar}.pdf`;
                                            link.click();
                                            window.URL.revokeObjectURL(link.href);

                                            let timerInterval;
                                            Swal.fire({
                                                icon: "success",
                                                title: 'A fost desemnat expertul <b>convenit</b>. Fisierul PDF a fost generat.',
                                                timer: 2000,
                                                timerProgressBar: true,
                                                willClose: () => {
                                                    clearInterval(timerInterval);
                                                }
                                            }).then(() => {
                                                $(document).find('.afisare4').hide();
                                                $(document).find('div.afisare4').html('');
                                            });
                                            resolve(true);
                                        } else {
                                            reject('Unexpected response type: ' + (contentType || 'unknown'));
                                        }
                                    },
                                    error: function (xhr, status, error) {
                                        console.error('Ajax error:', {
                                            status: status,
                                            error: error,
                                            xhr: xhr
                                        });
                                        reject(`Request failed: ${error}`);
                                    }
                                });
                            });
                        },
                        allowOutsideClick: () => !Swal.isLoading()
                    }).then((result) => {
                        if (result && result.isConfirmed === false && result.message) {
                            $('#numarDosar').closest('.row')
                                .find('div.col-lg-2 p.afisare4 i')
                                .addClass('fa')
                                .removeClass('far');
                            formularReset();
                        } else if (result && result.isConfirmed) {
                            $('#numarDosar').closest('.row')
                                .find('div.col-lg-2 p.afisare4 i')
                                .addClass('fa')
                                .removeClass('far');
                        }
                    }).catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'A apărut o eroare în procesarea cererii.'
                        });
                    });
                } else {
                    Swal.fire({
                        title: "Eroare",
                        text: checkResponse.message,
                        icon: "error"
                    });
                }
            },
            error: function () {
                Swal.fire({
                    title: "Eroare",
                    text: "Posibil sesiune expirată. Reautentificați-vă.",
                    icon: "error"
                });
            }
        });
    });

    function formularReset() {
        $('p[class^="afisare"]').find('input').val('');
        $('select[name="nrExperti"]').val(1);
        $('p[class^="afisare"]').find('.select2').empty();
        getSpecializari();
        // $('p[class^="afisare"]').hide();
        $('p[class^="afisare"] > i').removeClass('fa').addClass('far');

    }

    async function verificareInstanta() {
        try {

            const inputValue = $('#numarDosar').val();
            const splitValue = inputValue.split('/');

            let IdExtrasNumarDosar = Number(splitValue[1]);
            const data = await getSessionIdInstanta(IdExtrasNumarDosar);

            if (!data || !data.id_instanta) {
                showMessageAlertInstantaDosar(data.nume_instanta);
                console.log("Nu am primit id_instanta");
                return;
            }

            let idInst = Number(data.id_instanta);

            console.log("Compar:", IdExtrasNumarDosar, "cu", idInst);

            if (IdExtrasNumarDosar === 1) {
                const idICCJ = 5001;
                IdExtrasNumarDosar = idICCJ;
                idInst = idICCJ;
            }

            if (IdExtrasNumarDosar === idInst) {
                $('.afisare1').show();
                $('#numarDosar')
                    .closest('.row')
                    .find('div.col-lg-2 p.afisare0 i')
                    .removeClass('far')
                    .addClass('fa');
            } else {
                showMessageAlertInstantaDosar(data.nume_instanta);
                console.log("Valoarea nu se potrivește cu id_instanta");
                // Poți ascunde câmpurile aici, dacă dorești
            }

        } catch (error) {
            console.error("Eroare la verificare:", error);
        }
    }

});

function showMessageAlertInstantaDosar(numeInstanta) {
    if (!numeInstanta) {
        Swal.fire({
            // title: "Atentie!",
            //text: ``,
            html:` <b> Nr. dosar introdus: ${$('#numarDosar').val()} </b> <br> Numărul de dosar introdus conține un ID care nu aparține unei instanțe înregistrate.`,
            icon: "warning",
            showCancelButton: true,
            showConfirmButton: false,
            cancelButtonColor: "#d33",
            cancelButtonText : "Anulare"
        }).then((result)=>{
            if(!result.isConfirmed){ $('#numarDosar').val("");}
        });
    } else {

        Swal.fire({
            // title: "Atentie!",
            html:` <b> Nr. dosar introdus: ${$('#numarDosar').val()} </b> <br><br> Conform id de instanță din numărul de dosar introdus, acesta se află/s-a aflat pe rolul instanței:<br> ${numeInstanta} <br><br> <i>Continuă în cazul:<b> Dosar în Cale de atac / Declinat</b></i> `,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Continua ",
            cancelButtonText : "Anulare"
            // footer: 'Continua în cazul: Cale de atac,Recurs,Apel sau Declinare'
        }).then((result) => {
            if (result.isConfirmed) {
                $(document).find('.afisare1').show();
                $('#numarDosar').closest('.row').find('div.col-lg-2 p.afisare0 i').removeClass('far').addClass('fa');

            } else {
                $('#numarDosar').val("");

            }
        });
    }
}