/**
 * <PERSON><PERSON><PERSON> pentru a rezolva problema cu overflow-y în coloana Date din audit_logs.php
 */
document.addEventListener('DOMContentLoaded', function() {
    // Funcție pentru a verifica dacă un element are nevoie de scrollbar
    function needsScrollbar(element) {
        return element.scrollHeight > element.clientHeight;
    }

    // Funcție pentru a aplica stiluri suplimentare pentru elementele care au nevoie de scrollbar
    function enhanceScrollableElements() {
        // Selectează toate elementele json-data din coloana Date (a 10-a coloană)
        const jsonDataElements = document.querySelectorAll('.audit-table td:nth-child(10) .json-data');
        
        jsonDataElements.forEach(function(element) {
            // Verifică dacă elementul are nevoie de scrollbar
            if (needsScrollbar(element)) {
                // Adaugă o clasă pentru a indica că elementul are scrollbar
                element.classList.add('has-scrollbar');
                
                // Adaugă un indicator vizual pentru a arăta că există conținut care poate fi derulat
                const scrollIndicator = document.createElement('div');
                scrollIndicator.className = 'scroll-indicator';
                scrollIndicator.innerHTML = '<i class="fas fa-chevron-down"></i>';
                element.parentNode.appendChild(scrollIndicator);
                
                // Adaugă event listener pentru a ascunde indicatorul când utilizatorul derulează până la sfârșit
                element.addEventListener('scroll', function() {
                    const scrollIndicator = this.parentNode.querySelector('.scroll-indicator');
                    if (scrollIndicator) {
                        if (this.scrollHeight - this.scrollTop <= this.clientHeight + 10) {
                            scrollIndicator.style.opacity = '0';
                        } else {
                            scrollIndicator.style.opacity = '1';
                        }
                    }
                });
            }
        });
    }

    // Aplică stiluri suplimentare pentru elementele care au nevoie de scrollbar
    enhanceScrollableElements();

    // Forțează recalcularea înălțimii pentru toate elementele json-data
    const jsonDataElements = document.querySelectorAll('.json-data');
    jsonDataElements.forEach(function(element) {
        // Setează explicit înălțimea maximă
        element.style.maxHeight = '150px';
        // Forțează overflow-y
        element.style.overflowY = 'scroll';
        // Asigură că elementul este vizibil
        element.style.display = 'block';
    });

    // Adaugă stiluri inline pentru a suprascrie orice stil care ar putea interfera
    const style = document.createElement('style');
    style.textContent = `
        .audit-table td:nth-child(10) {
            position: relative !important;
            vertical-align: top !important;
            max-width: 300px !important;
            width: 300px !important;
        }
        .audit-table td:nth-child(10) .json-data {
            max-height: 150px !important;
            height: auto !important;
            overflow-y: scroll !important;
            overflow-x: hidden !important;
            display: block !important;
            width: 100% !important;
            box-sizing: border-box !important;
        }
        .scroll-indicator {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            opacity: 1;
            transition: opacity 0.3s;
            pointer-events: none;
            z-index: 10;
        }
    `;
    document.head.appendChild(style);
});
