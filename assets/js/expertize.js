$(document).ready(function () {
    $('#specializareFilter').select2({
        width: '100%',
        placeholder: 'Toate specializ<PERSON>rile',
        allowClear: true
    }).on('select2:open', function () {
        document.querySelector('.select2-search__field').focus();
    });

    $('.select2-selection').css({
        'height': '38px',
        'padding-top': '4px',
        'border': '1px solid #ced4da'  // culoarea standard Bootstrap pentru borduri
    });

    let table = $('#tabelExpertize').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "controller/getExpertize.php",
            "type": "POST",
            "data": function (d) {
                d.statusFilter = $('#statusFilter').val();
                d.specializare = $('#specializareFilter').val();
            }
        },
        "columns": [
            {"data": "nrDosar"},
            {"data": "expert"},
            {"data": "specializare"},
            {"data": "dataDesemnare"},
            {"data": "status"},
            {"data": "actiuni"}
        ],
        "dom": "<'row'<'col-sm-12 col-md-4'l><'col-sm-12 col-md-4'B><'col-sm-12 col-md-4'f>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                text: 'Reset Filters',
                className: 'btn btn-outline-danger',
                action: function () {
                    $('#specializareFilter').val(null).trigger('change');
                    $('#statusFilter').val('').trigger('change');
                    table.search('').columns().search('').draw();
                }
            },
            {
                extend: 'excel',
                text: 'Export Excel',
                className: 'btn btn-outline-success',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4]
                }
            },
            {
                extend: 'pdf',
                text: 'Export PDF',
                className: 'btn btn-outline-success',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4]
                }
            }
        ],
        "lengthMenu": [[10, 50, 100], [10, 50, 100]],
        "pageLength": 10,
        "language": {
            "processing": "Procesare...",
            "search": "Caută:",
            "lengthMenu": "Afișează _MENU_ înregistrări pe pagină",
            "info": "Afișate de la _START_ la _END_ din _TOTAL_ înregistrări",
            "infoEmpty": "Afișate de la 0 la 0 din 0 înregistrări",
            "infoFiltered": "(filtrate din _MAX_ înregistrări totale)",
            "loadingRecords": "Se încarcă...",
            "zeroRecords": "Nu au fost găsite înregistrări",
            "emptyTable": "Nu există date în tabel",
            "paginate": {
                "first": '<i class="fas fa-angle-double-left"></i>',
                "previous": '<i class="fas fa-angle-left"></i>',
                "next": '<i class="fas fa-angle-right"></i>',
                "last": '<i class="fas fa-angle-double-right"></i>'
            }
        },
        initComplete: function () {
            //nimic momentan
        }

    });

    function showLoadingSwal() {
        let swalInstance = Swal.fire({
            title: "Se încarcă datele...",
            html: "Un moment, lucrăm la asta.",
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        return swalInstance;
    }

    $('#tabelExpertize').on('preXhr.dt', function () {
        let swalLoading = showLoadingSwal();

        table.one('xhr.dt', function () {
            swalLoading.close();
        });
    });

    $('#tabelExpertize').on('init.dt', function () {
        var searchDelay = null;
        $('#tabelExpertize_filter input').off().on('input', function () {
            var self = this;
            clearTimeout(searchDelay);
            searchDelay = setTimeout(function () {
                table.search($(self).val()).draw();
            }, 1000);
        });
    });

    $('#statusFilter, #specializareFilter').on('change', function () {
        table.draw();
    });

    $('#specializareFilter').on('change', function () {
        table.ajax.reload();
    });

    $('.reset-filters').on('click', function () {
        $('#specializareFilter').val(null).trigger('change');
        $('#statusFilter').val('').trigger('change');
        table.draw();
    });

    $(document).off('click', '.dosar-eronat-btn').on('click', '.dosar-eronat-btn', function (e) {
        e.preventDefault();
        const id = $(this).data('id');

        $.ajax({
            url: 'controller/dosarEronat.php',
            type: 'POST',
            dataType: 'json',
            data: {getModalDosarEronat: true},
            success: function (response) {
                if (response.status === 'success') {
                    $('#dosarEronatModal').remove();
                    $('body').append(response.data);
                    const myModal = new bootstrap.Modal(document.getElementById('dosarEronatModal'));
                    myModal.show();

                    $.ajax({
                        url: 'controller/dosarEronat.php',
                        type: 'POST',
                        dataType: 'json',
                        data: {id: id},
                        success: function (response) {
                            if (response.status === 'success') {
                                $('#expertizaId').val(id);
                                $('#numarDosar').val(response.data.nrDosar);
                                $('#numePrenume').val(response.data.nume_expert);
                                $('#cnp').val(response.data.cnpExpert);
                                $('#dosarEronatModal').modal('show');
                            } else {
                                Swal.fire('Eroare!', response.message, 'error');
                            }
                        }
                    });

                }
            }
        });
    });

    $(document).off('click', '#saveDosarEronat').on('click', '#saveDosarEronat', function () {
        // Verificăm dacă există un fișier selectat
        const fileInput = $('#documentJustificativ')[0];
        if (!fileInput.files || !fileInput.files[0]) {
            Swal.fire({
                title: 'Eroare!',
                text: 'Vă rugăm să selectați un document PDF',
                icon: 'error'
            });
            return;
        }

        const formData = new FormData();
        formData.append('document', fileInput.files[0]);
        formData.append('id', $('#expertizaId').val());

        // Adăugăm indicator de loading
        Swal.fire({
            title: 'Se procesează...',
            text: 'Vă rugăm să așteptați',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: 'controller/dosarEronat.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            processData: false,
            contentType: false,
            success: function (response) {
                if (response.status === 'success') {
                    Swal.fire({
                        title: 'Succes!',
                        text: response.message,
                        icon: 'success'
                    }).then(() => {
                        $('#dosarEronatModal').modal('hide');
                        table.ajax.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Eroare!',
                        text: response.message || 'A apărut o eroare neașteptată',
                        icon: 'error'
                    });
                }
            },
            error: function (xhr, status, error) {
                console.error('Error:', xhr.responseText);
                Swal.fire({
                    title: 'Eroare!',
                    text: 'A apărut o eroare la procesarea cererii',
                    icon: 'error'
                });
            }
        });
    });

});