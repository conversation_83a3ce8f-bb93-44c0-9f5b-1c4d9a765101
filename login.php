<?php
require_once 'vendor/autoload.php';
include_once "inc/cfg_db.php";
require_once 'inc/cfg_functions.php';
require_once 'controller/AccessControl.php';

$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');
$message = null;

$logged_in = $sesiune->get('logged_in');
if ($logged_in == true) {
    $redirectLocation = getDefaultPageForRole($sesiune->get('id_rol'));
    header("Location: $redirectLocation");
    exit;
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $message = checkCredentials();
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="../assets/img/icon/var1.ico" rel="icon"/>

    <title>Autentificare Aplicație desemnare experți tehnici judiciari</title>
    <link href="assets/css/font-awesome-all.min.css" rel="stylesheet"/>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #9a9696;
        }

        .login-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            width: 500px;
            text-align: center;
        }

        .login-container h2 {
            margin-bottom: 20px;
        }

        .input-group {
            position: relative;
            margin-bottom: 15px;
        }

        .input-group i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .input-group input {
            width: 100%;
            padding: 10px 40px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
        }

        .input-group input:focus {
            border-color: #007bff;
            outline: none;
        }

        .input-group label {
            position: absolute;
            left: 40px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
            font-size: 14px;
            transition: 0.3s;
            pointer-events: none;
        }

        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label {
            top: 5px;
            left: 40px;
            font-size: 12px;
            color: #007bff;
        }

        .login-btn {
            width: 100%;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: 0.3s;
        }

        .login-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>

<div class="container login-container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <h3 class="mt-3 mb-3">Desemnare Experți Tehnici Judiciari</h3>
        </div>
    </div>
    <div class="row justify-content-center">
        <div class="col-md-12">
            <form action="login.php" method="POST" class="mt-3 mb-3">
                <div class="input-group">
                    <i class="fa fa-user"></i>
                    <input type="text" name="username" id="username" placeholder="" required>
                    <label for="username">Username</label>
                </div>

                <div class="input-group">
                    <i class="fa fa-lock"></i>
                    <input type="password" name="password" id="password" placeholder="" required>
                    <label for="password">Parolă</label>
                </div>

                <?php
                if ($message) {
                    echo "<div class='mb-4 mt-4 alert alert-danger alert-link shadow-lg'>$message</div>";
                } ?>

                <div>
                    <button type="submit" class="login-btn">Autentificare</button>
                </div>
            </form>
        </div>
    </div>
</div>

</body>
</html>
<?php
$showWarning = true;
$dbConnection = DatabasePool::getConnection();
$checkQuery = "SELECT id FROM exp_jud.warning_acknowledgments where ip_address = '$_SERVER[REMOTE_ADDR]'";
$stmt = $dbConnection->query($checkQuery);
$nrstmt = $stmt->rowCount();
if ($nrstmt > 0) {
    $showWarning = false;
}
?>

<script src="assets/js/jquery.js"></script>
<script src="assets/js/swal.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        <?php if ($showWarning): ?>
        Swal.fire({
            title: 'Atenție! Mediu de producție',
            html: `
            <div class="text-start">
                <p>Această aplicație rulează în mediul de producție (real).</p>
                <p>Vă rugăm să:</p>
                <ul>
                    <li>Verificați cu atenție datele introduse</li>
                    <li>Confirmați corectitudinea informațiilor înainte de salvare</li>
                    <li>Tratați cu maximă responsabilitate orice operațiune</li>
                </ul>
                <p>Toate acțiunile sunt înregistrate și au impact direct asupra sistemului real.</p>
            </div>
        `,
            icon: 'warning',
            confirmButtonText: 'Am înțeles',
            confirmButtonColor: '#3085d6',
            allowOutsideClick: false
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: 'controller/warning_acknowledgment.php',
                    type: 'POST',
                    dataType: 'json',
                    data: {acknowledgment: true},
                    success: function (response) {
                        if (response.status === 'success') {
                            console.log('Confirmare cu succes');
                        }
                    }
                });
            }
        });
        <?php endif; ?>
    });
</script>