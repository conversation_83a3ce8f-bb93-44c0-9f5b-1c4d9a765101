<?php
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
$dbConnection = DatabasePool::getConnection();

if (!isset($_POST['action'])) {
    echo json_encode(['status' => 'error', 'message' => 'Cerere invalidă']);
    exit;
}
if ($_POST['action'] === 'addUser') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta, $SESSION_id_utilizator;

        $utilizator = sanitizeInput($_POST['utilizator']);
        if (filter_var($utilizator, FILTER_VALIDATE_EMAIL)) {
            $localPart = strstr($utilizator, '@', true);
            $nameParts = strtolower(str_replace('.', ' ', $localPart));
            $utilizator = preg_replace('/\.+/', '.', str_replace(' ', '.', $nameParts));
        }
        $idInstanta = sanitizeInput($_POST['idInstanta']);
        $idSectie = sanitizeInput($_POST['idSectie']);
        $idRol = sanitizeInput($_POST['idRol']);

        // Validate inputs
        // Check if username is provided
        if (empty($utilizator)) {
            echo json_encode(['status' => 'error', 'message' => 'Numele de utilizator este obligatoriu']);
            exit;
        }

        // Check if role is provided
        if (empty($idRol)) {
            echo json_encode(['status' => 'error', 'message' => 'Rolul este obligatoriu']);
            exit;
        }

        // If instance is empty, section should also be empty
        if (empty($idInstanta)) {
            $idSectie = ''; // Force section to be empty when instance is empty
            echo json_encode(['status' => 'error', 'message' => 'Instanța este obligatorie']);
            exit;
        }

        // If role is not 5 and instance is provided, section is required
        if ($idRol != 5 && !empty($idInstanta) && empty($idSectie)) {
            echo json_encode(['status' => 'error', 'message' => 'Secția este obligatorie pentru acest rol']);
            exit;
        }

        // Verify user has permission to add users for this instance
        if (!in_array($SESSION_id_rol, [1, 5]) && $idInstanta != $SESSION_id_instanta) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a adăuga utilizatori pentru această instanță']);
            exit;
        }

        // Check if user already exists
        $checkQuery = "SELECT * FROM exp_jud.utilizatori WHERE utilizator = :utilizator AND dataInactivare IS NULL";
        $stmt = $dbConnection->prepare($checkQuery);
        $stmt->execute([':utilizator' => $utilizator]);
        $rowCount = $stmt->rowCount();
        $stmt = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($rowCount > 0) {
            if ($stmt['idInstanta'] == null) {
                echo json_encode(['status' => 'error', 'message' => 'Utilizatorul există deja și este neasignat. Procedati la asignarea acestuia, in cadrul instantei dumneavoastra, prin filtrare dupa "Neasignați" sau cautare dupa nume cont.']);
                exit;
            }
            echo json_encode(['status' => 'error', 'message' => 'Utilizatorul există deja']);
            exit;
        }

// Verify role permissions
        $allowedRoles = [];
        switch ($SESSION_id_rol) {
            case 1: // Admin can add any role
                $allowedRoles = [1, 2, 3, 4, 5];
                break;
            case 3: // SPJC can only add Judecator and Grefier
                $allowedRoles = [2, 4];
                break;
            case 5: // SpIT can add Judecator, Grefier and SpIT
                $allowedRoles = [2, 4, 5];
                break;
        }

        if (!in_array($idRol, $allowedRoles)) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a adăuga acest rol']);
            exit;
        }

        $checkLDAPUserExists = checkLDAPUserExists($utilizator);
        if ($checkLDAPUserExists['statusLDAP'] === 'error') {
            echo json_encode(['status' => 'error', 'message' => $checkLDAPUserExists['messageLDAP']]);
            exit;
        }

        // Insert new user
        $insertQuery = "INSERT INTO exp_jud.utilizatori (utilizator, idInstanta, idSectieInstanta, idRol, uidActivare, dataActivare) VALUES (:utilizator, :idInstanta, :idSectieInstanta, :idRol, :uidActivare, :dataActivare)";
        $stmt = $dbConnection->prepare($insertQuery);
        $stmt->execute([
            'utilizator' => $utilizator,
            'idInstanta' => $idInstanta,
            'idSectieInstanta' => $idSectie,
            'idRol' => $idRol,
            'uidActivare' => $SESSION_id_utilizator,
            'dataActivare' => date('Y-m-d H:i:s')
        ]);

        // Get the new user ID for the audit log
        $newUserId = $dbConnection->lastInsertId();

        // Log the user creation action
        log_admin_action($dbConnection, 'user_create', [
            'affected_user_id' => $newUserId,
            'table_name' => 'utilizatori',
            'record_id' => $newUserId,
            'description' => "Creare utilizator nou: $utilizator cu rolul $idRol la instanța $idInstanta",
            'data_after' => json_encode([
                'utilizator' => $utilizator,
                'idInstanta' => $idInstanta,
                'idSectieInstanta' => $idSectie,
                'idRol' => $idRol
            ])
        ]);

        echo json_encode(['status' => 'success']);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (addUser): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la salvarea datelor']);
        exit;
    }
}
if ($_POST['action'] === 'editUser') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta, $SESSION_id_utilizator;

        $userId = sanitizeInput($_POST['userId']);
        $utilizator = sanitizeInput($_POST['utilizator']);
        $idInstanta = sanitizeInput($_POST['idInstanta']);
        $idRol = sanitizeInput($_POST['idRol']);
        $idSectie = sanitizeInput($_POST['idSectie']);

        // Check if username is provided
        if (empty($utilizator)) {
            echo json_encode(['status' => 'error', 'message' => 'Numele de utilizator este obligatoriu']);
            exit;
        }

        // Check if role is provided
        if (empty($idRol)) {
            echo json_encode(['status' => 'error', 'message' => 'Rolul este obligatoriu']);
            exit;
        }

        if ($idInstanta === '00') {
            $idInstanta = null;
            $idSectie = null;
        }else if ($idInstanta === '0' || $idInstanta == null) {
            echo json_encode(['status' => 'error', 'message' => 'Instanța este obligatorie']);
            exit;
        }

        if (!in_array($idRol, [1, 5])  && !empty($idInstanta) && empty($idSectie)) {
            echo json_encode(['status' => 'error', 'message' => 'Secția este obligatorie pentru acest rol']);
            exit;
        }

        if (!in_array($SESSION_id_rol, [1, 5]) && $idInstanta != $SESSION_id_instanta) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a edita utilizatori pentru această instanță']);
            exit;
        }

        if ($userId == $SESSION_id_utilizator) {
            echo json_encode(['status' => 'error', 'message' => 'Nu puteți edita contul curent']);
            exit;
        }

        $checkQuery = "SELECT id FROM exp_jud.utilizatori WHERE utilizator = :utilizator AND id != :userId AND dataInactivare IS NULL";
        $stmt = $dbConnection->prepare($checkQuery);
        $stmt->execute(['utilizator' => $utilizator, 'userId' => $userId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode(['status' => 'error', 'message' => 'Utilizatorul există deja']);
            exit;
        }

        // Get user data before update for audit log
        $getUserQuery = "SELECT utilizator, idInstanta, idSectieInstanta, idRol FROM exp_jud.utilizatori WHERE id = :userId";
        $stmtGet = $dbConnection->prepare($getUserQuery);
        $stmtGet->execute(['userId' => $userId]);
        $userData = $stmtGet->fetch(PDO::FETCH_ASSOC);

        $updateQuery = "UPDATE exp_jud.utilizatori SET utilizator = :utilizator, idInstanta = :idInstanta, idRol = :idRol, idSectieInstanta = :idSectie WHERE id = :userId";
        $stmt = $dbConnection->prepare($updateQuery);
        $stmt->execute([
            'utilizator' => $utilizator,
            'idInstanta' => $idInstanta,
            'idSectie' => $idSectie,
            'idRol' => $idRol,
            'userId' => $userId
        ]);

        // Log the user update action
        log_admin_action($dbConnection, 'user_update', [
            'affected_user_id' => $userId,
            'table_name' => 'utilizatori',
            'record_id' => $userId,
            'description' => "Actualizare utilizator: $utilizator",
            'data_before' => json_encode($userData),
            'data_after' => json_encode([
                'utilizator' => $utilizator,
                'idInstanta' => $idInstanta,
                'idSectieInstanta' => $idSectie,
                'idRol' => $idRol
            ])
        ]);

        echo json_encode(['status' => 'success']);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (editUser): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la salvarea datelor']);
        exit;
    }
}
if ($_POST['action'] === 'deactivateUser') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta, $SESSION_id_utilizator;

        global $date;
        $userId = sanitizeInput($_POST['userId']);
        if ($userId == $SESSION_id_utilizator) {
            echo json_encode(['status' => 'error', 'message' => 'Nu puteți inactiva contul curent']);
            exit;
        }

        $userQuery = "SELECT idInstanta FROM exp_jud.utilizatori WHERE id = :userId";
        $stmt = $dbConnection->prepare($userQuery);
        $stmt->execute(['userId' => $userId]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!in_array($SESSION_id_rol, [1, 5]) && $userData['idInstanta'] != $SESSION_id_instanta) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a inactiva acest utilizator']);
            exit;
        }

        // Get user data before deactivation for audit log
        $getUserQuery = "SELECT utilizator, idInstanta, idSectieInstanta, idRol FROM exp_jud.utilizatori WHERE id = :userId";
        $stmtGet = $dbConnection->prepare($getUserQuery);
        $stmtGet->execute(['userId' => $userId]);
        $userData = $stmtGet->fetch(PDO::FETCH_ASSOC);

        $deactivateQuery = "UPDATE exp_jud.utilizatori SET dataInactivare = '$date', uidInactivare = :uidInactivare WHERE id = :userId";
        $stmt = $dbConnection->prepare($deactivateQuery);
        $stmt->execute([
            'userId' => $userId,
            'uidInactivare' => $SESSION_id_utilizator
        ]);

        // Log the user deactivation action
        log_admin_action($dbConnection, 'user_deactivate', [
            'affected_user_id' => $userId,
            'table_name' => 'utilizatori',
            'record_id' => $userId,
            'description' => "Ștergere utilizator: {$userData['utilizator']}",
            'data_before' => json_encode($userData),
            'data_after' => json_encode([
                'dataDeactivate' => $date,
                'uidDeactivate' => $SESSION_id_utilizator
            ])
        ]);

        echo json_encode(['status' => 'success']);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (deactivateUser): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la dezactivarea contului']);
        exit;
    }
}
if ($_POST['action'] === 'activateUser') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta;

        $userId = sanitizeInput($_POST['userId']);

        $userQuery = "SELECT idInstanta FROM exp_jud.utilizatori WHERE id = :userId";
        $stmt = $dbConnection->prepare($userQuery);
        $stmt->execute(['userId' => $userId]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!in_array($SESSION_id_rol, [1, 5]) && $userData['idInstanta'] != $SESSION_id_instanta) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a activa acest utilizator']);
            exit;
        }

        // Get user data before activation for audit log
        $getUserQuery = "SELECT utilizator, idInstanta, idSectieInstanta, idRol FROM exp_jud.utilizatori WHERE id = :userId";
        $stmtGet = $dbConnection->prepare($getUserQuery);
        $stmtGet->execute(['userId' => $userId]);
        $userData = $stmtGet->fetch(PDO::FETCH_ASSOC);

        $activateQuery = "UPDATE exp_jud.utilizatori SET dataInactivare = NULL WHERE id = :userId";
        $stmt = $dbConnection->prepare($activateQuery);
        $stmt->execute(['userId' => $userId]);

        // Log the user activation action
        log_admin_action($dbConnection, 'user_activate', [
            'affected_user_id' => $userId,
            'table_name' => 'utilizatori',
            'record_id' => $userId,
            'description' => "Activare utilizator: {$userData['utilizator']}",
            'data_before' => json_encode($userData),
            'data_after' => json_encode([
                'dataInactivare' => null
            ])
        ]);

        echo json_encode(['status' => 'success']);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (activateUser): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la activarea contului']);
        exit;
    }
}
if ($_POST['action'] === 'unasignUser') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta;

        $userId = sanitizeInput($_POST['userId']);

        if (!in_array($SESSION_id_rol, [1, 5])) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a muta acest utilizator']);
            exit;
        }

        // Get user data before unasigning for audit log
        $getUserQuery = "SELECT utilizator, idInstanta, idSectieInstanta, idRol FROM exp_jud.utilizatori WHERE id = :userId";
        $stmtGet = $dbConnection->prepare($getUserQuery);
        $stmtGet->execute(['userId' => $userId]);
        $userData = $stmtGet->fetch(PDO::FETCH_ASSOC);

        $unasignQuery = "UPDATE exp_jud.utilizatori SET idInstanta = NULL, idSectieInstanta = NULL WHERE id = :userId";
        $stmt = $dbConnection->prepare($unasignQuery);
        $stmt->execute(['userId' => $userId]);

        // Log the user unasign action
        log_admin_action($dbConnection, 'user_unasign', [
            'affected_user_id' => $userId,
            'table_name' => 'utilizatori',
            'record_id' => $userId,
            'description' => "Mutare utilizator în useri neasignați: {$userData['utilizator']}",
            'data_before' => json_encode($userData),
            'data_after' => json_encode([
                'idInstanta' => null,
                'idSectieInstanta' => null
            ])
        ]);

        echo json_encode(['status' => 'success']);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (unasignUser): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la mutarea utilizatorului']);
        exit;
    }
}
// getUnassignedUsers action is implemented below
if ($_POST['action'] === 'assignUser') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta;

        $userId = sanitizeInput($_POST['userId']);
        if (!in_array($SESSION_id_rol, [1, 5])) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a asigna acest utilizator']);
            exit;
        }

        // Get user data before assignment for audit log
        $getUserQuery = "SELECT utilizator, idInstanta, idSectieInstanta, idRol FROM exp_jud.utilizatori WHERE id = :userId";
        $stmtGet = $dbConnection->prepare($getUserQuery);
        $stmtGet->execute(['userId' => $userId]);
        $userData = $stmtGet->fetch(PDO::FETCH_ASSOC);

        $assignQuery = "UPDATE exp_jud.utilizatori SET idInstanta = :idInstanta, idSectieInstanta = :idSectie WHERE id = :userId";
        $stmt = $dbConnection->prepare($assignQuery);
        $stmt->execute([
            'idInstanta' => $SESSION_id_instanta,
            'idSectie' => null,
            'userId' => $userId
        ]);

        // Log the user assignment action
        log_admin_action($dbConnection, 'user_assign', [
            'affected_user_id' => $userId,
            'table_name' => 'utilizatori',
            'record_id' => $userId,
            'description' => "Asignare utilizator la instanța: {$userData['utilizator']}",
            'data_before' => json_encode($userData),
            'data_after' => json_encode([
                'idInstanta' => $SESSION_id_instanta,
                'idSectieInstanta' => null
            ])
        ]);

        echo json_encode(['status' => 'success']);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (assignUser): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la asignarea utilizatorului']);
        exit;
    }
}

if ($_POST['action'] === 'getUnassignedUsers') {
    try {
        global $SESSION_id_rol, $SESSION_id_instanta;

        // Only admin and SpIT can see unassigned users
        if (!in_array($SESSION_id_rol, [1, 5])) {
            echo json_encode(['status' => 'error', 'message' => 'Nu aveți permisiunea de a vedea utilizatorii neasignați']);
            exit;
        }

        // Get pagination parameters from DataTables
        $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
        $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
        $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
        $searchValue = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';

        // Base query for unassigned users
        $baseQuery = "SELECT u.id, u.utilizator
                      FROM exp_jud.utilizatori u
                      JOIN exp_jud.z_rol_utilizator zrl ON zrl.id = u.idRol
                      WHERE u.idInstanta IS NULL
                      AND u.dataInactivare IS NULL";

        // Add search condition if search value is provided
        $searchCondition = '';
        if (!empty($searchValue)) {
            $searchCondition = " AND u.utilizator LIKE :searchValue";
        }

        // Count total records
        $countQuery = "SELECT COUNT(*) as total FROM exp_jud.utilizatori u WHERE u.idInstanta IS NULL AND u.dataInactivare IS NULL";
        $stmt = $dbConnection->prepare($countQuery);
        $stmt->execute();
        $totalRecords = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Count filtered records
        $filteredCountQuery = $countQuery;
        if (!empty($searchCondition)) {
            $filteredCountQuery .= $searchCondition;
            $stmt = $dbConnection->prepare($filteredCountQuery);
            $stmt->bindValue(':searchValue', "%$searchValue%", PDO::PARAM_STR);
        } else {
            $stmt = $dbConnection->prepare($filteredCountQuery);
        }
        $stmt->execute();
        $filteredRecords = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get data
        $dataQuery = $baseQuery . $searchCondition . " ORDER BY u.utilizator ASC LIMIT :length OFFSET :start";
        $stmt = $dbConnection->prepare($dataQuery);
        if (!empty($searchCondition)) {
            $stmt->bindValue(':searchValue', "%$searchValue%", PDO::PARAM_STR);
        }
        $stmt->bindValue(':length', $length, PDO::PARAM_INT);
        $stmt->bindValue(':start', $start, PDO::PARAM_INT);
        $stmt->execute();
        $dataStmt = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $data = [];
        foreach ($dataStmt as $row) {
            $data[] = [
                'utilizator' => htmlspecialchars($row['utilizator']),
                'actiuni' => "<button class='btn btn-sm btn-outline-primary btn-assign-user'
                                data-bs-toggle='tooltip'
                                data-bs-html='true'
                                data-bs-placement='left'
                                title='Asignează utilizator'
                                data-user-id='{$row['id']}'
                                data-username='" . htmlspecialchars($row['utilizator']) . "'>
                                <i class='fa fa-user-check'></i>
                            </button>"
            ];
        }

        $response = [
            "draw" => $draw,
            "recordsTotal" => $totalRecords,
            "recordsFiltered" => $filteredRecords,
            "data" => $data,
        ];

        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response);
        exit;
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in administrareUseri.php (getUnassignedUsers): " . $e->getMessage()
        ]);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la obținerea utilizatorilor neasignați']);
        exit;
    }
}