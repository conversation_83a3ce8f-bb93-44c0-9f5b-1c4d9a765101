<?php
global $env;
header('Content-Type: application/json; charset=UTF-8');

// Include session check helper which will verify session validity
require_once '../inc/session_check.php';

require_once __DIR__ . '/../vendor/autoload.php';

use Mpdf\Mpdf;

require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once '../inc/cfg_db.php';
require_once '../inc/EmailService.php';


global $conn_EXP_TEHNICI_OLD, $pool_EXP_TEHNICI_OLD, $date, $SESSION_id_judet, $SESSION_id_instanta, $SESSION_instanta, $SESSION_id_utilizator;

if ($conn_EXP_TEHNICI_OLD === null) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Nu s-a putut stabili conexiunea la baza de date.'
    ]);
    exit;
}

$status = 'error';
$message = '';

if (isset($_POST['getSpecializari'])) {
    $status = 'ok';
    $message = getSpecializariSelectOptgroup();
}

if (isset($_POST['checkDosar'])) {
    try {
        header('Content-Type: application/json');

        if (empty($SESSION_id_instanta) || empty($SESSION_id_judet)) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Datele instanței sau ale județului lipsesc din sesiune.'
            ]);
            exit();
        }

        $nrDosar = strtolower(trim($_POST['nrDosar'] ?? ''));
        if (empty($nrDosar)) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Numărul dosarului este invalid sau lipsă.'
            ]);
            exit();
        }

        $raw = $_POST['specializariAlese'] ?? '[]';
        $specializariArray = json_decode($raw, true);

        if (!is_array($specializariArray) || empty($specializariArray)) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Nu a fost selectată nicio specializare.'
            ]);
            exit();
        }

        // Normalizează array-ul: [1,2,3,4] => [[1,2],[3,4]]
        $specializariSelect = [];
        foreach ($specializariArray as $item) {
            if (is_array($item)) {
                foreach ($item as $value) {
                    $specializariSelect[] = (int)$value;
                }
            } else {
                $specializariSelect[] = (int)$item;
            }
        }

        $formattedArray = [];
        $count = count($specializariSelect);
        for ($i = 0; $i < $count; $i += 2) {
            $main = $specializariSelect[$i] ?? null;
            $sub = $specializariSelect[$i + 1] ?? 0;
            if ($main !== null) {
                $formattedArray[] = [$main, $sub];
            }
        }

        if (empty($formattedArray)) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Specializările nu sunt valide.'
            ]);
            exit();
        }

        // 1. Verificăm dacă există deja un expert alocat
        $expAlocat = checkExpertAlocat($nrDosar, $formattedArray);
        if (!is_array($expAlocat)) {
            $expAlocat = [];
        }

        if (count($expAlocat) > 0) {
            echo json_encode([
                'status' => 'warning',
                'message' => 'Există deja un expert alocat în acest dosar cu specializarea selectată. Doriți să îl înlocuiți?'
            ]);
            exit();
        }
        $cnpAlocati = [];
        foreach ($expAlocat as $expert) {
            if (!empty($expert['CNP'])) {
                $cnpAlocati[] = $expert['CNP'];
            }
        }
        $cnpAlocatiList = implode(",", array_map(fn($c) => "'$c'", $cnpAlocati));
        if (empty($cnpAlocatiList)) {
            $cnpAlocatiList = "''";
        }
        $nrExperti = isset($_POST['nrExperti']) ? filter_input(INPUT_POST, 'nrExperti', FILTER_SANITIZE_NUMBER_INT) : null;
        $experti = null;
        if ($nrExperti == 1) {
            $experti = "a fost desemnat aleatoriu, în calitate de expert,";
        } else if ($nrExperti == 3) {
            $experti = "au fost desemnați aleatoriu, în calitate de experți,";
        } else if ($nrExperti == 11) {
            $experti = "a fost desemnat aleatoriu, în calitate de expert,";
            $nrExperti = 1;
        } else {
            throw new Exception('Eroare nr experti.');
        }
        // 2. Căutăm experți eligibili
        $result = lottery($SESSION_id_instanta, $SESSION_id_judet, $formattedArray, $nrExperti, $cnpAlocatiList);
        $expertRandom = $result['experti'];
        $output = $result['output'];
        $mesajS = 'Niciun expert judiciar identificat la nivel național.';
        if (count($expertRandom) === 0 && $nrExperti == 3) {
            $mesajS = 'Nu au fost identificați 3 experți judiciari la nivel național.';
        }
        if (!is_array($expertRandom) || count($expertRandom) === 0) {
            echo json_encode([
                'status' => 'error',
                'message' => $mesajS
            ]);
            exit();
        } else {
            // 3. Totul este în regulă
            echo json_encode([
                'status' => 'ok',
                'message' => "$output. A fost găsit numărul necesar de experți."
            ]);
            exit();
        }

    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Eroare la verificare: ' . $e->getMessage()
        ]);
    }

}


if (isset($_POST['inlocuireExpertInComisie'])) {
//    se intra prin pagina inlocuire.php > inlocuieste expertul dintr-o comisie de 3
    if (
        !isset($_POST['eid']) //nu e setat idul expertizei
        || (!isset($_POST['cnp']) && !isset($_POST['inlocuireRandomExpert']))//lipseste cnp-ul si nu e setat inlocuireRandomExpert
    ) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Eroare la verificare: Nu exista ID-ul expertizei sau CNP-ul expertului.'
        ]);
        exit();
    }

    $dbConnection = null;
    try {
        require_once '../inc/cfg_db.php';
        $dbConnection = DatabasePool::getConnection();

        $idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);
        $eid = $idEncoder->decode(sanitizeInput($_POST['eid']));

        $selectRowExpertiza = "SELECT * FROM exp_jud.expertize WHERE id = :eid";
        $stmt = $dbConnection->prepare($selectRowExpertiza);
        $stmt->execute([':eid' => $eid]);
        $rowExpertiza = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$rowExpertiza) {
            throw new Exception('Expertiza nu a fost găsită');
        }

        if ($rowExpertiza['idStatusExpertiza'] != 1) {
            throw new Exception('Expertiza nu (mai) este activă.');
        }

        $cnpAlocatiInDosar = getCNPAlocatiInDosar($rowExpertiza['nrDosar']);
        //get random cnp for expert aleatoriu
        $newExpert = lottery($SESSION_id_instanta, $SESSION_id_judet, [[$rowExpertiza['idSpecializare'], $rowExpertiza['idSubspecializare']]], 1, implode(",", array_map(fn($c) => "'$c'", $cnpAlocatiInDosar)));
        $newCNP = $newExpert['experti'][0]['CNP'];
        if (isset($_POST['inlocuireConvenitExpert'])) {
            //get expert data for expert convenit
            $newCNP = sanitizeInput($_POST['cnp']);
            $newExpertData = getExpertData($newCNP);
            if ($newExpertData == null) {
                throw new Exception('<b>Expertul căutat nu există.</b>');
            }
            if (in_array($newCNP, $cnpAlocatiInDosar)) {
                throw new Exception('<b>Expertul căutat a fost deja alocat în acest dosar. Vă rugăm selectați un alt expert.</b>');
            }
        }

        $expertizaData = getExpertizaDataById($eid);
        $nrDosar = $expertizaData['nrDosar'];
        $onorariu = $expertizaData['onorariu'];
        $nrExperti = 1;//cazul 1, 3, sau 11 - in cazul acesta `1` inlocuieste un singur expert

        //verifica daca cnp-ul nou are specializarea din dosarul dat
        $specializariNewCNP = getSpecializariExpertDAETJ($newCNP);
        $specializareaCeTrebuieSaOAibaExpNou = [$expertizaData['idSpecializare'], $expertizaData['idSubspecializare']];

        // verifica daca cnp-ul are specializarea si subspecializarea - in cazul in care se foloseste un cnp convenit
        $areSpecializarea = in_array($specializareaCeTrebuieSaOAibaExpNou[0], $specializariNewCNP['specializari']);
        $areSubspecializarea = in_array($specializareaCeTrebuieSaOAibaExpNou[1], $specializariNewCNP['subspecializari']);

        if ($newCNP == $expertizaData['cnpExpert']) {
            throw new Exception('<b>Expertul căutat a fost deja alocat în acest dosar. Vă rugăm selectați un alt expert.</b>');
        }

        //daca nu are fie specializarea fie subspecializarea
        if (!$areSpecializarea || !$areSubspecializarea) {
            throw new Exception('<b>Expertul căutat nu are specializarea necesară pentru acest dosar. Vă rugăm selectați un alt expert.</b>');
        }

        //totul ok -> facem insert expert nou / inlocuire expert vechi / increment si decrement workload / send email / logs

        //status expertiza -> inlocuit
        $updateSql = "UPDATE exp_jud.expertize
                  SET idStatusExpertiza = 2
                  WHERE nrDosar = :nrDosar
                  AND cnpExpert = :cnpExpert
                  AND idSpecializare = :specializare
                  AND idSubSpecializare = :subspecializare
                  AND idStatusExpertiza = 1";
        $stmt = $dbConnection->prepare($updateSql);
        $stmt->execute([
            ':nrDosar' => $nrDosar,
            ':cnpExpert' => $expertizaData['cnpExpert'],
            ':specializare' => $expertizaData['idSpecializare'],
            ':subspecializare' => $expertizaData['idSubspecializare']
        ]);

        //send email inlocuire expert
        $emailService = new EmailService($dbConnection);
        $expertData = getExpertData($expertizaData['cnpExpert']);
        $specializariStringInlocuireStringNume = implode(', ', $specializareaCeTrebuieSaOAibaExpNou);
        $emailService->sendEmailInlocuireExpert($expertData, $nrDosar, $specializariStringInlocuireStringNume);
        $emailService->sendEmailInlocuireBLET($expertData, $nrDosar, $specializariStringInlocuireStringNume);

        log_data_operation($dbConnection, 'update', 'expertize', null, [
            'description' => "Actualizare status expertiză 2 (inlocuit in comisia de trei) pentru dosarul $nrDosar",
            'data_after' => [
                'nrDosar' => $nrDosar,
                'cnpExpert' => $expertizaData['cnpExpert'],
                'idStatusExpertiza' => 2,
                'specializare' => $expertizaData['idSpecializare'],
                'subspecializare' => $expertizaData['idSubspecializare']
            ]
        ]);

        $verificaExistentaInWorkload = verificaExistentaInWorkload($expertizaData['cnpExpert']);
        if (count($verificaExistentaInWorkload) > 0) {
            $minus = "update exp_jud.workload set incarcatura_app = incarcatura_app -1 where CNP = '$expertizaData[cnpExpert]' AND idLoad > 0";
            $dbConnection->exec($minus);
        }
        log_data_operation($dbConnection, 'update', 'workload', null, [
            'description' => "Decrementare încărcătură -1 (inlocuire) expert pentru dosarul $nrDosar",
            'data_after' => ['CNP' => $expertizaData['cnpExpert']]
        ]);

        // Insert new expert convenit ce inlocuieste pe cel anterior
        $sql = "INSERT INTO exp_jud.expertize (nrDosar, cnpExpert, idSpecializare, idSubSpecializare, onorariu, idUtilizatorInsert, dataDesemnare, idStatusExpertiza) VALUES ";
        $values = "('$nrDosar', '$newCNP', '$expertizaData[idSpecializare]', '$expertizaData[idSubspecializare]', '$onorariu', '$SESSION_id_utilizator', NOW(), '1')";
        $sql .= $values . ";";
        $dbConnection->exec($sql);
        $idExpertiza = $dbConnection->lastInsertId();

        // Send email notification
        $emailService = new EmailService($dbConnection);
        $expertData = getExpertData($newCNP);
        $emailService->sendEmailCatreExpert($expertData, $nrDosar, $specializariStringInlocuireStringNume, $onorariu);
        $emailService->sendEmailCatreBLET($nrDosar, $specializariStringInlocuireStringNume, $onorariu, 1, $expertData);

        log_data_operation($dbConnection, 'insert', 'expertize', $idExpertiza, [
            'description' => "Adăugare expert convenit pentru dosarul $nrDosar",
            'data_after' => [
                'nrDosar' => $nrDosar,
                'cnpExpert' => $newCNP,
                'specializări' => getTextSpecializari($expertizaData['idSpecializare'], $expertizaData['idSubspecializare'], $specializareaCeTrebuieSaOAibaExpNou),
                'onorariu' => $onorariu
            ]
        ]);

        $verificaExistentaInWorkload = verificaExistentaInWorkload($newCNP);
        if (count($verificaExistentaInWorkload) > 0) {
            $plus = "update exp_jud.workload set incarcatura_app = incarcatura_app +1 where CNP = '$newCNP' AND idLoad > 0";
            $dbConnection->exec($plus);
        }

        log_data_operation($dbConnection, 'update', 'workload', null, [
            'description' => "Incrementare încărcătură +1 expert pentru dosarul $nrDosar",
            'data_after' => ['CNP' => $newCNP]
        ]);

        DatabasePool::releaseConnection($dbConnection);

        $status = 'ok';
        $message = "<a href='controller/generateExpertPDF.php?id=" . $idEncoder->encode($idExpertiza) . "' target='_blank' class='text-danger'
                    data-bs-toggle='tooltip' data-bs-placement='left' data-bs-html='true' title='Vizualizare expert desemnat'>
                    <i class='fa fa-file'></i> Click aici pentru a descărca fișierul PDF</a>";
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Eroare: ' . $e->getMessage()
        ]);
        exit();
    } finally {
        if ($dbConnection !== null) {
            DatabasePool::releaseConnection($dbConnection);
        }
    }

}

if (isset($_POST['randomExpert'])) {
    try {
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'tempDir' => __DIR__ . '/tmp', // Set a temp directory
        ]);

        $nrDosar = isset($_POST['nrDosar']) ? strtolower(sanitizeInput($_POST["nrDosar"])) : null;
        $onorariu = isset($_POST['onorariu']) ? filter_var($_POST['onorariu'], FILTER_VALIDATE_INT) : null;
        $specializariArray = json_decode(sanitizeInput($_POST['specializariAlese']), true);
        $nrExperti = isset($_POST['nrExperti']) ? filter_input(INPUT_POST, 'nrExperti', FILTER_SANITIZE_NUMBER_INT) : null;

        if ($nrDosar === false || $nrDosar === null) {
            $message .= "Completati numarul de dosar!<br>";
        }
        if ($onorariu === false || $onorariu === null || $onorariu <= 0) {
			$message .= "Onorariul trebuie să fie > zero!<br>";
		}
        if (!is_array($specializariArray) || empty($specializariArray)) {
            $message .= "Nu a fost selectată nicio specializare!<br>";
        }
        if ($nrExperti === false || $nrExperti === null) {
            $message .= "Alegeti numarul de experti!<br>";
        }

        if ($message) {
            echo json_encode([
                'status' => 'error'
                , 'message' => $message
            ]);
            exit();
        }

        $specializariSelect = [];
        foreach ($specializariArray as $item) {
            if (is_array($item)) {
                foreach ($item as $value) {
                    $specializariSelect[] = intval($value);
                }
            } else {
                $specializariSelect[] = intval($item);
            }
        }
        if (empty($specializariSelect)) {
            echo json_encode([
                'status' => 'error'
                , 'message' => 'Nu a fost selectată nicio specializare.'
            ]);
            exit();
        }

        $specializareaCeTrebuieSaOAibaExpNou = [];
        $sql = getSpecializari(); //[184=>0,204=>68]
        foreach ($specializariArray as $pair) {
            if (!is_array($pair) || count($pair) < 2) {
                error_log('Invalid pair structure: ' . print_r($pair, true));
                continue;
            }

            $specializareId = $pair[0];
            $subspecializareId = $pair[1];
            $found = false;
            foreach ($sql as $row) {
                if ($specializareId == $row['id_specializare']) {
                    if ($subspecializareId == 0) {
                        $specializareaCeTrebuieSaOAibaExpNou[] = "<b>{$row['nume_specializare']}</b>";
                    } else if ($subspecializareId == $row['id_subspecializare']) {
                        $specializareaCeTrebuieSaOAibaExpNou[] = "<b>{$row['nume_specializare']}</b>";
                        $specializareaCeTrebuieSaOAibaExpNou[] = $row['nume_subspecializare'];
                    } else {
                        continue; // Continue searching for matching subspecializare
                    }

                    $found = true;
                    break;
                }
            }

        }

        $articulatSpecializari = $articulatSpecializari2 = 'specializarea';
        if (count($specializareaCeTrebuieSaOAibaExpNou) > 1) {
            $articulatSpecializari = 'specializările';
            $articulatSpecializari2 = 'Specializări';
        }

        $formattedArray = []; //[[184,0],[204,68],[306,90]]
        foreach ($specializariSelect as $i => $value) {
            if ($i % 2 == 0) {
                $formattedArray[] = [$value, $specializariSelect[$i + 1]];
            }
        }

        $experti = null;
        if ($nrExperti == 1) {
            $experti = "a fost desemnat aleatoriu, în calitate de expert,";
        } else if ($nrExperti == 3) {
            $experti = "au fost desemnați aleatoriu, în calitate de experți,";
        } else if ($nrExperti == 11) {
            $experti = "a fost desemnat aleatoriu, în calitate de expert,";
            $nrExperti = 1;
        } else {
            throw new Exception('Eroare nr experti.');
        }
        $expAlocat = checkExpertAlocat($nrDosar, $formattedArray);
        $cnpAlocati = [];
        foreach ($expAlocat as $expert) {
            if (!empty($expert['CNP'])) {
                $cnpAlocati[] = $expert['CNP'];
            }
        }
        $cnpAlocatiList = implode(",", array_map(fn($c) => "'$c'", $cnpAlocati));
        if (empty($cnpAlocatiList)) {
            $cnpAlocatiList = "''";
        }
        $result = lottery($SESSION_id_instanta, $SESSION_id_judet, $formattedArray, $nrExperti, $cnpAlocatiList);
        $expertRandom = $result['experti'];
        $output = $result['output'];
        if (!$expertRandom && count($expAlocat) > 0) {
            echo json_encode([
                'status' => 'error'
                , 'message' => 'Nu există un alt expert cu care sa fie facută înlocuirea.'
            ]);
            exit();
        }

        $tdExperti = $expertNumeConcat = null;
        $values = $expertFullNames = [];
        foreach ($expertRandom as $ex) {
            $expertNume = $ex['nume'];
            $expertPrenume = $ex['prenume'];
            $expertFullNames[] = "$expertNume $expertPrenume";
            $expertCNP = anonimizareCNP($ex['CNP']);
            $expertJudet = $ex['judet'];
            $expertLocalitate = $ex['localitate'];
            $legitimatie = $ex['nr_legitimatie'];
            $idSpecialziari = $idSuspecializari = "";
            foreach ($specializariSelect as $i => $value) {
                if ($i % 2 == 0) {
                    $idSpecialziari .= $value . ",";
                    $idSuspecializari .= $specializariSelect[$i + 1] . ",";
                }
            }
            $idSpecialziari = rtrim($idSpecialziari, ',');
            $idSuspecializari = rtrim($idSuspecializari, ',');
            $values[] = "('$nrDosar', '$ex[CNP]', '$idSpecialziari', '$idSuspecializari', '$onorariu', '$SESSION_id_utilizator', NOW(), '1')";
            $tdExperti .= "
            <p><strong>Nume Prenume: </strong>$expertNume $expertPrenume</p>
            <p><strong>CNP: </strong>$expertCNP</p>
            <p><strong>Județ: </strong>$expertJudet</p>
            <p><strong>Localitate: </strong>$expertLocalitate</p>
            <p><strong>Autorizație: </strong>$legitimatie</p>
            <br>";
        }

        $expertNumeConcat = implode(', ', $expertFullNames);
        $specializariNewCNP = implode(', ', $specializareaCeTrebuieSaOAibaExpNou);

        $html = "
        <div style='width: 100%; text-align: center;'>
            <h3>$SESSION_instanta</h3>
            <p>" . data_afisare($date) . "</p>
        </div>
            <p style='text-align: center;'>În dosarul nr. <strong>$nrDosar</strong>, $experti</p>
            <p style='text-align: center;'><b>$expertNumeConcat</b></p>
            <p style='text-align: center;'>Având $articulatSpecializari: <strong>$specializariNewCNP</strong></p>
        </div>";


        if (count($expAlocat) == 1) // dacă a mai fost alocat un singur expert cu specializarea asta
        {
            $html .= "
            <div style='margin-top: 20px;'>
                <p style='text-align: center;'>Expertul {$expAlocat[0]['nume']} {$expAlocat[0]['prenume']} a fost înlocuit.</p>
            </div>";
        } else if (count($expAlocat) > 1) // mai multi
        {
            $html .= "
            <div style='margin-top: 20px;'>
                <p style='text-align: center;'>Experții desemnați anterior în dosar:</p>
                <ul style='text-align: center; list-style-type: none; padding: 0;'>";

            $numeExpInlocuitiArray = [];
            foreach ($expAlocat as $expert) {
                $nume = htmlspecialchars($expert['nume']);
                $prenume = htmlspecialchars($expert['prenume']);
                $numeExpInlocuitiArray[] = "$nume $prenume";
            }
            $numeExpInlocuitiArray = array_unique($numeExpInlocuitiArray);
            $html .= implode(", ", $numeExpInlocuitiArray);
            $html .= "
                </ul>
            </div>";
        }

        $html .= "
        <div style='margin-top: 20px;'>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr>
                    <th style='width: 49%; border: 1px solid #000; padding: 8px;'>Detalii lucrare</th>
                    <th style='width: 50%; border: 1px solid #000; padding: 8px;'>Detalii expert</th>
                </tr>
                <tr>
                    <td style='border: 1px solid #000; padding: 8px;'>
                        <p><strong>Număr dosar: </strong>$nrDosar</p>
                        <p><strong>" . ucfirst($articulatSpecializari2) . ": </strong>$specializariNewCNP</p>
                        <p><strong>Onorariu: </strong>$onorariu RON</p>
                        <p><strong>Data desemnării: </strong>" . data_afisare(date('Y-m-d H:i:s')) . "</p>
                    </td>
                    <td style='border: 1px solid #000; padding: 8px;'>
                        $tdExperti
                    </td>
                </tr>
            </table>
        </div>";
        if ($output) {
            $output .= "A fost găsit numărul necesar de experți.";
            $html .= "
            <div style='position: fixed; bottom: 0; left: 0; right: 0; padding: 10px 20px; border-top: 1px dashed #888; background-color: #f9f9f9; font-family: Arial, sans-serif; font-size: 12px;'>
                <div style='white-space: pre-wrap;'>
                    " . nl2br(htmlspecialchars($output)) . "
                </div>
            </div>";
        }

        //facem insert
        require_once '../inc/cfg_db.php';
        $dbConnection = DatabasePool::getConnection();
        $CNP_Random = '';
        foreach ($expertRandom as $expert) {
            $CNP_Random .= "'$expert[CNP]',";
        }
        $CNP_Random = rtrim($CNP_Random, ',');
        $verificaExistentaInWorkload = verificaExistentaInWorkload($CNP_Random);
        if (count($verificaExistentaInWorkload) > 0) {
            $plus = "update exp_jud.workload set incarcatura_app = incarcatura_app +1 where CNP IN ($CNP_Random) AND idLoad > 0";
            $dbConnection->exec($plus);
        }

        log_data_operation($dbConnection, 'update', 'workload', null, [
            'description' => "Incrementare încărcătură +1 experți (alocare random) pentru dosarul $nrDosar",
            'data_after' => ['CNP' => $CNP_Random]
        ]);

        $update = NULL;
        if (count($expAlocat) >= 1) {
            //are expert deja alocat si il inlocuim
            $CNP_URI = "";
            foreach ($expAlocat as $expert) {
                $CNP_URI .= "'$expert[CNP]',";
            }
            $CNP_URI = rtrim($CNP_URI, ',');

            $conditii = [];
            foreach ($formattedArray as [$spec, $subspec]) {
                $conditii[] = "($spec, $subspec)";
            }
            $tupleList = implode(',', $conditii);
            verificaExistentaInWorkload($CNP_Random);
            $update = "UPDATE exp_jud.expertize
            SET idStatusExpertiza = 2
            WHERE nrDosar = '$nrDosar'
            AND cnpExpert IN ($CNP_URI)
            AND (idSpecializare, idSubspecializare) IN ($tupleList)
            AND id > 0;
            UPDATE exp_jud.workload
            SET incarcatura_app = incarcatura_app - 1
            WHERE CNP IN ($CNP_URI) AND idLoad > 0;";
        }
        if ($update) {
            //facem inlocuirea expertului / expertilor alocati deja
            $dbConnection->exec($update);

            $emailService = new EmailService($dbConnection);
            foreach ($expAlocat as $expert) {
                $getExpertData = getExpertData($expert['CNP']);
                $expertData = [
                    'cnp' => $expert['CNP'],
                    'nume' => $getExpertData['nume'],
                    'prenume' => $getExpertData['prenume'],
                    'judet' => $getExpertData['judet'],
                    'id_judet' => $getExpertData['id_judet'],
                    'localitate' => $getExpertData['localitate'],
                    'nr_legitimatie' => $getExpertData['nr_legitimatie']
                ];
                $emailService->sendEmailInlocuireExpert($expertData, $nrDosar, $specializariNewCNP);
                $emailService->sendEmailInlocuireBLET($expertData, $nrDosar, $specializariNewCNP);
            }

            log_data_operation($dbConnection, 'update', 'expertize', null, [
                'description' => "Actualizare status expertiză in status 2 (inlocuit) pentru dosarul $nrDosar",
                'data_after' => ['nrDosar' => $nrDosar, 'CNP' => $CNP_URI, 'idStatusExpertiza' => 2]
            ]);
            log_data_operation($dbConnection, 'update', 'workload', null, [
                'description' => "Decrementare încărcătură -1 (alocare random (inlocuit)) experți pentru dosarul $nrDosar",
                'data_after' => ['CNP' => $CNP_URI]
            ]);
        }
        //insert expert random
        $sql = "INSERT INTO exp_jud.expertize (nrDosar, cnpExpert, idSpecializare, idSubSpecializare, onorariu, idUtilizatorInsert, dataDesemnare, idStatusExpertiza) VALUES ";
        $sql .= implode(", ", $values) . ";";
        $dbConnection->exec($sql);
        $idExpertiza = $dbConnection->lastInsertId();

        $conditii = [];
        foreach ($formattedArray as [$spec, $subspec]) {
            $conditii[] = "($spec, $subspec)";
        }
        $tupleList = implode(',', $conditii);

        // Send email notification to experts if they have email addresses
        $emailService = new EmailService($dbConnection);
        foreach ($expertRandom as $expert) {
            $expertData = getExpertData($expert['CNP']);
            $emailService->sendEmailCatreExpert($expertData, $nrDosar, $specializariNewCNP, $onorariu);
        }
        $emailService->sendEmailCatreBLET($nrDosar, $specializariNewCNP, $onorariu, $nrExperti, $expertData);

        log_data_operation($dbConnection, 'insert', 'expertize', $idExpertiza, [
            'description' => "Adăugare expertiză (alocare random) pentru dosarul $nrDosar",
            'data_after' => [
                'nrDosar' => $nrDosar,
                'experți' => $expertFullNames,
                'specializări' => $tupleList,
                'onorariu' => $onorariu,
                'sql' => $sql
            ]
        ]);

        $sql = "INSERT INTO exp_jud.lottery_output (id_expertiza, output) VALUES ($idExpertiza, '$output');";
        $dbConnection->exec($sql);

        log_data_operation($dbConnection, 'insert', 'lottery_output', $idExpertiza, [
            'description' => "Adăugare pasi parcursi la alocarea random pentru dosarul $nrDosar",
            'data_after' => ['id_expertiza' => $idExpertiza]
        ]);

        DatabasePool::releaseConnection($dbConnection);
        $chunks = str_split($html, 1_000_000);
        foreach ($chunks as $chunk) {
            $mpdf->WriteHTML($chunk);
        }

        ob_clean(); // elimină tot ce e înainte
        header_remove();
        $mpdf->Output("expert_$nrDosar.pdf", "D");
        exit;
    } catch (Exception $e) {
        $message = 'Error generating PDF: ' . $e->getMessage();
    }
}

if (isset($_POST['getExpertConvenit'])) {
    global $SESSION_id_utilizator;
    require_once '../inc/cfg_db.php';
    $dbConnection = DatabasePool::getConnection();
    $status = 'error';
    $message = '';

    try {
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'tempDir' => __DIR__ . '/tmp',
        ]);

        $nrDosar = strtolower(sanitizeInput($_POST['nrDosar']));
        $onorariu = isset($_POST['onorariu']) ? filter_var($_POST['onorariu'], FILTER_VALIDATE_INT) : null;
        $specializariArray = json_decode(sanitizeInput($_POST['specializariAlese']), true);
        $cnpPrelucrat = isset($_POST['cnp']) ? filter_input(INPUT_POST, 'cnp', FILTER_SANITIZE_FULL_SPECIAL_CHARS) : null;
        
		if ($onorariu === false || $onorariu === null || $onorariu <= 0) {
            throw new Exception('Onorariul trebuie să fie > zero!<br>');
		}
            
        if (validCNP($cnpPrelucrat) === false) {
            echo json_encode([
                'status' => 'error',
                'message' => "<h4>CNP-ul introdus $cnpPrelucrat este invalid.</h4>"
            ]);
            exit();
        }

        $existaExpertulCuCNP = "SELECT COUNT(*) FROM exp_jud.experti_tehnici WHERE cnp = :cnp";
        $stmt = $dbConnection->prepare($existaExpertulCuCNP);
        $stmt->execute([':cnp' => $cnpPrelucrat]);
        $expertExists = $stmt->fetchColumn();
        if ($expertExists == 0) {
            echo json_encode([
                'status' => 'error',
                'message' => "<h4>Expertul cu CNP-ul introdus ($cnpPrelucrat) nu există în baza de date.</h4>"
            ]);
            exit();
        }
        $specializariSelect = [];
        foreach ($specializariArray as $item) {
            if (is_array($item)) {
                foreach ($item as $value) {
                    $specializariSelect[] = intval($value);
                }
            } else {
                $specializariSelect[] = intval($item);
            }
        }
        if (empty($specializariSelect)) {
            echo json_encode([
                'status' => 'error'
                , 'message' => 'Nu a fost selectată nicio specializare.'
            ]);
            exit();
        }
        $formattedArray = []; //[[184,0],[204,68],[306,90]]
        foreach ($specializariSelect as $i => $value) {
            if ($i % 2 == 0) {
                $formattedArray[] = [$value, $specializariSelect[$i + 1]];
            }
        }

        $perechi = getSpecializariExpertDAETJ($cnpPrelucrat);
        $specializariList = $perechi['specializari'];
        $subspecializariList = $perechi['subspecializari'];
        $nr = 0;

        foreach ($specializariArray as $specializare) {
            for ($i = 0; $i < count($specializariList); $i++) {
                if ($specializariList[$i] == $specializare[0] &&
                    $subspecializariList[$i] == $specializare[1]) {
                    $nr++;
                    break; // Found a match, move to next specializare
                }
            }
        }
        $expAlocat = checkExpertAlocat($nrDosar, $formattedArray);
        if ($expAlocat) {
            foreach ($expAlocat as $expert) {
                if (($expert['CNP']) == $cnpPrelucrat) {
                    echo json_encode([
                        'status' => 'error'
                        , 'message' => 'Expertul căutat a fost deja alocat în acest dosar. Vă rugăm selectați un alt expert.'
                    ]);
                    exit();
                }
            }
        }

        if ($nr == count($specializariArray)) {
            $expertQuery = "SELECT e.nume, e.prenume, e.legitimatie, j.numeJudet judet, l.localitate localitate FROM exp_jud.experti_tehnici e join exp_jud.njudete j on j.idJudet = e.id_judet left join exp_jud.z_localitati l on l.id = e.id_localitate WHERE cnp = :cnp";
            $stmt = $dbConnection->prepare($expertQuery);
            $stmt->execute([':cnp' => $cnpPrelucrat]);
            $expertData = $stmt->fetch(PDO::FETCH_ASSOC);

            $expertNume = $expertData['nume'];
            $expertPrenume = $expertData['prenume'];
            $legitimatie = $expertData['legitimatie'];
            $expertCNP = anonimizareCNP($cnpPrelucrat);
            $expertJudet = $expertData['judet'];
            $expertLocalitate = $expertData['localitate'];

            // Get specializari names
            $specializariArray = json_decode(sanitizeInput($_POST['specializariAlese']), true);
            $specializariSelect = [];
            foreach ($specializariArray as $item) {
                if (is_array($item)) {
                    foreach ($item as $value) {
                        $specializariSelect[] = intval($value);
                    }
                } else {
                    $specializariSelect[] = intval($item);
                }
            }

            if (empty($specializariSelect)) {
                echo json_encode([
                    'status' => 'error'
                    , 'message' => 'Nu a fost selectată nicio specializare.'
                ]);
                exit();
            }

            $specializareaCeTrebuieSaOAibaExpNou = [];
            $sql = getSpecializari(); //[184=>0,204=>68]
            foreach ($specializariArray as $pair) {
                if (!is_array($pair) || count($pair) < 2) {
                    error_log('Invalid pair structure: ' . print_r($pair, true));
                    continue;
                }

                $specializareId = $pair[0];
                $subspecializareId = $pair[1];
                $found = false;
                foreach ($sql as $row) {
                    if ($specializareId == $row['id_specializare']) {
                        if ($subspecializareId == 0) {
                            $specializareaCeTrebuieSaOAibaExpNou[] = "<b>{$row['nume_specializare']}</b>";
                        } else if ($subspecializareId == $row['id_subspecializare']) {
                            $specializareaCeTrebuieSaOAibaExpNou[] = "<b>{$row['nume_specializare']}</b>";
                            $specializareaCeTrebuieSaOAibaExpNou[] = $row['nume_subspecializare'];
                        } else {
                            continue; // Continue searching for matching subspecializare
                        }

                        $found = true;
                        break;
                    }
                }

            }

            $specializariString = implode(', ', $specializareaCeTrebuieSaOAibaExpNou);

            // Generate PDF
            $html = "
            <div style='width: 100%; text-align: center;'>
                <h3>{$SESSION_instanta}</h3>
                <p>" . data_afisare(date('Y-m-d H:i:s')) . "</p>
            </div>

            <div>
                <p style='text-align: center;'>În dosarul nr. <strong>$nrDosar</strong>, a fost desemnat în calitate de expert convenit de părți</p>
                <p style='text-align: center;'><b>$expertNume $expertPrenume</b></p>
                <p style='text-align: center;'>Având specializările următoare: <strong>$specializariString</strong></p>
            </div>";


            if (count($expAlocat) == 1) // dacă a mai fost alocat un singur expert cu specializarea asta
            {
                $html .= "
                <div style='margin-top: 20px;'>
                    <p style='text-align: center;'>Expertul {$expAlocat[0]['nume']} {$expAlocat[0]['prenume']} a fost înlocuit.</p>
                </div>";
            } else if (count($expAlocat) > 1) // mai multi
            {
                $html .= "
                <div style='margin-top: 20px;'>
                    <p style='text-align: center;'>Experții desemnați anterior în dosar:</p>
                    <ul style='text-align: center; list-style-type: none; padding: 0;'>";

                $numeExpInlocuitiArray = [];
                foreach ($expAlocat as $expert) {
                    $nume = htmlspecialchars($expert['nume']);
                    $prenume = htmlspecialchars($expert['prenume']);
                    $numeExpInlocuitiArray[] = "$nume $prenume";
                }
                $numeExpInlocuitiArray = array_unique($numeExpInlocuitiArray);
                $html .= implode(", ", $numeExpInlocuitiArray);
                $html .= "
                    </ul>
                </div>";
            }


            $html .= "
            <div style='margin-top: 20px;'>
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <th style='width: 49%; border: 1px solid #000; padding: 8px;'>Detalii lucrare</th>
                        <th style='width: 50%; border: 1px solid #000; padding: 8px;'>Detalii expert</th>
                    </tr>
                    <tr>
                        <td style='border: 1px solid #000; padding: 8px;'>
                            <p><strong>Număr dosar: </strong>{$nrDosar}</p>
                            <p><strong>Specializări: </strong>{$specializariString}</p>
                            <p><strong>Onorariu: </strong>$onorariu RON</p>
                            <p><strong>Data desemnării: </strong>" . data_afisare(date('Y-m-d H:i:s')) . "</p>
                        </td>
                        <td style='border: 1px solid #000; padding: 8px;'>
                            <p><strong>Nume: </strong>{$expertNume} {$expertPrenume}</p>
                            <p><strong>CNP: </strong>{$expertCNP}</p>
                            <p><strong>Județ: </strong>{$expertJudet}</p>
                            <p><strong>Localitate: </strong>{$expertLocalitate}</p>
                            <p><strong>Autorizație: </strong>{$legitimatie}</p>
                        </td>
                    </tr>
                </table>
            </div>";

            $chunks = str_split($html, 1_000_000);
            foreach ($chunks as $chunk) {
                $mpdf->WriteHTML($chunk);
            }


            $specializari = [];
            $subSpecializari = [];
            foreach ($specializariArray as $s) {
                $specializari[] = $s[0];
                $subSpecializari[] = $s[1];
            }
            $specializariString = implode(',', $specializari);
            $subSpecializariString = implode(',', $subSpecializari);


            $checkSql = "SELECT COUNT(*) FROM exp_jud.expertize
                        WHERE nrDosar = :nrDosar
                        AND idStatusExpertiza = 1
                        AND idSpecializare = :specializare
                        AND idSubSpecializare = :subspecializare";
            $stmt = $dbConnection->prepare($checkSql);
            $stmt->execute([
                ':nrDosar' => $nrDosar,
                ':specializare' => $specializariString,
                ':subspecializare' => $subSpecializariString
            ]);
            $existingExpert = $stmt->fetchColumn();
            if ($existingExpert > 0) {
                $getExistingExpertSql = "SELECT cnpExpert, idSpecializare, idSubSpecializare
                            FROM exp_jud.expertize
                            WHERE nrDosar = :nrDosar
                            AND idStatusExpertiza = 1
                            AND idSpecializare = :specializare
                            AND idSubSpecializare = :subspecializare";
                $stmt = $dbConnection->prepare($getExistingExpertSql);
                $stmt->execute([
                    ':nrDosar' => $nrDosar,
                    ':specializare' => $specializariString,
                    ':subspecializare' => $subSpecializariString
                ]);
                $existingExpertData = $stmt->fetch(PDO::FETCH_ASSOC);

                $updateSql = "UPDATE exp_jud.expertize
                  SET idStatusExpertiza = 2
                  WHERE nrDosar = :nrDosar
                  AND cnpExpert = :cnpExpert
                  AND idSpecializare = :specializare
                  AND idSubSpecializare = :subspecializare
                  AND idStatusExpertiza = 1";
                $stmt = $dbConnection->prepare($updateSql);
                $stmt->execute([
                    ':nrDosar' => $nrDosar,
                    ':cnpExpert' => $existingExpertData['cnpExpert'],
                    ':specializare' => $specializariString,
                    ':subspecializare' => $subSpecializariString
                ]);

                //send email inlocuire expert
                $emailService = new EmailService($dbConnection);
                $expertData = getExpertData($existingExpertData['cnpExpert']);
                $specializariStringInlocuireStringNume = implode(', ', $specializareaCeTrebuieSaOAibaExpNou);
                $emailService->sendEmailInlocuireExpert($expertData, $nrDosar, $specializariStringInlocuireStringNume);
                $emailService->sendEmailInlocuireBLET($expertData, $nrDosar, $specializariStringInlocuireStringNume);

                log_data_operation($dbConnection, 'update', 'expertize', null, [
                    'description' => "Actualizare status expertiză 2 (inlocuit) pentru dosarul $nrDosar",
                    'data_after' => [
                        'nrDosar' => $nrDosar,
                        'cnpExpert' => $existingExpertData['cnpExpert'],
                        'idStatusExpertiza' => 2,
                        'specializare' => $specializariString,
                        'subspecializare' => $subSpecializariString
                    ]
                ]);

                $verificaExistentaInWorkload = verificaExistentaInWorkload($existingExpertData['cnpExpert']);
                if (count($verificaExistentaInWorkload) > 0) {
                    $minus = "update exp_jud.workload set incarcatura_app = incarcatura_app -1 where CNP = '$existingExpertData[cnpExpert]' AND idLoad > 0";
                    $dbConnection->exec($minus);
                }

                log_data_operation($dbConnection, 'update', 'workload', null, [
                    'description' => "Decrementare încărcătură -1 (inlocuire) expert pentru dosarul $nrDosar",
                    'data_after' => ['CNP' => $existingExpertData['cnpExpert']]
                ]);

                // Insert new expert convenit ce inlocuieste pe cel anterior
                $sql = "INSERT INTO exp_jud.expertize (nrDosar, cnpExpert, idSpecializare, idSubSpecializare, onorariu, idUtilizatorInsert, dataDesemnare, idStatusExpertiza) VALUES ";
                $values = "('$nrDosar', '$cnpPrelucrat', '$specializariString', '$subSpecializariString', '$onorariu', '$SESSION_id_utilizator', NOW(), '1')";
                $sql .= $values . ";";
                $dbConnection->exec($sql);
                $idExpertiza = $dbConnection->lastInsertId();

                // Send email notification
                $emailService = new EmailService($dbConnection);
                $expertData = getExpertData($cnpPrelucrat);
                $emailService->sendEmailCatreExpert($expertData, $nrDosar, $specializariStringInlocuireStringNume, $onorariu);
                $emailService->sendEmailCatreBLET($nrDosar, $specializariStringInlocuireStringNume, $onorariu, 1, $expertData);

                log_data_operation($dbConnection, 'insert', 'expertize', $idExpertiza, [
                    'description' => "Adăugare expert convenit pentru dosarul $nrDosar",
                    'data_after' => [
                        'nrDosar' => $nrDosar,
                        'cnpExpert' => $cnpPrelucrat,
                        'specializări' => getTextSpecializari($specializari, $subSpecializari, $specializareaCeTrebuieSaOAibaExpNou),
                        'onorariu' => $onorariu
                    ]
                ]);

                $verificaExistentaInWorkload = verificaExistentaInWorkload($cnpPrelucrat);
                if (count($verificaExistentaInWorkload) > 0) {
                    $plus = "update exp_jud.workload set incarcatura_app = incarcatura_app +1 where CNP = '$cnpPrelucrat' AND idLoad > 0";
                    $dbConnection->exec($plus);
                }

                log_data_operation($dbConnection, 'update', 'workload', null, [
                    'description' => "Incrementare încărcătură +1 expert pentru dosarul $nrDosar",
                    'data_after' => ['CNP' => $cnpPrelucrat]
                ]);

                $pdfContent = $mpdf->Output('', 'S'); // Get PDF as string
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="expert_convenit_' . $nrDosar . '.pdf"');
                echo $pdfContent;
                exit;
            } else {
                // Insert new expert convenit
                $sql = "INSERT INTO exp_jud.expertize (nrDosar, cnpExpert, idSpecializare, idSubSpecializare, onorariu, idUtilizatorInsert, dataDesemnare, idStatusExpertiza) VALUES ";
                $specializari = [];
                $subSpecializari = [];
                foreach ($specializariArray as $s) {
                    $specializari[] = $s[0];
                    $subSpecializari[] = $s[1];
                }
                $specializariString = implode(',', $specializari);
                $subSpecializariString = implode(',', $subSpecializari);
                $values = "('$nrDosar', '$cnpPrelucrat', '$specializariString', '$subSpecializariString', '$onorariu', '$SESSION_id_utilizator', NOW(), '1')";
                $sql .= $values . ";";
                $dbConnection->exec($sql);
                $idExpertiza = $dbConnection->lastInsertId();

                log_data_operation($dbConnection, 'insert', 'expertize', $idExpertiza, [
                    'description' => "Adăugare dexpert convenit pentru dosarul $nrDosar",
                    'data_after' => [
                        'nrDosar' => $nrDosar,
                        'cnpExpert' => $cnpPrelucrat,
                        'specializări' => getTextSpecializari($specializari, $subSpecializari, $specializareaCeTrebuieSaOAibaExpNou),
                        'onorariu' => $onorariu
                    ]
                ]);

                // Send email notification
                $emailService = new EmailService($dbConnection);
                $expertData = getExpertData($cnpPrelucrat);
                $specializariStringInlocuireStringNume = implode(', ', $specializareaCeTrebuieSaOAibaExpNou);
                $emailService->sendEmailCatreExpert($expertData, $nrDosar, $specializariStringInlocuireStringNume, $onorariu);
                $emailService->sendEmailCatreBLET($nrDosar, $specializariStringInlocuireStringNume, $onorariu, 1, $expertData);

                verificaExistentaInWorkload($cnpPrelucrat);
                $plus = "update exp_jud.workload set incarcatura_app = incarcatura_app +1 where CNP = '$cnpPrelucrat' AND idLoad > 0";
                $dbConnection->exec($plus);

                log_data_operation($dbConnection, 'update', 'workload', null, [
                    'description' => "Incrementare încărcătură +1 expert pentru dosarul $nrDosar",
                    'data_after' => ['CNP' => $cnpPrelucrat]
                ]);

                $pdfContent = $mpdf->Output('', 'S'); // Get PDF as string
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . $nrDosar . ' expert convenit.pdf"');
                echo $pdfContent;
                exit;

            }
        } else {
            $ceAreDefaptExpertul = getSpecializariExpertDAETJ($cnpPrelucrat);
            $toateSpecializarile = getSpecializari();
            $message = "<b>Expertul nu poate fi desemnat</b> deoarece ";
            if (count($specializariArray) > 1) {
                $message .= "nu deține toate specializările selectate";
            } else {
                $message .= "nu deține specializarea selectată";
            }
            $message .= ".<br>Specializările expertului (CNP: $cnpPrelucrat) sunt:<ol>";
            $specializariMap = [];
            foreach ($toateSpecializarile as $spec) {
                $specializariMap[$spec['id_specializare']] = $spec['nume_specializare'];
                if (!empty($spec['id_subspecializare'])) {
                    $specializariMap[$spec['id_subspecializare']] = $spec['nume_subspecializare'];
                }
            }
            $specializariList = $ceAreDefaptExpertul['specializari'];
            $subspecializariList = $ceAreDefaptExpertul['subspecializari'];

            for ($i = 0; $i < count($specializariList); $i++) {
                $message .= "<li>";
                $specId = $specializariList[$i];
                $subSpecId = isset($subspecializariList[$i]) ? $subspecializariList[$i] : '';

                $specName = $specializariMap[$specId] ?? 'Necunoscută';
                $subSpecName = ($subSpecId && $subSpecId != '0') ? ($specializariMap[$subSpecId] ?? '') : '';

                $message .= $specName;
                if (!empty($subSpecName)) {
                    $message .= " - " . $subSpecName;
                }
                $message .= "</li>";
            }
            $message .= "</ol>";
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => $message
            ]);
            exit;
        }
    } catch (Exception $e) {
        $status = 'error';
        $message = "Error occurred: " . $e->getMessage();
    } finally {
        DatabasePool::releaseConnection($dbConnection);
    }

//    $perecheSpecializari = $perechi['specializari'];
//    $perecheSubspecializari = $perechi['subspecializari'];
//
//
//    //perechea trimisa $specializari se regaseste in $perecheSpecializari si $perecheSubspecializari
//    $specializari=[240,63];
//    $text="";
//    $areSpecializariDorite = 0;
//    for($i=0; $i<count($perecheSpecializari); $i++)
//    {
//        foreach($specializari as $index => $val){
//            if($val[$index][0] == $perecheSpecializari[$i] && $val[$index][1] == $perecheSubspecializari[$i]){
//
//                $text .="Am gasit specializarea $perecheSpecializari[$i] cu subspecializarea $perecheSubspecializari[$i]";
//                $areSpecializariDorite++;
//
//                if($areSpecializariDorite == count($specializari)){
//                    //insert in expertize si mesaj
//                    break;
//                }
//            }
//
//        }
//    }
//
//    if($areSpecializariDorite = 0){
//       // mesaj expertul nu are specizalizarile selectate
//
//    }

}

$pool_EXP_TEHNICI_OLD->releaseConnection($conn_EXP_TEHNICI_OLD);
echo json_encode([
    'status' => $status
    , 'message' => $message
]);

exit;
