<?php

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';

$db = DatabasePool::getConnection();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $query = "SELECT idJudet, numeJudet FROM njudete order by numeJudet asc ";
    $stmt = $db->prepare($query);
    $stmt->execute();

    $data = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $data[] = [
            'idJudet' => $row['idJudet'],
            'numeJudet' => $row['numeJudet']
        ];
    }

    echo json_encode(["data" => $data]);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'Eroare JSON: ' . json_last_error_msg();
    }

    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
