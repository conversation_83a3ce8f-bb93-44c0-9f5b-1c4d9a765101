<?php
header('Content-Type: application/json');
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        global $SESSION_id_utilizator, $SESSION_username;
        $dbConnection = DatabasePool::getConnection();

        $insertQuery = "INSERT INTO exp_jud.warning_acknowledgments (ip_address) VALUES (:ipAddress)";
        $stmt = $dbConnection->prepare($insertQuery);
        $stmt->execute([
            'ipAddress' => $_SERVER['REMOTE_ADDR']
        ]);

        $acknowledgmentId = $dbConnection->lastInsertId();

        // Log the warning acknowledgment
        log_data_operation($dbConnection, 'insert', 'warning_acknowledgments', $acknowledgmentId, [
            'description' => "Confirmare avertisment mediu de producție",
            'data_after' => [
                'ip_address' => $_SERVER['REMOTE_ADDR']
            ]
        ]);

        echo json_encode(['status' => 'success']);
    } catch (PDOException $e) {
        log_audit_action($dbConnection, [
            'action_type' => 'database_error',
            'action_category' => 'error',
            'action_level' => 'error',
            'description' => "Database error in warning_acknowledgment.php: " . $e->getMessage()
        ]);
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Eroare la salvarea confirmării']);
    }
} else {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
}
exit;

