<?php
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
header('Content-Type: application/json');

try {
    global $SESSION_id_rol, $SESSION_id_instanta;
    if (!in_array($SESSION_id_rol, [1, 3, 5])) { // Admin, SPJC, SpIT
        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access'
        ]);
        exit;
    }

    $dbConnection = DatabasePool::getConnection();
    $params = [];
    if ($SESSION_id_rol == 1) {
        // <PERSON><PERSON> can see all instances
        $query = "SELECT id, den FROM exp_jud.z_instante ORDER BY den";
    } else {
        // Get current user's instance info
        $levelQuery = "SELECT id_nivel, id_instanta_sup FROM exp_jud.z_instante WHERE id = :current_instance";
        $stmt = $dbConnection->prepare($levelQuery);
        $stmt->execute(['current_instance' => $SESSION_id_instanta]);
        $instanceInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        // Build query based on hierarchy
        $query = "WITH RECURSIVE instance_hierarchy AS (
            -- Base case: current instance
            SELECT id, den, id_instanta_sup, id_nivel
            FROM exp_jud.z_instante
            WHERE id = :current_instance

            UNION ALL

            -- Recursive case: get subordinate instances
            SELECT i.id, i.den, i.id_instanta_sup, i.id_nivel
            FROM exp_jud.z_instante i
            INNER JOIN instance_hierarchy ih ON i.id_instanta_sup = ih.id
        )
        SELECT id, den 
        FROM instance_hierarchy
        ORDER BY den";

        $params['current_instance'] = $SESSION_id_instanta;
    }

    $stmt = $dbConnection->prepare($query);
    $stmt->execute($params);
    $instances = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'status' => 'success',
        'data' => $instances
    ]);
    exit;

} catch (Exception $e) {
    print_r("Error in getInstante.php: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => 'A apărut o eroare la încărcarea instanțelor'
    ]);
    exit;
}