<?php
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';

$dbConnection = DatabasePool::getConnection();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['action'])) {
        echo json_encode(['status' => 'error', 'message' => 'Cerere invalidă']);
        exit;
    }

    switch ($_POST['action']) {
        case 'completare_formular':
            log_audit_action($dbConnection, [
                'action_type' => 'form_completion',
                'action_category' => 'user_interaction',
                'action_level' => 'info',
                'description' => "Formular - {$_POST['form_name']} - completat (doar completare form, fara a trimite datele, in acest moment, pe btn aleatoriu/convenit)",
                'data_after' => json_encode($_POST['campuri_form'])
            ]);
            break;
    }
    echo json_encode(['status' => 'ok']);
    exit;
}

?>