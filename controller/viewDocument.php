<?php
global $env;
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';

if (!isset($_GET['id']) || empty($_GET['id'])) {
    die('ID-ul documentului lipsește');
}

try {
    $idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);
    $dbConnection = DatabasePool::getConnection();
    $id = $idEncoder->decode(sanitizeInput($_GET['id']));

    $sql = "SELECT document_justificativ 
            FROM exp_jud.expertize 
            WHERE id = :id AND idStatusExpertiza = 4";
    
    $stmt = $dbConnection->prepare($sql);
    $stmt->execute([':id' => $id]);
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && $result['document_justificativ']) {
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="document_justificativ.pdf"');
        echo $result['document_justificativ'];
    } else {
        die('Documentul nu a fost găsit');
    }
    
    DatabasePool::releaseConnection($dbConnection);
} catch (Exception $e) {
    die('Eroare la încărcarea documentului: ' . $e->getMessage());
}