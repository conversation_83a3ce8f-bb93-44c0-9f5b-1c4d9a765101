<?php
header('Content-Type: application/json; charset=UTF-8');

// Include session check helper which will verify session validity
require_once ('../inc/session_check.php');

// Include cfg_session.php which will set up global session variables
// This will reuse the session already started in session_check.php
require_once ('../inc/cfg_session.php');

// Now we can use session variables
global $SESSION_id_instanta;
$dbConnection = DatabasePool::getConnection();

$numeInstanta=NULL;

if(isset($_POST['verificareIDinstanta'])) {

    $idInstantaDinDosarIntrodus = $_POST['id_instanta'];

    $sql="select * from exp_jud.z_instante where id='$idInstantaDinDosarIntrodus'";
    $rez= $dbConnection->prepare($sql);
    $rez->execute();
    $result=$rez->fetchAll();
    foreach ($result as $row) {
        $numeInstanta=$row['den'];
    }
}

echo json_encode(['id_instanta' => $SESSION_id_instanta, 'nume_instanta' => $numeInstanta]);

?>