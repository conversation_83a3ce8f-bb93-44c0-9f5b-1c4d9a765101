<?php
header('Content-Type: application/json; charset=UTF-8');

// Include session handling
require_once '../inc/cfg_db.php';
require_once '../vendor/autoload.php';
require_once '../inc/cfg_session.php'; // Include for SESSION_TIMEOUT constant

// Initialize session
$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');

// Check if user is logged in
$logged_in = $sesiune->get('logged_in');

if ($logged_in) {
    // Update last activity time
    $sesiune->set('last_activity', time());

    // Return success response
    echo json_encode([
        'status' => 'success',
        'message' => 'Activity timestamp updated',
        'timestamp' => time()
    ]);
} else {
    // Return error response
    echo json_encode([
        'status' => 'error',
        'message' => 'User not logged in',
        'code' => 'not_logged_in'
    ]);
}
