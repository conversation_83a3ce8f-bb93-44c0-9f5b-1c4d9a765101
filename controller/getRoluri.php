<?php
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';

try {
    $dbConnection = DatabasePool::getConnection();
    global $SESSION_id_rol, $SESSION_id_instanta;

    // Only allow access for authorized roles
    global $SESSION_id_rol;
    if (!in_array($SESSION_id_rol, [1, 3, 5])) { // Admin, SPJC, SpIT
        echo json_encode([
            'status' => 'error',
            'message' => 'Unauthorized access'
        ]);
        exit;
    }

    $query = "SELECT id, tip FROM exp_jud.z_rol_utilizator ORDER BY id";
    $stmt = $dbConnection->query($query);
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Filter roles based on user's role
    $filteredRoles = array_filter($roles, function($role) use ($SESSION_id_rol) {
        switch ($SESSION_id_rol) {
            case 1: // Admin can see all roles
                return true;
            case 3: // SPJC can only see Judecator and Grefier roles
                return in_array($role['id'], [2, 4]);
            case 5: // SpIT can only see Judecator and Grefier roles
                return in_array($role['id'], [2, 4, 5]);
            default:
                return false;
        }
    });

    echo json_encode([
        'status' => 'success',
        'data' => array_values($filteredRoles)
    ]);

} catch (PDOException $e) {
    log_audit_action($dbConnection, [
        'action_type' => 'database_error',
        'action_category' => 'error',
        'action_level' => 'error',
        'description' => "Database error in getRoluri.php: " . $e->getMessage()
    ]);
    echo json_encode([
        'status' => 'error',
        'message' => 'A apărut o eroare la încărcarea rolurilor'
    ]);
}