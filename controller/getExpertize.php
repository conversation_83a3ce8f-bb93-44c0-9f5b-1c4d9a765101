<?php
global $env;
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_session.php';
require_once 'UniqueIdEncoder.php';
global $SESSION_id_utilizator;

$dbConnection = DatabasePool::getConnection();
$idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);

// DataTables parameters
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 10;
$search = $_POST['search']['value'] ?? '';
$statusFilter = isset($_POST['statusFilter']) && $_POST['statusFilter'] !== '' ?
    intval($_POST['statusFilter']) : null;

$join = "
LEFT JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
LEFT JOIN exp_jud.specializarisubspecializari ss ON e.idSpecializare = ss.id_specializare
LEFT JOIN  exp_jud.nstatus_expertize ns ON e.idStatusExpertiza = ns.id
LEFT JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
LEFT JOIN exp_jud.z_instante i ON u.idInstanta = i.id
LEFT JOIN exp_jud.njudete j ON i.id_judet = j.idJudet";
$where = "
WHERE 1 = 1
    AND i.id_judet =
    (
        SELECT i2.id_judet
       FROM exp_jud.utilizatori u2
       JOIN exp_jud.z_instante i2 ON u2.idInstanta = i2.id
       WHERE u2.id = '$SESSION_id_utilizator'
    )";
$groupBy = " group by e.nrDosar, e.cnpExpert, e.idSpecializare, e.idSubspecializare, e.idStatusExpertiza";
$params = [];
if (!empty($search)) {
    $search = str_replace(['\\', '&amp;', '&amp'], '', sanitizeInput($search));
    $searchTerms = explode(' ', trim($search));
    $searchConditions = [];
    $basicSearch = "(et.nume LIKE '%{$search}%' OR et.prenume LIKE '%{$search}%' OR et.cnp LIKE '%{$search}%' 
    OR et.telefon LIKE '%{$search}%' OR e.nrDosar LIKE '%{$search}%' OR et.legitimatie LIKE '%{$search}%'
    OR u.utilizator = '{$search}')";
    if (count($searchTerms) > 1) {
        foreach ($searchTerms as $term) {
            if (strlen($term) > 0) {
                $searchConditions[] = "(et.nume LIKE '%{$term}%' OR et.prenume LIKE '%{$term}%')";
            }
        }
    }

    $where .= " AND (" . $basicSearch;
    if (!empty($searchConditions)) {
        $where .= " OR (" . implode(" AND ", $searchConditions) . ")";
    }
    $where .= ")";
}


if ($statusFilter !== null) {
    $where .= " AND e.idStatusExpertiza = :status";
    $params[':status'] = $statusFilter;
}
if (isset($_POST['specializare']) && $_POST['specializare'] !== '') {
    $specializare = intval($_POST['specializare']);
    $where .= " AND (FIND_IN_SET(:specializare, e.idSpecializare) > 0 OR FIND_IN_SET(:specializare, e.idSubspecializare) > 0)";
    $params[':specializare'] = $specializare;
}

$totalQuery = "SELECT COUNT(*) as total FROM (SELECT e.id FROM exp_jud.expertize e $join $where $groupBy) as subquery";
$totalStmt = $dbConnection->prepare($totalQuery);
$totalStmt->execute($params);
$totalRecords = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];

$filteredQuery = "SELECT COUNT(*) as total FROM (SELECT e.id FROM exp_jud.expertize e $join $where $groupBy) as subquery";
$filteredStmt = $dbConnection->prepare($filteredQuery);
$filteredStmt->execute($params);
$result = $filteredStmt->fetch(PDO::FETCH_ASSOC);
$filteredRecords = $result ? $result['total'] : 0;

$sql = "
SELECT *,
    (CASE WHEN status_1_count = 3 THEN 1 ELSE 0 END) as comisie3
FROM (
    select
    MAX(e.id) as id,
    e.nrDosar,
    e.cnpExpert,
    e.idSpecializare,
    e.idSubspecializare,
    MAX(e.onorariu) as onorariu,
    MAX(e.idUtilizatorInsert) as idUtilizatorInsert,
    MAX(e.dataDesemnare) as dataDesemnare,
    MAX(e.idStatusExpertiza) as idStatusExpertiza,
    MAX(e.idUtilizatorInchidere) as idUtilizatorInchidere,
    MAX(e.dataInchidere) as dataInchidere,
    CONCAT(et.nume, ' ', et.prenume) as nume_expert,
    ss.nume_specializare,
    ns.status as status,
	j.numeJudet as judet,
    e.document_justificativ,
    SUM(CASE WHEN e.idStatusExpertiza = 1 THEN 1 ELSE 0 END) 
    OVER (PARTITION BY e.nrDosar, e.idSpecializare, e.idSubspecializare) as status_1_count
FROM exp_jud.expertize e
    $join
    $where
    $groupBy
) as subquery";

$orderColumn = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
$orderDir = isset($_POST['order'][0]['dir']) ? strtoupper($_POST['order'][0]['dir']) : 'ASC';
$columns = ['nrDosar', 'nume_expert', 'nume_specializare', 'dataDesemnare', 'status'];
$orderBy = isset($columns[$orderColumn]) ? $columns[$orderColumn] : $columns[0];
$sql .= " ORDER BY $orderBy $orderDir";
$sql .= " LIMIT :offset, :limit";
$stmt = $dbConnection->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}

$stmt->bindValue(':offset', $start, PDO::PARAM_INT);
$stmt->bindValue(':limit', $length, PDO::PARAM_INT);
$stmt->execute();
$data = [];

$specialiariCunume = getSpecializari();
foreach ($stmt as $row) {
    $id = $idEncoder->encode($row['id']);

    $specializariArray = explode(',', $row['idSpecializare']);
    $subspecializariArray = explode(',', $row['idSubspecializare']);

    $groupedSpecializations = [];
    for ($i = 0; $i < count($specializariArray); $i++) {
        $specId = trim($specializariArray[$i]);
        $subSpecId = trim($subspecializariArray[$i]);

        $specName = '';
        $subSpecName = '';

        foreach ($specialiariCunume as $spec) {
            if ($spec['id_specializare'] == $specId) {
                $specName = $spec['nume_specializare'];
            }
            if ($spec['id_subspecializare'] == $subSpecId) {
                $subSpecName = $spec['nume_subspecializare'];
            }
        }

        if (!isset($allUniqueSpecializations[$specName])) {
            $allUniqueSpecializations[$specName] = [];
        }
        if ($subSpecId != '0' && !in_array($subSpecName, $allUniqueSpecializations[$specName])) {
            $allUniqueSpecializations[$specName][] = $subSpecName;
        }
        if (!isset($groupedSpecializations[$specName])) {
            $groupedSpecializations[$specName] = [];
        }
        if ($subSpecId != '0') {
            $groupedSpecializations[$specName][] = $subSpecName;
        }
    }

    $specializariTD = '';
    foreach ($groupedSpecializations as $specName => $subSpecs) {
        $specializariTD .= "<b>$specName</b><br>";
        foreach ($subSpecs as $subSpec) {
            $specializariTD .= "&nbsp;&nbsp;&nbsp;&nbsp;$subSpec<br>";
        }
    }

    $statusTD = '<label class="badge ' .
        ($row['idStatusExpertiza'] == 0 ? 'bg-success' :
            ($row['idStatusExpertiza'] == 1 ? 'bg-danger' :
                ($row['idStatusExpertiza'] == 2 ? 'bg-mov' : 'bg-dark'))) .
        ' rounded-pill">' . htmlspecialchars($row['status']) . '</label>';

    $generarePDFBtn = "<a href='controller/generateExpertPDF.php?id=$id' target='_blank'
                        data-bs-toggle='tooltip' data-bs-placement='left' data-bs-html='true' title='Vizualizare expert desemnat'>
                        <i class='fa fa-file " .
        ($row['idStatusExpertiza'] == 0 ? "text-success" :
            ($row['idStatusExpertiza'] == 1 ? "text-danger" :
                ($row['idStatusExpertiza'] == 2 ? "text-mov" : "text-dark"))) . "'></i></a>";
    $dosarEronatBtn = "<a href='#' class='dosar-eronat-btn' style='color: #728179;'
                        data-id='{$id}'
                        data-bs-toggle='tooltip' data-bs-html='true'
                        data-bs-placement='left' title='Dosar introdus eronat'
                        data-bs-original-title='Dosar introdus eronat'
                        aria-label='Dosar introdus eronat'>
                        <i class='fa fa-trash-alt'></i></a>";
    if ($row['idStatusExpertiza'] == 4) {
        $dosarEronatBtn = null;
        if ($row['document_justificativ']) {
            $dosarEronatBtn = "<a href='controller/viewDocument.php?id={$id}'
                            target='_blank'
                            data-bs-toggle='tooltip'
                            data-bs-html='true'
                            data-bs-placement='left'
                            title='Vizualizare document justificativ'
                            data-bs-original-title='Vizualizare document justificativ'
                            aria-label='Vizualizare document justificativ'>
                            <i class='fa fa-file-pdf text-danger'></i></a>";
        }
    } else if ($row['idStatusExpertiza'] == 1 && $row['comisie3'] == 1) {
        $dosarEronatBtn .= "<a href='inlocuire.php?eid={$id}' class='btn-inlocuire-expert' style='color: #e8c952;' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-html='true' title='Inlocuire expert in comisie'><i class='fa fa-users'></i></a>";
    }

    $actiuniTD = "$generarePDFBtn $dosarEronatBtn";

    $data[] = [
        'nrDosar' => htmlspecialchars($row['nrDosar'] ?? ''),
        'expert' => '<b>' . htmlspecialchars($row['nume_expert'] ?? '') . '</b><br>CNP ' . htmlspecialchars($row['cnpExpert'] ?? ''),
        'specializare' => $specializariTD,
        'dataDesemnare' => (new DateTime($row['dataDesemnare']))->format('d.m.Y'),
        'status' => $statusTD,
        'actiuni' => $actiuniTD
    ];
}

// Log the search operation if search terms or filters were used
if (!empty($search) || $statusFilter !== null) {
    log_search_operation($dbConnection, 'expertize_search', [
        'search_term' => $search,
        'status_filter' => $statusFilter,
        'results_count' => $filteredRecords
    ], [
        'description' => "Căutare expertize" . (!empty($search) ? ": $search" : '') .
            ($statusFilter !== null ? " (filtru status: $statusFilter)" : '')
    ]);
}

header('Content-Type: application/json');
// If no search or filters are applied, set filteredRecords equal to totalRecords
if (empty($search) && $statusFilter === null && (!isset($_POST['specializare']) || $_POST['specializare'] === '')) {
    $filteredRecords = $totalRecords;
}

echo json_encode([
    'draw' => $draw,
    'recordsTotal' => $totalRecords,
    'recordsFiltered' => $filteredRecords,
    'data' => $data,
    'lengthMenu' => [[10, 50, 100], [10, 50, 100]]
]);

DatabasePool::releaseConnection($dbConnection);
exit;