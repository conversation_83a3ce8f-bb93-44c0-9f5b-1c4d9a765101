<?php

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';

global $SESSION_id_rol, $SESSION_id_judet, $SESSION_judet;;
$db = DatabasePool::getConnection(); // conexiune PDO

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'getBletEmails') {
        try {
            $emails = [];
            switch ($SESSION_id_rol) {
                case '1':
                    $emails = getEmailsBLETall($db);
                    break;
                case '5':
                    $emails = getEmailsBLETbyJudet($db, $SESSION_id_judet);
                    log_search_operation($db, 'administrareBLET.php', [
                        'description' => "Access vizualizare e-mail-uri BLET, pentru judetul $SESSION_judet",
                        'query_params' => ['id_rol' => $SESSION_id_rol, 'id_judet' => $SESSION_id_judet, 'table_name' => 'exp_jud.e_blet'],
                    ]);
                    break;
            }

            if ($emails) {
                echo json_encode(['success' => true, 'emails' => $emails]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Nu s-au gasit email-uri pentru Judetul ' . $SESSION_judet]);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Eroare la conexiune: ' . $e->getMessage()]);
        }
    }
    elseif ($_POST['action'] === 'deleteBletEmail' && isset($_POST['id'])) {
        try {
            $id = intval($_POST['id']);

            $query_data_before = $db->prepare("SELECT * FROM exp_jud.e_blet WHERE id = ?");
            $query_data_before->execute([$id]);
            $data_before = $query_data_before->fetchAll(PDO::FETCH_ASSOC);

            $stmt = $db->prepare("DELETE FROM exp_jud.e_blet WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();

            log_data_operation($db, 'delete', 'e_blet', $id, [
                'description' => "Ștergere email BLET",
                'data_before' => $data_before
            ]);

            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Eroare la ștergere: ' . $e->getMessage()]);
        }
    }
    elseif ($_POST['action'] === 'editBletEmail' && isset($_POST['id']) && isset($_POST['email'])) {
        try {
            $id = intval($_POST['id']);
            $email = strtolower($_POST['email']);
            $id_judet = intval($_POST['id_judet']);

            $query_data_before = $db->prepare("SELECT * FROM exp_jud.e_blet WHERE id = ?");
            $query_data_before->execute([$id]);
            $data_before = $query_data_before->fetchAll(PDO::FETCH_ASSOC);

            $stmt = $db->prepare("UPDATE exp_jud.e_blet SET email = :email WHERE id = :id AND id_judet = :id_judet");
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':id_judet', $id_judet, PDO::PARAM_INT);
            $stmt->execute();


            log_data_operation($db, 'update', 'e_blet', $id, [
                'description' => "Actualizare email BLET",
                'data_before' => $data_before,
                'data_after' => ['email' => $email]
            ]);

            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Eroare la actualizare: ' . $e->getMessage()]);
        }
    }
    elseif ($_POST['action'] === 'insertBletEmail') {
        try {
            $email = isset($_POST['email']) ? strtolower(trim($_POST['email'])) : '';
            $judetId = isset($_POST['judetId']) ? intval($_POST['judetId']) : 0;
            error_log("DEBUG: SESSION_id_judet = $judetId");
            if ($SESSION_id_rol == 5) {
                $judetId = $SESSION_id_judet;
            }

            if (empty($email)) {
                echo json_encode([
                    'status' => 'error',
                    'title' => 'Eroare',
                    'text' => 'Te rugăm să introduci un email!'
                ]);
                exit;
            }

            if ($SESSION_id_rol == 1 && $judetId == 0) {
                echo json_encode([
                    'status' => 'error',
                    'title' => 'Eroare',
                    'text' => 'Te rugăm să selectezi un județ!'
                ]);
                exit;
            }

            // Validăm dacă email-ul este valid
            if (!preg_match('/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $email)) {
                echo json_encode([
                    'status' => 'error',
                    'title' => 'Eroare',
                    'text' => 'Email invalid!'
                ]);
                exit;
            }

            if (checkExistEmail($db, $judetId)) {
                echo json_encode([
                    'status' => 'error',
                    'title' => 'Eroare',
                    'text' => 'Exista un email pentru judetul ' . getNumeJudet($db, $judetId) . '!'
                ]);
                exit;
            }

            log_data_operation($db, 'insert', 'e_blet', null, [
                'description' => "Adăugare email BLET pentru judetul " . getNumeJudet($db, $judetId),
                'data_after' => ['email' => $email, 'id_judet' => $judetId]
            ]);

            $stmt = $db->prepare("INSERT INTO exp_jud.e_blet (email, id_judet) VALUES (:email, :id_judet)");
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->bindParam(':id_judet', $judetId, PDO::PARAM_INT);
            $stmt->execute();

            echo json_encode([
                'status' => 'success',
                'title' => 'Succes!',
                'text' => 'Email-ul a fost adăugat cu succes!'
            ]);
        } catch (PDOException $e) {
            echo json_encode([
                'status' => 'error',
                'title' => 'Eroare',
                'text' => 'Eroare la inserare: ' . $e->getMessage()
            ]);
        }
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Cerere invalidă.']);
}
function getEmailsBLETall(PDO $db): array
{
    $sql = "SELECT e.id, e.email,e.id_judet,j.numeJudet FROM exp_jud.e_blet as e
            join njudete as j on e.id_judet=j.idJudet ORDER BY e.id_judet";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);

}

function getEmailsBLETbyJudet(PDO $db, int $idJudet): array
{
    $sql = "SELECT e.id,e.email,e.id_judet,j.numeJudet FROM exp_jud.e_blet as e
                                    join njudete as j on e.id_judet=j.idJudet
                                     WHERE id_judet = :idjudet ";

    $stmt = $db->prepare($sql);
    $stmt->bindParam(':idjudet', $idJudet, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function checkExistEmail(PDO $db, int $idJudet): bool
{
    $sql = "SELECT count(*) as cnt FROM exp_jud.e_blet
          WHERE id_judet = :idjudet";

    $stmt = $db->prepare($sql);
    $stmt->bindParam(':idjudet', $idJudet, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return ($result['cnt'] > 0);

}

function getNumeJudet(PDO $db, int $idJudet)
{
    $sql = "SELECT numeJudet FROM exp_jud.njudete
          WHERE idJudet = :idjudet";

    $stmt = $db->prepare($sql);
    $stmt->bindParam(':idjudet', $idJudet, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return ($result['numeJudet']);
}