<?php
header('Content-Type: application/json; charset=UTF-8');

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_functions.php';
$dbConnection = DatabasePool::getConnection();

global $conn_EXP_TEHNICI_OLD;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $data = getSpecializari();

    echo json_encode(["data" => $data]);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'Eroare JSON: ' . json_last_error_msg();
    }
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
