<?php
ob_start();
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
global $SESSION_id_judet;
$dbConnection = DatabasePool::getConnection();
$debug = false;  // Debug flag
$judetPrioritar = $SESSION_id_judet;

$search = $_POST['search']['value'] ?? '';
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 0;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 10;

$orderColumnIndex = $_POST['order'][0]['column'] ?? 0;
$orderDir = $_POST['order'][0]['dir'] ?? 'ASC';

$columns = ["expert", "judet", "adresa", "telefon", "incarcatura", "specializare"];
$orderColumn = $columns[$orderColumnIndex] ?? 'nume';

$where = "1 = 1";
$params = [];


if (!empty($search)) {
    $search = str_replace(['\\', '&amp;', '&amp'], '', sanitizeInput($search));
    $searchTerms = explode(' ', trim($search));
    $searchConditions = [];
    $basicSearch = "(e.nume LIKE '%{$search}%' OR e.prenume LIKE '%{$search}%' OR e.cnp LIKE '%{$search}%' OR e.telefon LIKE '%{$search}%' OR e.legitimatie LIKE '%{$search}%')";
    if (count($searchTerms) > 1) {
        foreach ($searchTerms as $term) {
            if (strlen($term) > 0) {
                $searchConditions[] = "(e.nume LIKE '%{$term}%' OR e.prenume LIKE '%{$term}%')";
            }
        }
    }

    $where .= " AND (" . $basicSearch;
    if (!empty($searchConditions)) {
        $where .= " OR (" . implode(" AND ", $searchConditions) . ")";
    }
    $where .= ")";
}

if (isset($_POST['columns'][1]['search']['value']) && $_POST['columns'][1]['search']['value'] !== '') {
    $filterJudet = str_replace(['\\', '&amp;', '&amp'], '', sanitizeInput($_POST['columns'][1]['search']['value']));
    if ($filterJudet !== '') {
        $filterJudet = trim($filterJudet, '^$');
        $filterJudet = intval($filterJudet);
        $where .= " AND e.id_judet = $filterJudet";
    }
}
if (isset($_POST['columns'][5]['search']['value']) && $_POST['columns'][5]['search']['value'] !== '') {
    $filterSpecializare = str_replace(['\\', '&amp;', '&amp'], '', sanitizeInput($_POST['columns'][5]['search']['value']));
    if ($filterSpecializare != '') {
        $filterSpecializare = trim($filterSpecializare, '^$');
        $filterSpecializare = intval($filterSpecializare);
        $where .= " AND (FIND_IN_SET('{$filterSpecializare}', e.specializari) > 0 OR FIND_IN_SET('{$filterSpecializare}', e.subspecializari) > 0)";
    }
}

if ($orderColumn == 'expert') {
    $orderColumn = "e.nume";
} elseif ($orderColumn == 'judet') {
    $orderColumn = "j.numeJudet";
} elseif ($orderColumn == 'adresa') {
    $orderColumn = "e.adresa";
} elseif ($orderColumn == 'telefon') {
    $orderColumn = "e.telefon";
} elseif ($orderColumn == 'incarcatura') {
    $orderColumn = "incarcatura";
} elseif ($orderColumn == 'specializare') {
    $orderColumn = "e.specializari";
}

$totalQuery = "SELECT COUNT(*) AS total FROM exp_jud.experti_tehnici e";
$totalRecords = $dbConnection->query($totalQuery)->fetchColumn();

$filteredRecords = $totalRecords;
if ($where != "1=1") {
    $filterQuery = "SELECT COUNT(*) AS total
                   FROM exp_jud.experti_tehnici e
                   INNER JOIN exp_jud.njudete j ON j.idJudet = e.id_judet
                   WHERE $where";
    try {
        $filteredRecords = $dbConnection->query($filterQuery)->fetchColumn();
    } catch (Exception $e) {
        $filteredRecords = 0;
    }
}

$specialiariCunume = getSpecializari();
$allUniqueSpecializations = [];
try {
    $stmt = $dbConnection->prepare("CALL get_date_incarcatura(?, ?, ?, ?, ?, ?)");
    $stmt->execute([$where, $orderColumn, $orderDir, $start, $length, $judetPrioritar]);
    if (!$stmt) {
        throw new Exception("Failed to execute statement");
    }
    $data = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $expertNume = htmlspecialchars($row['numeSpecialist']) . " " . htmlspecialchars($row['prenumeSpecialist']);
        $expertCNP = htmlspecialchars($row['cnpSpecialist'] ?? '');

        if (validCNP($expertCNP)) {
            $expertCNP = "<small class='text-dark rounded-pill my-1 mb-1'>
                            <i class='bi bi-person-badge'></i> CNP: $expertCNP
                          </small>";
        } else {
            $expertCNP = "<small class='text-danger rounded-pill my-1 mb-1' data-bs-toggle='tooltip'
                            data-bs-html='true' data-bs-placement='bottom' title='CNP invalid: $expertCNP'>
                            <i class='bi bi-exclamation-triangle-fill'></i> CNP invalid
                          </small>";
        }

        $autorizatie = "";
        if (strlen($row['legitimatie']) > 0) {
            $autorizatie = "<small class='text-dark text-muted rounded-pill mb-1'>
                              <i class='bi bi-card-heading'></i> Autorizație: {$row['legitimatie']}
                            </small>";
        }

        $email = "";
        if (strlen($row['email'] ?? '') > 0) {
            $email = "<p class='text-muted small'>
                        <i class='bi bi-envelope'></i> " . htmlspecialchars($row['email']) . "
                      </p>";
        }

        $expertNumeTD = "<div class='fw-bold text-primary mb-1'>$expertNume</div>
                        <div class='d-flex flex-wrap'>$expertCNP</div>
                        <div class='d-flex flex-wrap'>$autorizatie</div>
                        <div class='d-flex flex-wrap'>$email</div>";
        $localitate = htmlspecialchars($row['localitate'] ?? '');
        $adresaSpecialist = htmlspecialchars($row['adresaSpecialist'] ?? '');
        $judetTD = "<div class='p-1'><i class='bi bi-geo-alt'></i> " . htmlspecialchars($row['judet']) . "</div>";

        $expertAdresaTD = "<div class='p-1'>";
        if($localitate) {
            $expertAdresaTD .= "<div><i class='bi bi-geo-alt-fill text-danger'></i> <span class='fw-bold'>$localitate</span></div>";
            if(!empty($adresaSpecialist)) {
                $expertAdresaTD .= "<div class='text-muted small ms-4'>$adresaSpecialist</div>";
            }
        } else if(!empty($adresaSpecialist)) {
            $expertAdresaTD .= "<div><i class='bi bi-geo-alt text-secondary'></i> $adresaSpecialist</div>";
        } else {
            $expertAdresaTD .= "<div class='text-muted fst-italic'><i class='bi bi-dash-circle'></i> Adresă nedisponibilă</div>";
        }
        $expertAdresaTD .= "</div>";

        $incarcatura = $row['incarcatura'];
        $incarcaturaTD = formatIncarcatura(intval($incarcatura));

        $specializariArray = explode(',', $row['specializare']);
        $subspecializariArray = explode(',', $row['subspecializare']);

        $groupedSpecializations = [];
        for ($i = 0; $i < count($specializariArray); $i++) {
            $specId = trim($specializariArray[$i]);
            $subSpecId = trim($subspecializariArray[$i]);

            $specName = '';
            $subSpecName = '';

            foreach ($specialiariCunume as $spec) {
                if ($spec['id_specializare'] == $specId) {
                    $specName = $spec['nume_specializare'];
                }
                if ($spec['id_subspecializare'] == $subSpecId) {
                    $subSpecName = $spec['nume_subspecializare'];
                }
            }

            if (!isset($allUniqueSpecializations[$specName])) {
                $allUniqueSpecializations[$specName] = [];
            }
            if ($subSpecId != '0' && !in_array($subSpecName, $allUniqueSpecializations[$specName])) {
                $allUniqueSpecializations[$specName][] = $subSpecName;
            }
            if (!isset($groupedSpecializations[$specName])) {
                $groupedSpecializations[$specName] = [];
            }
            if ($subSpecId != '0') {
                $groupedSpecializations[$specName][] = $subSpecName;
            }
        }

        $specializariTD = '';
        foreach ($groupedSpecializations as $specName => $subSpecs) {
            $specializariTD .= "<div class='specializare-group mb-2'><i class='bi bi-award-fill'></i> $specName";
            if (!empty($subSpecs)) {
                $specializariTD .= "<ul class='list-unstyled ms-3 mb-0 small'>";
                foreach ($subSpecs as $subSpec) {
                    $specializariTD .= "<li><i class='bi bi-dash'></i> $subSpec</li>";
                }
                $specializariTD .= "</ul>";
            }
            $specializariTD .= "</div>";
        }

        $data[] = [
            'expert' => $expertNumeTD,
            'judet' => $judetTD,
            'adresa' => $expertAdresaTD,
            'telefon' => formatTelefon(htmlspecialchars($row['telefon']), htmlspecialchars($row['telefon2'])),
            'incarcatura' => $incarcaturaTD,
            'specializare' => $specializariTD,
            'idJudet' => $row['idJudet']
        ];
    }

    $formattedSpecializations = '';
    foreach ($allUniqueSpecializations as $specName => $subSpecs) {
        $formattedSpecializations .= "**" . $specName . "**<br>";
        foreach ($subSpecs as $subSpec) {
            $formattedSpecializations .= "&nbsp;&nbsp;&nbsp;&nbsp;$subSpec<br>";
        }
    }

    // debugging
    if (empty($data)) {
        print_r("Query executed with: where=$where, order=$orderColumn $orderDir, start=$start, length=$length");
    }

    $response = [
        "draw" => intval($draw),
        "recordsTotal" => intval($totalRecords),
        "recordsFiltered" => intval($filteredRecords),
        "data" => $data,
        "uniqueSpecializations" => $formattedSpecializations,
        "debug" => [
            "where" => $where,
            "orderColumn" => $orderColumn,
            "orderDir" => $orderDir,
            "start" => $start,
            "length" => $length
        ]
    ];

    // Log the search operation if search terms or filters were used
    $hasFilters = !empty($search) ||
        (isset($_POST['columns'][1]['search']['value']) && $_POST['columns'][1]['search']['value'] !== '') ||
        (isset($_POST['columns'][5]['search']['value']) && $_POST['columns'][5]['search']['value'] !== '');

    if ($hasFilters) {
        $searchParams = [
            'search_term' => $search,
            'judet_filter' => isset($_POST['columns'][1]['search']['value']) ? $_POST['columns'][1]['search']['value'] : null,
            'specializare_filter' => isset($_POST['columns'][5]['search']['value']) ? $_POST['columns'][5]['search']['value'] : null,
            'results_count' => $filteredRecords
        ];

        $searchDescription = "Căutare experți";
        if (!empty($search)) {
            $searchDescription .= ": $search";
        }
        if (isset($_POST['columns'][1]['search']['value']) && $_POST['columns'][1]['search']['value'] !== '') {
            $searchDescription .= " (județ: {$_POST['columns'][1]['search']['value']})";
        }
        if (isset($_POST['columns'][5]['search']['value']) && $_POST['columns'][5]['search']['value'] !== '') {
            $searchDescription .= " (specializare: {$_POST['columns'][5]['search']['value']})";
        }

        $stmt->closeCursor();
        log_search_operation($dbConnection, 'experti_search', $searchParams, [
            'description' => $searchDescription
        ]);
    }

    if (ob_get_length()) ob_clean();

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
} catch (Exception $e) {
    // Remove any previous output
    if (ob_get_length()) ob_clean();

    header('Content-Type: application/json; charset=utf-8');
    http_response_code(500);
    echo json_encode([
        "draw" => intval($draw),
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => [],
        "error" => $debug ? $e->getMessage() : "An error occurred ".$e->getMessage(),
        "debug" => [
            "where" => $where,
            "orderColumn" => $orderColumn,
            "orderDir" => $orderDir,
            "start" => $start,
            "length" => $length,
            "query_debug" => "Query executed with: where=$where, order=$orderColumn $orderDir, start=$start, length=$length"
        ]
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
ob_end_flush();