<?php
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_db_app_experti_old.php';
global $SESSION_id_judet;
$dbConnection = DatabasePool::getConnection();
$judetPrioritar = $SESSION_id_judet;

$exportType = isset($_POST['exportType']) ? $_POST['exportType'] : '';
$search = isset($_POST['search']) ? $_POST['search'] : '';
$judet = isset($_POST['judet']) ? $_POST['judet'] : '';
$specializare = isset($_POST['specializare']) ? $_POST['specializare'] : '';
$orderColumn = isset($_POST['orderColumn']) ? $_POST['orderColumn'] : 'e.nume';
$orderDir = isset($_POST['orderDir']) ? strtoupper($_POST['orderDir']) : 'ASC';
$start = 0; // Start from the first record
$length = 10000; // Get a large number of records

if (!in_array($exportType, ['excel', 'pdf'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid export type']);
}


if ($orderColumn == 'expert') {
    $orderColumn = "e.nume";
} elseif ($orderColumn == 'judet') {
    $orderColumn = "j.numeJudet";
} elseif ($orderColumn == 'adresa') {
    $orderColumn = "e.adresa";
} elseif ($orderColumn == 'telefon') {
    $orderColumn = "e.telefon";
} elseif ($orderColumn == 'incarcatura') {
    $orderColumn = "incarcatura";
} elseif ($orderColumn == 'specializare') {
    $orderColumn = "e.specializari";
}


$where = "1 = 1";
$data = [];

if (!empty($search)) {
    $search = str_replace(['\\', '&amp;', '&amp'], '', sanitizeInput($search));
    $searchTerms = explode(' ', trim($search));
    $searchConditions = [];
    $basicSearch = "(e.nume LIKE '%{$search}%' OR e.prenume LIKE '%{$search}%' OR e.cnp LIKE '%{$search}%' OR e.telefon LIKE '%{$search}%' OR e.legitimatie LIKE '%{$search}%')";

    if (count($searchTerms) > 1) {
        foreach ($searchTerms as $term) {
            if (strlen($term) > 0) {
                $searchConditions[] = "(e.nume LIKE '%{$term}%' OR e.prenume LIKE '%{$term}%')";
            }
        }
    }

    $where .= " AND (" . $basicSearch;
    if (!empty($searchConditions)) {
        $where .= " OR (" . implode(" AND ", $searchConditions) . ")";
    }
    $where .= ")";
}
if (!empty($judet)) {
    $judet = sanitizeInput($judet);
    $where .= " AND e.id_judet = '$judet'";
}
if (isset($_POST['specializare']) && !empty($_POST['specializare'])) {
    $specializareValues = explode(',', sanitizeInput($_POST['specializare']));
    if (count($specializareValues) == 2) {
        $filterSpecializare = intval($specializareValues[1]);
        $where .= " AND (FIND_IN_SET('{$filterSpecializare}', e.specializari) > 0 OR FIND_IN_SET('{$filterSpecializare}', e.subspecializari) > 0)";
    } else {
        $filterSpecializare = intval($specializare);
        if ($filterSpecializare > 0) {
            $where .= " AND (FIND_IN_SET('{$filterSpecializare}', e.specializari) > 0 OR FIND_IN_SET('{$filterSpecializare}', e.subspecializari) > 0)";
        }
    }
}

// Use the same stored procedure as in _cVizualizare.php
$specialiariCunume = getSpecializari();

// Debug - uncomment to see the structure of specializari
/*
if (isset($_GET['debug_spec']) && $_GET['debug_spec'] == '1') {
    header('Content-Type: application/json');
    echo json_encode([
        'specializari_count' => count($specialiariCunume),
        'first_few' => array_slice($specialiariCunume, 0, 10)
    ], JSON_PRETTY_PRINT);
    exit;
}
*/
try {
    // Add a debug parameter to the URL to see the data structure
    $debug_mode = isset($_GET['debug']) && $_GET['debug'] == '1';

    $stmt = $dbConnection->prepare("CALL get_date_incarcatura(?, ?, ?, ?, ?, ?)");
    $stmt->execute([$where, $orderColumn, $orderDir, $start, $length, $judetPrioritar]);
    if (!$stmt) {
        throw new Exception("Failed to execute statement");
    }
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug - show the structure of the first row if debug mode is enabled
    if ($debug_mode && !empty($result)) {
        header('Content-Type: application/json');
        echo json_encode([
            'first_row' => $result[0],
            'where' => $where,
            'orderColumn' => $orderColumn,
            'orderDir' => $orderDir,
            'start' => $start,
            'length' => $length,
            'judetPrioritar' => $judetPrioritar
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Close the cursor to prevent "Cannot execute queries while there are pending result sets" error
    $stmt->closeCursor();
} catch (Exception $e) {
    // Handle error
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Error executing stored procedure: ' . $e->getMessage()]);
    exit;
}
// Initialize allUniqueSpecializations to avoid undefined variable error
$allUniqueSpecializations = [];

foreach ($result as $row) {
    // We'll rebuild the specializare format from scratch
    // First, get the specializare and subspecializare IDs
    $specializariArray = [];
    $subspecializariArray = [];

    // Check different possible field names from the stored procedure
    if (isset($row['specializari']) && !empty($row['specializari'])) {
        $specializariArray = explode(',', $row['specializari']);
        $subspecializariArray = isset($row['subspecializari']) ? explode(',', $row['subspecializari']) : array_fill(0, count($specializariArray), '0');
    } elseif (isset($row['specializare_id']) && !empty($row['specializare_id'])) {
        $specializariArray = explode(',', $row['specializare_id']);
        $subspecializariArray = isset($row['subspecializare_id']) ? explode(',', $row['subspecializare_id']) : array_fill(0, count($specializariArray), '0');
    } elseif (isset($row['specializare']) && !empty($row['specializare']) && !is_string($row['specializare'])) {
        // Handle case where specializare might be directly in the row
        $specializariArray = explode(',', $row['specializare']);
        $subspecializariArray = isset($row['subspecializare']) ? explode(',', $row['subspecializare']) : array_fill(0, count($specializariArray), '0');
    }

    // If we couldn't find specializare IDs, try to use the already formatted specializare
    if (empty($specializariArray) && isset($row['specializare']) && !empty($row['specializare']) && is_string($row['specializare']) && strpos($row['specializare'], '<b>') !== false) {
        $specializariTD = $row['specializare'];
    } else {
        // Format the specializare based on the IDs
        $formattedSpecializari = [];
        $plainTextSpecializari = [];
        $formattedSubSpecializari = [];
        $plainTextSubSpecializari = [];

        // Make sure the arrays have the same length
        $count = min(count($specializariArray), count($subspecializariArray));

        // If we have specializare IDs but no names yet, look them up
        for ($i = 0; $i < $count; $i++) {
            $specId = trim($specializariArray[$i]);
            if (empty($specId)) continue;

            $subSpecId = isset($subspecializariArray[$i]) ? trim($subspecializariArray[$i]) : '0';

            // Find the specializare and subspecializare names
            $specName = '';
            $subSpecName = '';

            foreach ($specialiariCunume as $spec) {
                if ($spec['id_specializare'] == $specId) {
                    $specName = $spec['nume_specializare'];
                }
                if ($spec['id_subspecializare'] == $subSpecId) {
                    $subSpecName = $spec['nume_subspecializare'];
                }
            }

            // If we couldn't find the name, use the ID as fallback
            if (empty($specName)) {
                $specName = "Specializare $specId";
            }

            // Format based on whether there's a subspecializare
            if ($subSpecId != '0' && !empty($subSpecName)) {
                $formattedSpecializari[] = "<b>$specName</b><br>&nbsp;&nbsp;&nbsp;&nbsp;$subSpecName";
                $plainTextSpecializari[] = "$specName - $subSpecName";
                $formattedSubSpecializari[] = "<i>$subSpecName</i>";
                $plainTextSubSpecializari[] = "$subSpecName";
            } else {
                $formattedSpecializari[] = "<b>$specName</b>";
                $plainTextSpecializari[] = $specName;
            }
        }

        // Join all specializari with line breaks
        $specializariTD = implode("<br>", $formattedSpecializari);
        $subSpecializariTD = implode("<br>", $formattedSubSpecializari);
        $plainTextSpecializare = implode(", ", $plainTextSpecializari);
        $plainTextSubSpecializare = implode(", ", $plainTextSubSpecializari);

        // If we still don't have any specializare, try to use the raw specializare field
        if (empty($specializariTD) && isset($row['specializare']) && !empty($row['specializare'])) {
            $specializariTD = $row['specializare'];
            $plainTextSpecializare = $row['specializare'];
            $subSpecializariTD = $row['subspecializare'];
            $plainTextSubSpecializare = $row['subspecializare'];
        }

        // Store the plain text version for PDF export
        $row['plainTextSpecializare'] = $plainTextSpecializare;
    }

    // Store the formatted specializare in the data array
    $data[] = [
        'numeSpecialist' => $row['numeSpecialist'] ?? $row['nume'] ?? '',
        'prenumeSpecialist' => $row['prenumeSpecialist'] ?? $row['prenume'] ?? '',
        'judet' => $row['judet'] ?? '',
        'localitate' => $row['localitate'] ?? '',
        'adresaSpecialist' => $row['adresaSpecialist'] ?? $row['adresa'] ?? '',
        'telefon' => $row['telefon'] ?? '',
        'telefon2' => $row['telefon2'] ?? '',
        'incarcatura' => $row['incarcatura'] ?? '',
        'specializare' => $specializariTD,
        'subSpecializare' => $subSpecializariTD,
        'specializariTD' => $specializariTD,  // Store in both fields to be safe
        'plainTextSpecializare' => $row['plainTextSpecializare'] ?? ''
    ];
}

// Create a new database connection for logging to avoid issues with pending result sets
$logDbConnection = DatabasePool::getConnection();

// Log the export operation
log_data_operation($logDbConnection, 'export', 'experti_tehnici', null, [
    'description' => "Export date experți în format $exportType",
    'data_after' => [
        'export_type' => $exportType,
        'search' => $search,
        'judet' => $judet,
        'specializare' => $specializare,
        'order_column' => $orderColumn,
        'order_direction' => $orderDir,
        'record_count' => count($data)
    ]
]);

// Release the logging connection
DatabasePool::releaseConnection($logDbConnection);

// Release the main database connection before exporting
DatabasePool::releaseConnection($dbConnection);

if ($exportType == 'excel') {
    exportToExcel($data);
} elseif ($exportType == 'pdf') {
    exportToPDF($data);
}

function getCommonTableStructure()
{
    return [
        'headers' => [
            'Nr.crt.' => ['width' => '4%'],
            'Expert' => ['width' => '10%'],
            'Județ' => ['width' => '6%'],
            'Adresă' => ['width' => '7%'],
            'Telefon' => ['width' => '4%'],
            'Încărcătură' => ['width' => '4%'],
            'Specializare' => ['width' => '65%']
        ],
        'styles' => '
            table {
                width: 100%;
                border-collapse: collapse;
                font-size: 12px;
                table-layout: fixed;
            }
            th, td {
                padding: 5px;
                border: 1px solid #ddd;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
                text-align: left;
            }
            th:last-child, td:last-child {
                width: 65% !important;
            }
            td:nth-child(4), th:nth-child(4) {
                width: 7% !important;
            }
            h1 {
                font-size: 16px;
                text-align: center;
                margin-bottom: 10px;
            }
        '
    ];
}

function formatRowData($row, $index)
{
    $nume = htmlspecialchars($row['numeSpecialist'] ?? '') . ' ' . htmlspecialchars($row['prenumeSpecialist'] ?? '');
    $telefon = htmlspecialchars($row['telefon'] ?? '') . ' ' . htmlspecialchars($row['telefon2'] ?? '');

    // Use plain text specializare for export if available
    if (!empty($row['plainTextSpecializare'])) {
        $specializare = $row['plainTextSpecializare'];
    } else {
        // Fall back to HTML version if plain text is not available
        $specializare = $row['specializare'] ?? '';
        if (empty($specializare) && isset($row['specializariTD'])) {
            $specializare = $row['specializariTD'];
        }

        // Strip HTML tags if we're using the HTML version
        $specializare = strip_tags($specializare);
    }

    // Get subspecialization if available
    $subSpecializare = '';
    if (isset($row['subSpecializare']) && !empty($row['subSpecializare'])) {
        $subSpecializare = strip_tags($row['subSpecializare']);
    }

    return [
        'index' => $index + 1,
        'nume' => $nume,
        'judet' => htmlspecialchars($row['judet'] ?? ''),
        'localitate' => htmlspecialchars($row['localitate'] ?? ''),
        'adresa' => htmlspecialchars($row['adresaSpecialist'] ?? ''),
        'telefon' => $telefon,
        'incarcatura' => htmlspecialchars($row['incarcatura'] ?? ''),
        'specializare' => $specializare,
        'subSpecializare' => $subSpecializare
    ];
}

function exportToExcel($data)
{
    $tableStructure = getCommonTableStructure();
    $specialiariCunume = getSpecializari();

    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="experti-export-' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');

    echo chr(239) . chr(187) . chr(191); // BOM for UTF-8
    echo '<!DOCTYPE html>';
    echo '<html>';
    echo '<head>';
    echo '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
    echo '<style>' . $tableStructure['styles'] . '</style>';
    echo '</head>';
    echo '<body>';
    echo '<table>';

    echo '<tr>';
    foreach ($tableStructure['headers'] as $header => $props) {
        echo '<th>' . $header . '</th>';
    }
    echo '</tr>';

    foreach ($data as $i => $row) {
        $rowData = formatRowData($row, $i);

        // Get the original data for specialization and subspecialization
        $originalData = $data[$i];

        // Process specializations
        $specializariArray = [];
        $subspecializariArray = [];

        if (isset($originalData['specializare']) && !empty($originalData['specializare'])) {
            $specializariArray = explode(',', $originalData['specializare']);
        }

        if (isset($originalData['subSpecializare']) && !empty($originalData['subSpecializare'])) {
            $subspecializariArray = explode(',', $originalData['subSpecializare']);
        }

        // Map specialization and subspecialization IDs to names
        $specializariNames = [];
        $subspecializariNames = [];

        // Create a structured array to organize specializations and their subspecializations
        $structuredSpecializari = [];

        // Process each specialization ID
        foreach ($specializariArray as $specId) {
            $specId = trim($specId);
            if (empty($specId)) continue;

            // Find the name for this specialization ID
            foreach ($specialiariCunume as $spec) {
                if ($spec['id_specializare'] == $specId) {
                    $specializariNames[] = $spec['nume_specializare'];

                    // Add to structured array if not already there
                    if (!isset($structuredSpecializari[$spec['nume_specializare']])) {
                        $structuredSpecializari[$spec['nume_specializare']] = [];
                    }
                    break;
                }
            }
        }

        // Process each subspecialization ID and associate with its parent specialization
        foreach ($subspecializariArray as $subSpecId) {
            $subSpecId = trim($subSpecId);
            if (empty($subSpecId) || $subSpecId == '0') continue;

            // Find the name for this subspecialization ID
            foreach ($specialiariCunume as $spec) {
                if ($spec['id_subspecializare'] == $subSpecId) {
                    $subspecializariNames[] = $spec['nume_subspecializare'];

                    // Add to the appropriate specialization in the structured array
                    if (isset($structuredSpecializari[$spec['nume_specializare']])) {
                        $structuredSpecializari[$spec['nume_specializare']][] = $spec['nume_subspecializare'];
                    }
                    break;
                }
            }
        }

        // Format the specialization and subspecialization in a structured way
        $formattedSpecializare = '';

        // Build the formatted string with specializations as headers and indented subspecializations
        foreach ($structuredSpecializari as $specName => $subSpecs) {
            // Add the specialization name
            $formattedSpecializare .= $specName . "\n";

            // Add each subspecialization with indentation
            foreach ($subSpecs as $subSpec) {
                $formattedSpecializare .= "    " . $subSpec . "\n";
            }
        }

        // If we have no structured data, fall back to the original format
        if (empty($formattedSpecializare)) {
            $specializareStr = implode(', ', $specializariNames);
            $subSpecializareStr = implode(', ', $subspecializariNames);

            if (!empty($specializareStr) && !empty($subSpecializareStr)) {
                $formattedSpecializare = $specializareStr . "\n" . $subSpecializareStr;
            } elseif (!empty($specializareStr)) {
                $formattedSpecializare = $specializareStr;
            } elseif (!empty($subSpecializareStr)) {
                $formattedSpecializare = $subSpecializareStr;
            } else {
                $formattedSpecializare = $rowData['specializare'];
            }
        }

        // Prepare the display content for the specialization column
        $specializareDisplay = htmlspecialchars($formattedSpecializare);

        echo sprintf(
            '<tr>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
            </tr>',
            $rowData['index'],
            $rowData['nume'],
            "Județ: ".($rowData['judet'] ?? '')."\nLocalitate: ".($rowData['localitate'] ?? ''),
            $rowData['adresa'],
            $rowData['telefon'],
            $rowData['incarcatura'],
            $specializareDisplay
        );
    }
    echo '</table>';
    echo '</body>';
    echo '</html>';
    exit;
}

function exportToPDF($data)
{
    require_once('../vendor/autoload.php');
    $tableStructure = getCommonTableStructure();
    $specialiariCunume = getSpecializari();

    $mpdfSettings = [
        'orientation' => 'L',
        'margin_left' => 10,
        'margin_right' => 10,
        'margin_top' => 15,
        'margin_bottom' => 15,
        'format' => 'A4-L'
    ];

    $mpdf = new \Mpdf\Mpdf($mpdfSettings);

    $mpdf->SetTitle('Experți Export');
    $mpdf->SetAuthor('App ETJ');
    $mpdf->SetHeader('');
    $mpdf->SetFooter('Data export: ' . date('d.m.Y H:i') . '|Pagina {PAGENO} din {nb}|');

    $mpdf->WriteHTML('
        <style>
            table { width: 100%; border-collapse: collapse; }
            td, th { border: 1px solid #000; }
            td:last-child, th:last-child { width: 65% !important; }  /* Increased width for specializare */
            td:nth-child(4), th:nth-child(4) { width: 7% !important; }  /* Decreased width for address */
        </style>
    ');

    $html = '<style>' . $tableStructure['styles'] . '</style>';
    $html .= '<h1>Lista Experți</h1>';
    $html .= '<table><thead><tr>';

    foreach ($tableStructure['headers'] as $header => $props) {
        $html .= '<th>' . $header . '</th>';
    }
    $html .= '</tr></thead><tbody>';

    $contentHtml = '';
    $maxPages = 200;
    $pageLimitReached = false;
    $rowCount = min(count($data), $maxPages * 30);

    for ($i = 0; $i < $rowCount; $i++) {
        $rowData = formatRowData($data[$i], $i);

        // Get the original data for specialization and subspecialization
        $originalData = $data[$i];

        // Process specializations
        $specializariArray = [];
        $subspecializariArray = [];

        if (isset($originalData['specializare']) && !empty($originalData['specializare'])) {
            $specializariArray = explode(',', $originalData['specializare']);
        }

        if (isset($originalData['subSpecializare']) && !empty($originalData['subSpecializare'])) {
            $subspecializariArray = explode(',', $originalData['subSpecializare']);
        }

        // Map specialization and subspecialization IDs to names
        $specializariNames = [];
        $subspecializariNames = [];

        // Create a structured array to organize specializations and their subspecializations
        $structuredSpecializari = [];

        // Process each specialization ID
        foreach ($specializariArray as $specId) {
            $specId = trim($specId);
            if (empty($specId)) continue;

            // Find the name for this specialization ID
            foreach ($specialiariCunume as $spec) {
                if ($spec['id_specializare'] == $specId) {
                    $specializariNames[] = $spec['nume_specializare'];

                    // Add to structured array if not already there
                    if (!isset($structuredSpecializari[$spec['nume_specializare']])) {
                        $structuredSpecializari[$spec['nume_specializare']] = [];
                    }
                    break;
                }
            }
        }

        // Process each subspecialization ID and associate with its parent specialization
        foreach ($subspecializariArray as $subSpecId) {
            $subSpecId = trim($subSpecId);
            if (empty($subSpecId) || $subSpecId == '0') continue;

            // Find the name for this subspecialization ID
            foreach ($specialiariCunume as $spec) {
                if ($spec['id_subspecializare'] == $subSpecId) {
                    $subspecializariNames[] = $spec['nume_subspecializare'];

                    // Add to the appropriate specialization in the structured array
                    if (isset($structuredSpecializari[$spec['nume_specializare']])) {
                        $structuredSpecializari[$spec['nume_specializare']][] = $spec['nume_subspecializare'];
                    }
                    break;
                }
            }
        }

        // Format the specialization and subspecialization in a structured way for HTML
        $formattedHtml = '';

        // Build the formatted HTML with specializations as headers and indented subspecializations
        foreach ($structuredSpecializari as $specName => $subSpecs) {
            // Add the specialization name
            $formattedHtml .= htmlspecialchars($specName) . "<br>";

            // Add each subspecialization with indentation
            foreach ($subSpecs as $subSpec) {
                $formattedHtml .= "&nbsp;&nbsp;&nbsp;&nbsp;" . htmlspecialchars($subSpec) . "<br>";
            }
        }

        // If we have no structured data, fall back to the original format
        if (empty($formattedHtml)) {
            $specializareStr = implode(', ', $specializariNames);
            $subSpecializareStr = implode(', ', $subspecializariNames);

            if (!empty($specializareStr) && !empty($subSpecializareStr)) {
                $formattedHtml = htmlspecialchars($specializareStr) . "<br>" . htmlspecialchars($subSpecializareStr);
            } elseif (!empty($specializareStr)) {
                $formattedHtml = htmlspecialchars($specializareStr);
            } elseif (!empty($subSpecializareStr)) {
                $formattedHtml = htmlspecialchars($subSpecializareStr);
            } else {
                $formattedHtml = htmlspecialchars($rowData['specializare']);
            }
        }

        $contentHtml .= '<tr>';
        $contentHtml .= '<td>' . $rowData['index'] . '</td>';
        $contentHtml .= '<td>' . $rowData['nume'] . '</td>';
        $contentHtml .= "<td>Județ: ".($rowData['judet'] ?? '')."<br>Localitate: ".($rowData['localitate'] ?? '')."</td>";
        $contentHtml .= '<td>' . $rowData['adresa'] . '</td>';
        $contentHtml .= '<td>' . $rowData['telefon'] . '</td>';
        $contentHtml .= '<td>' . $rowData['incarcatura'] . '</td>';
        $contentHtml .= "<td>" . $formattedHtml . "</td>";
        $contentHtml .= '</tr>';
    }

    if ($rowCount < count($data)) {
        $pageLimitReached = true;
    }

    $contentHtml .= '</tbody></table>';

    $mpdf->WriteHTML($html . $contentHtml);

    if ($pageLimitReached && $mpdf->PageNo() >= $maxPages) {
        $mpdf->AddPage();
        $warningHtml = "<div style='color:red; font-weight:bold; text-align:center; margin-top:20px;'>Prea multe randuri generate, folosiți filtrele pentru restrângerea rezultatelor returnate.<br>Too much data.</div>";
        $mpdf->WriteHTML($warningHtml);
    }

    $mpdf->Output('experti-export-' . date('Y-m-d') . '.pdf', 'D');
    exit;
}