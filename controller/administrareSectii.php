<?php
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_functions.php';
$dbConnection = DatabasePool::getConnection();
global $SESSION_id_rol, $SESSION_id_instanta;

if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'getSectii':
            $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
            $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
            $search = $_POST['search']['value'] ?? '';
            $data = [];

            $showInactive = isset($_POST['showInactive']) && $_POST['showInactive'] === 'true';
            $whereClause = $showInactive ? "1=1" : "s.data_inactivare IS NULL";

            $instantaFilter = isset($_POST['instantaFilter']) && $_POST['instantaFilter'] !== '' ?
                intval($_POST['instantaFilter']) : null;

            if ($instantaFilter !== null) {
                $whereClause .= " AND s.id_instanta = :instantaFilter";
            }

            if (!empty($search)) {
                $search = str_replace(['\\', '&amp;', '&amp'], '', sanitizeInput($search));
                $searchTerms = explode(' ', trim($search));
                $searchConditions = [];

                $basicSearch = "(s.denumire LIKE :search OR i.den LIKE :search)";
                if (count($searchTerms) > 1) {
                    foreach ($searchTerms as $key => $term) {
                        if (strlen($term) > 0) {
                            $paramName = ":term{$key}";
                            $searchConditions[] = "(s.denumire LIKE $paramName OR i.den LIKE $paramName)";
                        }
                    }
                }

                $whereClause .= " AND (" . $basicSearch;
                if (!empty($searchConditions)) {
                    $whereClause .= " OR (" . implode(" AND ", $searchConditions) . ")";
                }
                $whereClause .= ")";
            }
            if ($SESSION_id_rol == 1) {
                // Admin vede toate secțiile
                $query = "SELECT s.id, s.denumire, i.den as instanta, s.id_instanta as idInstanta, s.data_inactivare
                      FROM exp_jud.z_sectii s
                      JOIN exp_jud.z_instante i ON i.id = s.id_instanta
                      WHERE $whereClause
                      ORDER BY s.id
                      LIMIT :length OFFSET :start";
            } else {
                // SpIT și alte roluri văd doar secțiile din instanța lor și din subordine
                $current_instance = intval($SESSION_id_instanta); // Sanitizăm valoarea
                $query = "WITH RECURSIVE instance_hierarchy AS (
                            -- Base case: current instance
                            SELECT id, id_instanta_sup, id_nivel
                            FROM exp_jud.z_instante
                            WHERE id = $current_instance
                            
                            UNION ALL
                            
                            -- Recursive case: get subordinate instances
                            SELECT i.id, i.id_instanta_sup, i.id_nivel
                            FROM exp_jud.z_instante i
                            INNER JOIN instance_hierarchy ih ON i.id_instanta_sup = ih.id
                        )
                        SELECT s.id, s.denumire, i.den as instanta, s.id_instanta as idInstanta, s.data_inactivare
                        FROM exp_jud.z_sectii s
                        JOIN exp_jud.z_instante i ON i.id = s.id_instanta
                        WHERE s.id_instanta IN (SELECT id FROM instance_hierarchy)
                        AND $whereClause
                        ORDER BY s.id
                        LIMIT :length OFFSET :start";
            }

            $stmt = $dbConnection->prepare($query);
            $stmt->bindValue(':length', $length, PDO::PARAM_INT);
            $stmt->bindValue(':start', $start, PDO::PARAM_INT);
            if ($instantaFilter !== null) {
                $stmt->bindValue(':instantaFilter', $instantaFilter, PDO::PARAM_INT);
            }

            if (!empty($search)) {
                $searchParam = '%' . $search . '%';
                $stmt->bindValue(':search', $searchParam, PDO::PARAM_STR);

                if (count($searchTerms) > 1) {
                    foreach ($searchTerms as $key => $term) {
                        if (strlen($term) > 0) {
                            $stmt->bindValue(":term{$key}", '%' . $term . '%', PDO::PARAM_STR);
                        }
                    }
                }
            }

            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if ($SESSION_id_rol == 1) {
                $countQuery = "SELECT COUNT(*) as total FROM exp_jud.z_sectii s WHERE " . ($showInactive ? "1=1" : "s.data_inactivare IS NULL");
            } else {
                $current_instance = intval($SESSION_id_instanta);
                $countQuery = "WITH RECURSIVE instance_hierarchy AS (
                            SELECT id, id_instanta_sup, id_nivel
                            FROM exp_jud.z_instante
                            WHERE id = $current_instance
                            
                            UNION ALL
                            
                            SELECT i.id, i.id_instanta_sup, i.id_nivel
                            FROM exp_jud.z_instante i
                            INNER JOIN instance_hierarchy ih ON i.id_instanta_sup = ih.id
                        )
                        SELECT COUNT(*) as total 
                        FROM exp_jud.z_sectii s
                        WHERE s.id_instanta IN (SELECT id FROM instance_hierarchy)
                        AND " . ($showInactive ? "1=1" : "s.data_inactivare IS NULL");
            }

            $countStmt = $dbConnection->query($countQuery);
            $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            $filteredQuery = "SELECT COUNT(*) as total 
                             FROM exp_jud.z_sectii s
                             JOIN exp_jud.z_instante i ON i.id = s.id_instanta
                             WHERE $whereClause";
            $filteredStmt = $dbConnection->prepare($filteredQuery);
            if ($instantaFilter !== null) {
                $filteredStmt->bindValue(':instantaFilter', $instantaFilter, PDO::PARAM_INT);
            }

            if (!empty($search)) {
                $filteredStmt->bindValue(':search', $searchParam, PDO::PARAM_STR);
                if (count($searchTerms) > 1) {
                    foreach ($searchTerms as $key => $term) {
                        if (strlen($term) > 0) {
                            $filteredStmt->bindValue(":term{$key}", '%' . $term . '%', PDO::PARAM_STR);
                        }
                    }
                }
            }

            $filteredStmt->execute();
            $filteredRecords = $filteredStmt->fetch(PDO::FETCH_ASSOC)['total'];

            foreach ($rows as $row) {
                $actionsHtml = '';

                if ($row['data_inactivare'] === null) {
                    // Buton de dezactivare pentru secțiile active
                    $actionsHtml .= '<button class="btn btn-sm btn-danger btn-deactivate-sectie" ' .
                        'data-sectie-id="' . $row['id'] . '" ' .
                        'data-sectie-denumire="' . htmlspecialchars($row['denumire']) . '">' .
                        '<i class="fas fa-times"></i></button>';
                } else {
                    // Buton de activare pentru secțiile inactive
                    $actionsHtml .= '<button class="btn btn-sm btn-success btn-activate-sectie" ' .
                        'data-sectie-id="' . $row['id'] . '" ' .
                        'data-sectie-denumire="' . htmlspecialchars($row['denumire']) . '">' .
                        '<i class="fas fa-check"></i></button>';
                }

                $data[] = [
                    'id' => $row['id'],
                    'denumire' => htmlspecialchars($row['denumire']),
                    'instanta' => htmlspecialchars($row['instanta']),
                    'data_inactivare' => $row['data_inactivare'],
                    'actiuni' => $actionsHtml
                ];
            }


            echo json_encode([
                'draw' => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $filteredRecords,
                'data' => $data
            ]);
            break;

        case 'addSectie':
            $denumire = $_POST['denumire'];
            $idInstanta = $_POST['idInstanta'];

            $checkQuery = "SELECT COUNT(*) FROM exp_jud.z_sectii 
                          WHERE LOWER(denumire) = LOWER(:denumire) 
                          AND id_instanta = :idInstanta 
                          AND data_inactivare IS NULL";

            $checkStmt = $dbConnection->prepare($checkQuery);
            $checkStmt->bindValue(':denumire', $denumire);
            $checkStmt->bindValue(':idInstanta', $idInstanta, PDO::PARAM_INT);
            $checkStmt->execute();

            if ($checkStmt->fetchColumn() > 0) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Există deja o secție cu această denumire în instanța selectată!'
                ]);
                exit;
            }

            $query = "INSERT INTO exp_jud.z_sectii (denumire, id_instanta) 
                     VALUES (:denumire, :idInstanta)";
            $stmt = $dbConnection->prepare($query);
            $stmt->bindValue(':denumire', $denumire);
            $stmt->bindValue(':idInstanta', $idInstanta, PDO::PARAM_INT);

            try {
                $stmt->execute();
                echo json_encode([
                    'success' => true,
                    'message' => 'Secția a fost adăugată cu succes!'
                ]);
            } catch (PDOException $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Eroare la adăugarea secției: ' . $e->getMessage()
                ]);
            }
            break;
        case 'updateSectie':
            $id = $_POST['id'];
            $denumire = $_POST['denumire'];
            $idInstanta = $_POST['idInstanta'];
            $originalInstanta = $_POST['originalInstanta'];

            // Verificăm dacă cineva încearcă să modifice instanța
            if ($idInstanta != $originalInstanta) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Nu este permisă modificarea instanței pentru o secție existentă!'
                ]);
                exit;
            }

            // Actualizăm doar denumirea
            $query = "UPDATE exp_jud.z_sectii SET denumire = :denumire WHERE id = :id";
            $stmt = $dbConnection->prepare($query);
            $stmt->bindValue(':denumire', $denumire);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);

            try {
                $stmt->execute();
                echo json_encode([
                    'success' => true,
                    'message' => 'Secția a fost actualizată cu succes!'
                ]);
            } catch (PDOException $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Eroare la actualizarea secției: ' . $e->getMessage()
                ]);
            }
            break;

        case 'deactivateSectie':
            $id = $_POST['id'];

            // Check if there are any active users in the section
            $checkQuery = "SELECT COUNT(*) as count FROM exp_jud.utilizatori WHERE idSectieInstanta = :id AND dataInactivare IS NULL";
            $checkStmt = $dbConnection->prepare($checkQuery);
            $checkStmt->bindValue(':id', $id, PDO::PARAM_INT);
            $checkStmt->execute();
            $count = $checkStmt->fetchColumn();

            if ($count > 0) {
                echo json_encode(['success' => false, 'message' => 'Nu se poate dezactiva secția deoarece există utilizatori activi în această secție!']);
                exit;
            }

            $query = "UPDATE exp_jud.z_sectii SET data_inactivare = CURRENT_TIMESTAMP WHERE id = :id";
            $stmt = $dbConnection->prepare($query);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);

            try {
                $stmt->execute();
                echo json_encode(['success' => true, 'message' => 'Secția a fost dezactivată cu succes!']);
            } catch (PDOException $e) {
                echo json_encode(['error' => false, 'message' => 'Eroare la dezactivarea secției: ' . $e->getMessage()]);
            }
            break;

        case 'activateSectie':
            $id = $_POST['id'];

            $query = "UPDATE exp_jud.z_sectii SET data_inactivare = NULL WHERE id = :id";
            $stmt = $dbConnection->prepare($query);
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);

            try {
                $stmt->execute();
                echo json_encode(['success' => true, 'message' => 'Secția a fost activată cu succes!']);
            } catch (PDOException $e) {
                echo json_encode(['error' => false, 'message' => 'Eroare la activarea secției: ' . $e->getMessage()]);
            }
            break;

        case 'getSectiiActive':
            try {
                $idInstanta = isset($_POST['idInstanta']) ? intval($_POST['idInstanta']) : null;

                $query = "SELECT id, denumire FROM exp_jud.z_sectii WHERE data_inactivare IS NULL";

                if ($idInstanta) {
                    $query .= " AND id_instanta = :idInstanta";
                }

                $query .= " ORDER BY denumire";

                $stmt = $dbConnection->prepare($query);

                if ($idInstanta) {
                    $stmt->bindValue(':idInstanta', $idInstanta, PDO::PARAM_INT);
                }

                $stmt->execute();
                $sectii = $stmt->fetchAll(PDO::FETCH_ASSOC);

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'data' => $sectii
                ]);
                exit;
            } catch (PDOException $e) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Eroare la încărcarea secțiilor: ' . $e->getMessage(),
                    'data' => []
                ]);
                exit;
            }
            break;

    }
}