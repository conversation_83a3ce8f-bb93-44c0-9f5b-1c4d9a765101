<?php
global $env;
header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../vendor/autoload.php';
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_session.php';

use Mpdf\Mpdf;

$idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);

if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['status' => 'error', 'message' => 'ID-ul expertizei lipsește']);
    exit;
}

$dbConnection = null;
try {
    $dbConnection = DatabasePool::getConnection();
    $id = $idEncoder->decode(sanitizeInput($_GET['id']));

    $sql = "SELECT e.*, 
           CONCAT(et.nume, ' ', et.prenume) as nume_expert,
           et.nume as expert_nume,
           et.prenume as expert_prenume,
           et.cnp as expert_cnp,
           j.nume<PERSON><PERSON><PERSON> as expert_judet,
           ss.nume_specializare,
           GROUP_CONCAT(DISTINCT ss.nume_specializare SEPARATOR ', ') as specializari,
           ns.status as status,
           et.legitimatie,
           u.utilizator as nume_utilizator,
           i.den as instanta,
           lo.output,
           l.localitate
    FROM exp_jud.expertize e
    LEFT JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    LEFT JOIN exp_jud.specializarisubspecializari ss ON e.idSpecializare = ss.id_specializare
    LEFT JOIN exp_jud.nstatus_expertize ns ON e.idStatusExpertiza = ns.id
    LEFT JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    LEFT JOIN exp_jud.njudete j ON et.id_judet = j.idJudet
    LEFT JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    left join exp_jud.lottery_output lo on lo.id_expertiza = e.id
    left join exp_jud.z_localitati l on l.id = et.id_localitate
    WHERE e.id = ?
    GROUP BY e.id";

    $stmt = $dbConnection->prepare($sql);
    $stmt->execute([$id]);
    $expertiza = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$expertiza) {
        throw new Exception('Expertiza nu a fost găsită');
    }

    $mpdf = new Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4',
        'margin_left' => 15,
        'margin_right' => 15,
        'margin_top' => 15,
        'margin_bottom' => 15
    ]);


//    $randomSauConvenit = $expertiza['output'] ? '' : 'convenit de părți';
    $randomSauConvenit = 'convenit de părți';
    if ($expertiza['output'] === null) {
        $randomSauConvenit = 'aleatoriu';
    }

    $html = "
    <div style='width: 100%; text-align: center;'>
        <h3>{$expertiza['instanta']}</h3>
        <p>" . data_afisare($expertiza['dataDesemnare']) . "</p>
    </div>
    
    <div>
        <p style='text-align: center;'>În dosarul nr. <strong>{$expertiza['nrDosar']}</strong>, a fost desemnat <b>$randomSauConvenit</b>, în calitate de expert</p>
        <p style='text-align: center;'><b>{$expertiza['expert_nume']} {$expertiza['expert_prenume']}</b></p>
        <p style='text-align: center;'>Având specializările următoare: <strong>{$expertiza['specializari']}</strong></p>
    </div>
    
    <div style='margin-top: 20px;'>
        <table style='width: 100%; border-collapse: collapse;'>
            <tr>
                <th style='width: 49%; border: 1px solid #000; padding: 8px;'>Detalii lucrare</th>
                <th style='width: 49%; border: 1px solid #000; padding: 8px;'>Detalii expert</th>
            </tr>
            <tr>
                <td style='border: 1px solid #000; padding: 8px; vertical-align: top;'>
                    <p><strong>Număr dosar:</strong> {$expertiza['nrDosar']}</p>
                    <p><strong>Specializări:</strong> {$expertiza['specializari']}</p>
                    <p><strong>Onorariu:</strong> {$expertiza['onorariu']} RON</p>
                    <p><strong>Data desemnării:</strong> " . date('d.m.Y', strtotime($expertiza['dataDesemnare'])) . "</p>
                </td>
                <td style='border: 1px solid #000; padding: 8px; vertical-align: top;'>
                    <p><strong>Nume Prenume:</strong> {$expertiza['expert_nume']} {$expertiza['expert_prenume']}</p>
                    <p><strong>CNP:</strong> " . anonimizareCNP($expertiza['expert_cnp']) . "</p>
                    <p><strong>Județ:</strong> {$expertiza['expert_judet']}</p>
                    <p><strong>Localitate:</strong> {$expertiza['localitate']}</p>
                    <p><strong>Autorizație:</strong> {$expertiza['legitimatie']}</p>
                </td>
            </tr>
        </table>
    </div>";
    if ($expertiza['output']) {
        $html .= "
        <div style='position: fixed; bottom: 0; left: 0; right: 0; padding: 10px 20px; border-top: 1px dashed #888; background-color: #f9f9f9; font-family: Arial, sans-serif; font-size: 12px;'>
            <div style='white-space: pre-wrap;'>
                " . nl2br(htmlspecialchars($expertiza['output'])) . "
            </div>
        </div>";
    }
    $mpdf->WriteHTML($html);

    header('Content-Type: application/pdf');
    $mpdf->Output("Expertiza_$expertiza[nrDosar].pdf", 'D');

} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
} finally {
    if ($dbConnection !== null) {
        DatabasePool::releaseConnection($dbConnection);
    }
}