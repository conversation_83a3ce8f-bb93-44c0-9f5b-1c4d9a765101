<?php
header('Content-Type: application/json; charset=UTF-8');

// Include session handling
require_once '../inc/cfg_db.php';
require_once '../vendor/autoload.php';
require_once '../inc/cfg_session.php'; // Include for SESSION_TIMEOUT constant

// Initialize session
$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');

// Check if user is logged in
$logged_in = $sesiune->get('logged_in');

// Check session timeout
$lastActivity = $sesiune->get('last_activity');
$currentTime = time();
$sessionTimeout = SESSION_TIMEOUT;
$sessionValid = false;

if ($logged_in && $lastActivity && ($currentTime - $lastActivity <= $sessionTimeout)) {
    // Session is valid, but don't update last_activity here
    // This is only done in update_activity.php when user interacts
    $sessionValid = true;
} else if ($logged_in) {
    // Session has expired, log the event
    $username = $sesiune->get('username');
    $user_id = $sesiune->get('id_utilizator');

    // Log session expiration if we have user info
    if ($username && $user_id) {
        $dbConnection = DatabasePool::getConnection();
        require_once '../inc/cfg_functions.php';
        log_auth_event($dbConnection, 'session_expired', [
            'user_id' => $user_id,
            'username' => $username,
            'description' => 'Sesiune expirată după ' . round(($currentTime - $lastActivity) / 60) . ' minute de inactivitate'
        ]);
    }

    // Destroy the session
    $session->destroy();
}

// Return session status
echo json_encode([
    'status' => $sessionValid ? 'valid' : 'expired',
    'message' => $sessionValid ? 'Session is valid' : 'Session has expired',
    'code' => $sessionValid ? 'session_valid' : 'session_expired',
    'last_activity' => $lastActivity,
    'current_time' => $currentTime,
    'time_diff' => $currentTime - $lastActivity
]);
