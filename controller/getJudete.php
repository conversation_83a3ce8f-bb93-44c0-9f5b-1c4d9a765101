<?php
header('Content-Type: application/json; charset=UTF-8');

require_once '../inc/cfg_db_app_experti_old.php';
global $conn_EXP_TEHNICI_OLD;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $query = "SELECT id, nume FROM NJudet";
    sqlsrv_query($conn_EXP_TEHNICI_OLD, "SET NAMES 'UTF8'");
    $stmt = sqlsrv_query($conn_EXP_TEHNICI_OLD, $query);

    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => sqlsrv_errors()
        ]);
        exit;
    }

    $data = [];
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $id = $row['id'];
//        $nume = mb_convert_encoding($row['nume'], 'UTF-8', 'ISO-8859-1');
        $nume = $row['nume'];
        $data[] = ['id' => $id, 'nume' => $nume];
    }

    echo json_encode(["data" => $data]);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'Eroare JSON: ' . json_last_error_msg();
    }
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
