<?php

class AccessControl {
    private static $menuItems = [
        'expertize' => [
            'url' => 'index.php',
            'text' => 'Expertize',
            'icon' => 'fa fa-book-reader'
        ],
        'desemnare' => [
            'url' => 'desemnare.php',
            'text' => 'Desemnare Expert',
            'icon' => 'fa fa-balance-scale'
        ],
        'vizualizare' => [
            'url' => 'vizualizareExperti.php',
            'text' => 'Vizualizare Experți',
            'icon' => 'fa fa-search'
        ],
        'stats' => [
            'url' => 'stats.php',
            'text' => 'Statistici',
            'icon' => 'fa fa-sticky-note'
        ],
        'admin' => [
            'url' => 'administrare.php',
            'text' => 'Administrare',
            'icon' => 'fa fa-user-cog'
        ],
        'audit' => [
            'url' => 'audit_logs.php',
            'text' => 'Audit Logs',
            'icon' => 'fa fa-user-friends'
        ]
    ];

    // User dropdown menu items
    private static $userDropdownItems = [
        'manual' => [
            'url' => 'inc/manual_utilizare.pdf',
            'text' => 'Manual utilizare',
            'description' => 'Ghid de utilizare aplicație',
            'icon' => 'fa fa-book text-primary',
            'target' => '_blank'
        ],
        'about' => [
            'url' => 'about.php',
            'text' => 'Despre aplicație',
            'description' => 'Informații despre versiune',
            'icon' => 'fa fa-info-circle text-primary',
            'target' => ''
        ],
        'logout' => [
            'url' => 'logout.php',
            'text' => 'Delogare',
            'description' => 'Ieșire din aplicație',
            'icon' => 'fa fa-sign-out-alt text-danger',
            'target' => ''
        ]
    ];

    private static $rolePermissions = [
        1 => [ // Admin
            'permissions' => ['expertize', 'desemnare', 'vizualizare', 'stats', 'admin', 'audit'],
            'default_page' => 'index.php'
        ],
        2 => [ // Grefier
            'permissions' => ['expertize', 'desemnare', 'vizualizare'],
            'default_page' => 'index.php'
        ],
        3 => [ // SPJC
            'permissions' => ['vizualizare', 'stats'],
            'default_page' => 'vizualizareExperti.php'
        ],
        4 => [ // Judecator
            'permissions' => ['vizualizare'],
            'default_page' => 'vizualizareExperti.php'
        ],
        5 => [ // SpIT
            'permissions' => ['vizualizare', 'admin'],
            'default_page' => 'vizualizareExperti.php'
        ]
    ];

    // Special users with custom permissions
    private static $specialUsers = [
        'adrian.diaconu' => [
            'permissions' => ['vizualizare', 'stats', 'audit'],
            'default_page' => 'vizualizareExperti.php'
        ],
//        'radu.hazsda' => [
//            'permissions' => ['vizualizare', 'stats', 'audit'],
//            'default_page' => 'vizualizareExperti.php'
//        ]
    ];

    private static $publicPages = [
        'login.php',
        'assets',
        'css',
        'js',
        'img',
        'favicon.ico',
        'inlocuire'//pagina inlocuire expert din comisie
    ];

    // Pages that are accessible through the user dropdown menu
    // These are checked separately in checkPageAccess()
    private static $dropdownPages = [
        'about.php' => ['IT', 'Administrator']
    ];

    public static function getMenuItems() {
        return self::$menuItems;
    }

    public static function getUserDropdownItems($roleId) {
        global $SESSION_username;
        $items = [];

        // Always include manual and logout
        $items['manual'] = self::$userDropdownItems['manual'];

        // Add about page only for IT and Admin roles
        if (self::hasRole($roleId, ['IT', 'Administrator'])) {
            $items['about'] = self::$userDropdownItems['about'];
        }

        // Always add logout at the end
        $items['logout'] = self::$userDropdownItems['logout'];

        return $items;
    }

    public static function getRolePermissions() {
        return self::$rolePermissions;
    }

    // Check if a user has special permissions
    private static function isSpecialUser($username) {
        return isset(self::$specialUsers[$username]);
    }

    // Get permissions for a special user
    private static function getSpecialUserPermissions($username) {
        return self::isSpecialUser($username) ? self::$specialUsers[$username]['permissions'] : [];
    }

    // Get default page for a special user
    private static function getSpecialUserDefaultPage($username) {
        return self::isSpecialUser($username) ? self::$specialUsers[$username]['default_page'] : null;
    }

    public static function getDefaultPageForRole($roleId) {
        global $SESSION_username;

        // Check for special user
        if (self::isSpecialUser($SESSION_username)) {
            return self::getSpecialUserDefaultPage($SESSION_username);
        }

        // Normal case for other users
        return isset(self::$rolePermissions[$roleId])
            ? self::$rolePermissions[$roleId]['default_page']
            : 'index.php';
    }


    public static function checkPageAccess($currentPage) {
        global $SESSION_id_rol, $SESSION_username;

        // Check if current page is public
        foreach (self::$publicPages as $page) {
            if (strpos($currentPage, $page) !== false) {
                return true;
            }
        }

        // If not public page, check if user is logged in
        if (!isset($SESSION_id_rol)) {
            return false;
        }

        // Check if the page is a dropdown menu page with role-based access
        if (isset(self::$dropdownPages[$currentPage])) {
            $allowedRoles = self::$dropdownPages[$currentPage];

            // Check if user's role is in the allowed roles for this page
            if (isset(self::$roleMap[$SESSION_id_rol]) && in_array(self::$roleMap[$SESSION_id_rol], $allowedRoles)) {
                return true;
            }

            // Log unauthorized access attempts to dropdown pages
            try {
                $dbConnection = DatabasePool::getConnection();
                log_page_access($dbConnection, $currentPage, [
                    'description' => "Unauthorized access attempt to dropdown page by role $SESSION_id_rol"
                ]);
                DatabasePool::releaseConnection($dbConnection);
            } catch (Exception $e) {
                writeToLogFile("Unauthorized access attempt to dropdown page $currentPage by role $SESSION_id_rol");
            }
            return false;
        }

        // Map pages to their permission keys
        $pagePermissionMap = array_combine(
            array_column(self::$menuItems, 'url'),
            array_keys(self::$menuItems)
        );

        // If page isn't in the map, deny access by default
        if (!isset($pagePermissionMap[$currentPage])) {
            try {
                $dbConnection = DatabasePool::getConnection();
                log_page_access($dbConnection, $currentPage, [
                    'description' => "Attempted access to unmapped page"
                ]);
                DatabasePool::releaseConnection($dbConnection);
            } catch (Exception $e) {
                writeToLogFile("Attempted access to unmapped page: $currentPage");
            }
            return false;
        }

        // Get the required permission for this page
        $requiredPermission = $pagePermissionMap[$currentPage];

        // Check for special user
        if (self::isSpecialUser($SESSION_username)) {
            $specialPermissions = self::getSpecialUserPermissions($SESSION_username);
            $hasPermission = in_array($requiredPermission, $specialPermissions);

            // Log unauthorized access attempts
            if (!$hasPermission) {
                try {
                    $dbConnection = DatabasePool::getConnection();
                    log_page_access($dbConnection, $currentPage, [
                        'description' => "Unauthorized access attempt by special user $SESSION_username"
                    ]);
                    DatabasePool::releaseConnection($dbConnection);
                } catch (Exception $e) {
                    writeToLogFile("Unauthorized access attempt to $currentPage by special user $SESSION_username");
                }
            }

            return $hasPermission;
        }

        // If role doesn't exist, deny access
        if (!isset(self::$rolePermissions[$SESSION_id_rol])) {
            try {
                $dbConnection = DatabasePool::getConnection();
                log_page_access($dbConnection, $currentPage, [
                    'description' => "Invalid role ID: $SESSION_id_rol"
                ]);
                DatabasePool::releaseConnection($dbConnection);
            } catch (Exception $e) {
                writeToLogFile("Invalid role ID: $SESSION_id_rol");
            }
            return false;
        }

        // Normal case for other users
        $hasPermission = in_array(
            $requiredPermission,
            self::$rolePermissions[$SESSION_id_rol]['permissions']
        );

        // Log unauthorized access attempts
        if (!$hasPermission) {
            try {
                $dbConnection = DatabasePool::getConnection();
                log_page_access($dbConnection, $currentPage, [
                    'description' => "Unauthorized access attempt by role $SESSION_id_rol"
                ]);
                DatabasePool::releaseConnection($dbConnection);
            } catch (Exception $e) {
                writeToLogFile("Unauthorized access attempt to $currentPage by role $SESSION_id_rol");
            }
        }

        return $hasPermission;
    }

    public static function getUserMenu($roleId) {
        global $SESSION_username;

        // Check for special user
        if (self::isSpecialUser($SESSION_username)) {
            $specialPermissions = self::getSpecialUserPermissions($SESSION_username);
            $allowedMenuItems = [];

            foreach ($specialPermissions as $permission) {
                if (isset(self::$menuItems[$permission])) {
                    $allowedMenuItems[$permission] = self::$menuItems[$permission];
                }
            }

            return $allowedMenuItems;
        }

        // If role doesn't exist, return empty array
        if (!isset(self::$rolePermissions[$roleId])) {
            return [];
        }

        // Normal case for other users
        $allowedMenuItems = [];
        foreach (self::$rolePermissions[$roleId]['permissions'] as $permission) {
            if (isset(self::$menuItems[$permission])) {
                $allowedMenuItems[$permission] = self::$menuItems[$permission];
            }
        }

        return $allowedMenuItems;
    }

    // Role ID to role name mapping
    private static $roleMap = [
        1 => 'Administrator',
        2 => 'Grefier',
        3 => 'SPJC',
        4 => 'Judecator',
        5 => 'IT'
    ];

    /**
     * Check if a user has a specific role or one of multiple roles
     *
     * @param int $roleId The user's role ID
     * @param array|string $roles Array of role names or single role name to check
     * @return bool True if user has any of the specified roles
     */
    public static function hasRole($roleId, $roles) {
        // If role doesn't exist in our map, deny access
        if (!isset(self::$roleMap[$roleId])) {
            return false;
        }

        // Convert single role to array for consistent processing
        if (!is_array($roles)) {
            $roles = [$roles];
        }

        // Check if user's role is in the allowed roles list
        return in_array(self::$roleMap[$roleId], $roles);
    }
}