<?php

class UniqueIdEncoder
{
    private $secret;
    private $dbConnection;

    public function __construct($secret) {
        $this->secret = $secret;
        $this->dbConnection = DatabasePool::getConnection();
    }

    /**
     * Encode an ID to a unique hash with random salt (MOST SECURE)
     * Each ID gets a unique random salt - completely unpredictable
     * Same ID will produce different hash each time
     * @param int $id The original ID
     * @return string|false The encoded hash or false on failure
     */
    public function encode($id) {
        if (!is_numeric($id) || $id < 0) {
            return false;
        }

        // Generate cryptographically secure random salt
        $salt = bin2hex(random_bytes(12)); // 24 character salt

        // Create hash with ID + secret + random salt
        $hashInput = $id . '|' . $salt . '|' . $this->secret;
        $hash = hash('sha256', $hashInput);

        // Take first 12 hex chars for shorter URL
        $shortHash = substr($hash, 0, 12);

        // Convert to binary and then URL-safe base64
        $encoded = rtrim(strtr(base64_encode(hex2bin($shortHash)), '+/', '-_'), '=');

        // Store mapping with salt and creation time
        if ($this->dbConnection) {
            $this->storeMapping($encoded, $id, $salt);
        }

        return $encoded;
    }

    /**
     * Decode a hashed ID back to original ID
     * @param string $hashedId The encoded hash
     * @return int|false The original ID or false if not found
     */
    public function decode($hashedId) {
        if (!$this->dbConnection) {
            throw new Exception("Database connection required for decoding");
        }

        try {
            $stmt = $this->dbConnection->prepare("SELECT original_id FROM id_mappings_secure WHERE hashed_id = ? LIMIT 1");
            $stmt->execute([$hashedId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? (int)$result['original_id'] : false;
        } catch (PDOException $e) {
            echo json_encode("UniqueIdEncoder decode error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Store the mapping between hash, original ID, and salt
     * @param string $hashedId
     * @param int $originalId
     * @param string $salt
     */
    private function storeMapping($hashedId, $originalId, $salt) {
        try {
            $stmt = $this->dbConnection->prepare("INSERT IGNORE INTO id_mappings_secure (hashed_id, original_id, salt, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$hashedId, $originalId, $salt]);
        } catch (PDOException $e) {
            error_log("UniqueIdEncoder store mapping error: " . $e->getMessage());
        }
    }

    /**
     * Verify if a hash corresponds to a specific ID by checking database
     * Note: Since we use random salt, we can't verify without database lookup
     * @param string $hashedId
     * @param int $originalId
     * @return bool
     */
    public function verify($hashedId, $originalId) {
        if (!$this->dbConnection) {
            return false;
        }

        try {
            $stmt = $this->dbConnection->prepare("SELECT COUNT(*) as count FROM id_mappings_secure WHERE hashed_id = ? AND original_id = ? LIMIT 1");
            $stmt->execute([$hashedId, $originalId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result && $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("UniqueIdEncoder verify error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Batch encode multiple IDs
     * @param array $ids Array of IDs to encode
     * @return array Associative array [originalId => hashedId]
     */
    public function encodeBatch($ids) {
        $result = [];
        foreach ($ids as $id) {
            $encoded = $this->encode($id);
            if ($encoded !== false) {
                $result[$id] = $encoded;
            }
        }
        return $result;
    }

    /**
     * Clean up old mappings to prevent database bloat
     * @param int $daysOld Number of days old to consider for cleanup
     * @return int|false Number of deleted records or false on error
     */
    public function cleanupOldMappings($daysOld = 30) {
        if (!$this->dbConnection) {
            return false;
        }

        try {
            $stmt = $this->dbConnection->prepare("DELETE FROM id_mappings_secure WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
            $stmt->execute([$daysOld]);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("UniqueIdEncoder cleanup error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get statistics about stored mappings
     * @return array|false Array with stats or false on error
     */
    public function getStats() {
        if (!$this->dbConnection) {
            return false;
        }

        try {
            $stmt = $this->dbConnection->prepare("
                SELECT 
                    COUNT(*) as total_mappings,
                    COUNT(DISTINCT original_id) as unique_ids,
                    MIN(created_at) as oldest_mapping,
                    MAX(created_at) as newest_mapping
                FROM id_mappings_secure
            ");
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("UniqueIdEncoder stats error: " . $e->getMessage());
            return false;
        }
    }
}

?>