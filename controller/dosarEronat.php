<?php
global $env;
require_once '../inc/cfg_session.php';
require_once '../inc/cfg_db.php';
require_once '../inc/cfg_functions.php';
global $SESSION_id_utilizator;

$idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);

function getModalDosarEronat()
{
    $html = '
    <div class="modal fade" id="dosarEronatModal" tabindex="-1" aria-labelledby="dosarEronatModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <div>
                        <h5 class="modal-title" id="dosarEronatModalLabel">Închidere dosar introdus eronat</h5>
                        <h6 class="modal-subtitle text-muted mt-1">Încărcați justificarea pentru închiderea erorii / expertizei</h6>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="dosarEronatForm">
                        <input type="hidden" id="expertizaId" name="expertizaId">
                        <div class="mb-3">
                            <label for="documentJustificativ" class="form-label">Document Justificativ (PDF)*</label>
                            <input type="file" class="form-control" id="documentJustificativ" name="documentJustificativ" accept=".pdf" required>
                        </div>
                         <div class="mb-3">
                            <label for="numarDosar" class="form-label">Număr dosar</label>
                            <input type="text" class="form-control" id="numarDosar" name="numarDosar" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="numePrenume" class="form-label">Expert</label>
                            <input type="text" class="form-control" id="numePrenume" name="numePrenume" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="cnp" class="form-label">CNP Expert</label>
                            <input type="text" class="form-control" id="cnp" name="cnp" readonly>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                    <button type="button" class="btn btn-primary" id="saveDosarEronat">Salvează</button>
                </div>
            </div>
        </div>
    </div>';
    return $html;
}

if (isset($_POST['getModalDosarEronat'])) {
    echo json_encode([
        'status' => 'success',
        'data' => getModalDosarEronat()
    ]);
    exit;
}


if (isset($_POST['id']) && !isset($_FILES['document'])) {
    try {
        $dbConnection = DatabasePool::getConnection();
        $id = $idEncoder->decode(sanitizeInput($_POST['id']));

        if ($id === false) {
            throw new Exception('ID-ul expertizei este invalid');
        }

        $sql = "SELECT e.*,
               CONCAT(et.nume, ' ', et.prenume) as nume_expert,
               ss.nume_specializare,
               ns.status as status
        FROM exp_jud.expertize e
        LEFT JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
        LEFT JOIN exp_jud.specializarisubspecializari ss ON e.idSpecializare = ss.id_specializare
        LEFT JOIN exp_jud.nstatus_expertize ns ON e.idStatusExpertiza = ns.id
        WHERE e.id = ?";

        $stmt = $dbConnection->prepare($sql);
        $stmt->execute([$id]);
        $expertiza = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$expertiza) {
            throw new Exception('Expertiza nu a fost găsită');
        }

        echo json_encode([
            'status' => 'success',
            'data' => $expertiza
        ]);

        DatabasePool::releaseConnection($dbConnection);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => "A aparut o eroare: " . $e->getMessage()
        ]);
    }
    exit;
}
if (isset($_FILES['document'])) {
    try {
        $dbConnection = DatabasePool::getConnection();
        if ($_FILES['document']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Eroare la încărcarea fișierului');
        }

        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $_FILES['document']['tmp_name']);
        finfo_close($finfo);

        if ($mimeType !== 'application/pdf') {
            throw new Exception('Doar fișierele PDF sunt acceptate');
        }

		$id = $idEncoder->decode(sanitizeInput($_POST['id']));
		$documentContent = file_get_contents($_FILES['document']['tmp_name']);

        $sql = "UPDATE exp_jud.expertize
                SET idStatusExpertiza = 4,
                    idUtilizatorInchidere = :idUtilizatorInchidere,
                    dataInchidere = NOW(),
                    document_justificativ = :document_justificativ
                WHERE id = :id";

        $stmt = $dbConnection->prepare($sql);
        $stmt->execute([
            ':idUtilizatorInchidere' => $SESSION_id_utilizator,
            ':document_justificativ' => $documentContent,
            ':id' => $id
        ]);

        $minus = "update exp_jud.workload w join exp_jud.expertize e on e.cnpExpert = w.CNP
                set incarcatura_app = incarcatura_app -1 where CNP = w.CNP AND idLoad > 0 and e.id = :id";
        $minus = $dbConnection->prepare($minus);
        $minus->execute([':id' => $id]);

        // Get expertise details for the audit log
        $getExpertizaQuery = "SELECT e.*,
                              CONCAT(et.nume, ' ', et.prenume) as nume_expert,
                              et.cnp as cnp_expert,
                              ss.nume_specializare
                       FROM exp_jud.expertize e
                       LEFT JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
                       LEFT JOIN exp_jud.specializarisubspecializari ss ON e.idSpecializare = ss.id_specializare
                       WHERE e.id = :id";
        $stmtExpertiza = $dbConnection->prepare($getExpertizaQuery);
        $stmtExpertiza->execute([':id' => $id]);
        $expertiza = $stmtExpertiza->fetch(PDO::FETCH_ASSOC);

        // Log the action of marking a case as erroneous
        log_data_operation($dbConnection, 'update', 'expertize', $id, [
            'description' => "Marcare dosar ca eronat: {$expertiza['nrDosar']}",
            'data_before' => [
                'idStatusExpertiza' => $expertiza['idStatusExpertiza'],
                'idUtilizatorInchidere' => $expertiza['idUtilizatorInchidere'],
                'dataInchidere' => $expertiza['dataInchidere']
            ],
            'data_after' => [
                'idStatusExpertiza' => 4,
                'idUtilizatorInchidere' => $SESSION_id_utilizator,
                'dataInchidere' => date('Y-m-d H:i:s'),
                'document_justificativ' => 'PDF document uploaded',
                'nrDosar' => $expertiza['nrDosar'],
                'expert' => $expertiza['nume_expert'],
                'cnp_expert' => $expertiza['cnp_expert'], // Store complete CNP
                'specializare' => $expertiza['nume_specializare']
            ]
        ]);

        // Returnăm doar mesajul de succes
        echo json_encode([
            'status' => 'success',
            'message' => 'Dosarul a fost marcat ca eronat și închis.'
        ]);

        DatabasePool::releaseConnection($dbConnection);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => "A aparut o eroare: " . $e->getMessage()
        ]);
    }
    exit;
}
