<?php
global $dbConnection, $env;
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'controller/UniqueIdEncoder.php';

$dbConnection = DatabasePool::getConnection();
$idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);

if (isset($_GET['eid'])) {
    $encodedID = sanitizeInput($_GET['eid']);
    $id = $idEncoder->decode(sanitizeInput($encodedID));
    if ($id === false) {
        require_once 'inc/cfg_footer.php';
        die('ID-ul expertizei este invalid');
    }
} else {
    require_once 'inc/cfg_footer.php';
    die('Missing ID parameter');
}

$sql = "SELECT * FROM exp_jud.expertize e LEFT JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp WHERE e.id = :id";
$stmt = $dbConnection->prepare($sql);
$stmt->execute([':id' => $id]);
$expertiza = $stmt->fetch(PDO::FETCH_ASSOC);
if(!$expertiza){
    require_once 'inc/cfg_footer.php';
    die('Expertiza nu a fost găsită');
}

?>
<link href="vendor/select2/select2/dist/css/select2.min.css" rel="stylesheet"/>
<div class="container-xxl py-5">
    <div class="container">
        <div class="row g-5 mb-5 align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <p class="text-center">
                    <b><u>Înlocuire</u> Expert Tehnic Judiciar (în cadrul comisiei de 3 experți)</b>
                    <input type="hidden" value="<?php echo $encodedID; ?>" id="expertizaId">
                </p>
            </div>
        </div>

        <div class="row g-5">
            <div class="col-lg-3 wow fadeInUp" data-wow-delay="0.1s">
                <p><i class="text-primary me-3 mt-2"></i>Număr dosar</p>
                <p><i class="text-primary me-3 mt-4"></i>Expertul din comisie ce se înlocuiește</p>
                <p><i class="text-primary me-3 mt-4"></i>Specializarea din dosar</p>
                <p><i class="text-primary me-3 mt-3"></i>Onorariu provizoriu stabilit în dosar/expert</p>
                <p><i class="text-primary me-3 mt-4"></i>Desemnare</p>
                <p><i class="text-primary me-3 mt-4"></i></p>
            </div>
            <div class="col-lg-9 wow fadeInUp" data-wow-delay="0.1s">
                <p><span class="mutable"><?php echo $expertiza['nrDosar'] ?></span></p>
                <p><?php echo "{$expertiza['nume']} {$expertiza['prenume']}" ?></p>
                <p>
                    <?php
                    $specializari = explode(',', $expertiza['idSpecializare']);
                    $subspecializari = explode(',', $expertiza['idSubspecializare']);
                    echo '<span class="mutable">' . getTextSpecializari($specializari, $subspecializari, '') . '</span>';
                    ?>
                </p>
                <p><?php echo $expertiza['onorariu'] ?> RON</p>
                <p>
                    <a class="btn btn-sm btn-outline-info py-2 px-2 mt-3" href="#" id="btnExpAleatoriu"
                       data-bs-toggle="tooltip" data-bs-html="true"
                       title="Se repartizează în mod <b><u>aleatoriu</u></b> pe baza încărcării">Expert
                        aleatoriu <i class="fa fa-info-circle"></i></a>
                    <a class="btn btn-sm btn-outline-primary py-2 px-2 mt-3" href="#" id="btnExpPropus"
                       data-bs-toggle="tooltip" data-bs-html="true"
                       title="Părțile <b><u>s-au înțeles</b></u> asupra unui expert tehnic judiciar">Expert
                        propus de părți <i class="fa fa-info-circle"></i></a>
                </p>
            </div>
        </div>


        <div class="row g-3">
            <div class="col-md-12 wow fadeInUp afisare4"></div>
        </div>


        <div class="row g-3">
            <div class="col-md-12 wow fadeInUp mesajeHandle"></div>
        </div>

    </div>
</div>


<?php
require_once 'inc/cfg_footer.php';
?>
<script src="vendor/select2/select2/dist/js/select2.min.js"></script>
<script src="assets/js/inlocuire.js"></script>

