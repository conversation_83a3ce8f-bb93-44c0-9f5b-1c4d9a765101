<?php
/**
 * Excel Export Utility Functions
 *
 * This file contains functions for exporting data to Excel with multiple sheets
 */

/**
 * Export data to Excel with multiple sheets
 *
 * @param array $sheets Array of sheet data, each with 'name', 'title', 'headers', and 'data'
 * @param string $filename Filename for the Excel file
 */
function exportToExcelMultipleSheets($sheets, $filename = null) {
    if ($filename === null) {
        $filename = 'export-' . date('Y-m-d') . '.xls';
    }

    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Start XML document for Excel with multiple sheets
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<?mso-application progid=\"Excel.Sheet\"?>\n";
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"';
    echo ' xmlns:o="urn:schemas-microsoft-com:office:office"';
    echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"';
    echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"';
    echo ' xmlns:html="http://www.w3.org/TR/REC-html40">';

    // Add styles
    echo '<Styles>';
    echo '<Style ss:ID="Default" ss:Name="Normal">';
    echo '<Alignment ss:Vertical="Bottom"/>';
    echo '<Borders/>';
    echo '<Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>';
    echo '<Interior/>';
    echo '<NumberFormat/>';
    echo '<Protection/>';
    echo '</Style>';
    echo '<Style ss:ID="s62">';
    echo '<Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" ss:Bold="1"/>';
    echo '<Interior ss:Color="#f2f2f2" ss:Pattern="Solid"/>';
    echo '</Style>';
    echo '<Style ss:ID="s63">';
    echo '<Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="14" ss:Color="#000000" ss:Bold="1"/>';
    echo '</Style>';
    echo '</Styles>';

    // Create each sheet
    foreach ($sheets as $index => $sheet) {
        $sheetName = $sheet['name'] ?? 'Sheet ' . ($index + 1);
        $sheetTitle = $sheet['title'] ?? $sheetName;
        $headers = $sheet['headers'] ?? [];
        $data = $sheet['data'] ?? [];
        $description = $sheet['description'] ?? '';

        // Convert null to empty string and sanitize sheet name for Excel
        $sheetNameValue = $sheetName === null ? '' : (string)$sheetName;
        // Excel sheet names cannot contain: [ ] * ? : / \
        $sheetNameValue = preg_replace('/[\[\]\*\?\/\\:]/u', '_', $sheetNameValue);
        // Sheet names must be 31 characters or less
        $sheetNameValue = mb_substr($sheetNameValue, 0, 31, 'UTF-8');
        $sheetNameValue = htmlspecialchars($sheetNameValue);

        // Start worksheet
        echo '<Worksheet ss:Name="' . $sheetNameValue . '">';

        // Calculate the correct row count
        // Start with 1 for the title row
        $rowCount = 1;
        // Add 1 if there's a description
        if (!empty($description)) {
            $rowCount += 2; // One for description and one for spacing
        }
        // Add 1 for headers if they exist
        if (!empty($headers)) {
            $rowCount += 1;
        }
        // Add the number of data rows
        $rowCount += count($data);

        // Ensure minimum row count
        $rowCount = max($rowCount, 10);

        echo '<Table ss:ExpandedColumnCount="' . max(count($headers), 1) . '" ss:ExpandedRowCount="' . $rowCount . '" x:FullColumns="1" x:FullRows="1">';

        // Set column widths
        for ($i = 0; $i < max(count($headers), 1); $i++) {
            echo '<Column ss:Width="120"/>';
        }

        // Add title
        $sheetTitleValue = $sheetTitle === null ? '' : (string)$sheetTitle;
        $sheetTitleValue = htmlspecialchars($sheetTitleValue);
        echo '<Row>';
        echo '<Cell ss:StyleID="s63" ss:MergeAcross="' . (max(count($headers), 1) - 1) . '"><Data ss:Type="String">' . $sheetTitleValue . '</Data></Cell>';
        echo '</Row>';

        // Add description if available
        if (!empty($description)) {
            $descriptionValue = $description === null ? '' : (string)$description;
            $descriptionValue = htmlspecialchars($descriptionValue);
            echo '<Row>';
            echo '<Cell ss:MergeAcross="' . (max(count($headers), 1) - 1) . '"><Data ss:Type="String">' . $descriptionValue . '</Data></Cell>';
            echo '</Row>';
            echo '<Row></Row>'; // Empty row for spacing
        }

        // Output headers
        if (!empty($headers)) {
            echo '<Row>';
            foreach ($headers as $header) {
                $headerValue = $header === null ? '' : (string)$header;
                $headerValue = htmlspecialchars($headerValue);
                echo '<Cell ss:StyleID="s62"><Data ss:Type="String">' . $headerValue . '</Data></Cell>';
            }
            echo '</Row>';
        }

        // Output data
        foreach ($data as $row) {
            echo '<Row>';
            foreach ($row as $cell) {
                $cellValue = $cell === null ? '' : (string)$cell;
                $cellValue = htmlspecialchars($cellValue);

                // Determine if the cell is numeric
                if (is_numeric($cell) && !is_string($cell)) {
                    echo '<Cell><Data ss:Type="Number">' . $cellValue . '</Data></Cell>';
                } else {
                    echo '<Cell><Data ss:Type="String">' . $cellValue . '</Data></Cell>';
                }
            }
            echo '</Row>';
        }

        echo '</Table>';
        echo '</Worksheet>';
    }

    echo '</Workbook>';
    exit;
}

/**
 * Format data for Excel export
 *
 * @param array $labels Labels for the data
 * @param array $values Values for the data
 * @return array Formatted data for Excel export
 */
function formatDataForExcel($labels, $values) {
    $data = [];
    for ($i = 0; $i < count($labels); $i++) {
        $data[] = [
            $labels[$i],
            isset($values[$i]) ? $values[$i] : ''
        ];
    }
    return $data;
}

/**
 * Format table data for Excel export
 *
 * @param string $tableHtml HTML table content
 * @return array Associative array with 'headers' and 'data'
 */
function formatTableHtmlForExcel($tableHtml) {
    $result = [
        'headers' => [],
        'data' => []
    ];

    // Create a DOM parser
    $dom = new DOMDocument();

    // Load the HTML (suppress warnings for HTML5 tags)
    @$dom->loadHTML('<?xml encoding="UTF-8">' . $tableHtml);

    // Get the table
    $tables = $dom->getElementsByTagName('table');
    if ($tables->length === 0) {
        return $result;
    }

    $table = $tables->item(0);

    // Get headers
    $headers = $table->getElementsByTagName('th');
    foreach ($headers as $header) {
        $result['headers'][] = trim($header->textContent);
    }

    // Get rows
    $rows = $table->getElementsByTagName('tr');
    foreach ($rows as $row) {
        $cells = $row->getElementsByTagName('td');
        if ($cells->length > 0) {
            $rowData = [];
            foreach ($cells as $cell) {
                $rowData[] = trim($cell->textContent);
            }
            $result['data'][] = $rowData;
        }
    }

    return $result;
}
?>
