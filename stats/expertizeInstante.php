<?php
// Only set JSON header if not exporting to Excel
if (!isset($_GET['export']) || $_GET['export'] !== 'excel') {
    header('Content-Type: application/json; charset=UTF-8');
}
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db.php';
require_once 'excel_export.php';
$dbConnection = DatabasePool::getConnection();

// Interogare pentru expertize per instanță
$selectExpertizePerInstanta = "
SELECT
    i.den denumire_instanta,
    COUNT(e.id) as nr_total_expertize,
    SUM(CASE
        WHEN e.idStatusExpertiza = 0 THEN 1
        ELSE 0
    END) as nr_expertize_inchise,
    SUM(CASE
        WHEN e.idStatusExpertiza = 1 THEN 1
        ELSE 0
    END) as nr_expertize_active,
    SUM(CASE
        WHEN e.idStatusExpertiza = 2 THEN 1
        ELSE 0
    END) as nr_expertize_inlocuite,
    SUM(CASE
        WHEN e.idStatusExpertiza = 4 THEN 1
        ELSE 0
    END) as nr_expertize_eronate
FROM exp_jud.expertize e
JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
JOIN exp_jud.z_instante i ON u.idInstanta = i.id
GROUP BY i.id, i.den
ORDER BY nr_expertize_eronate desc";
$rezultatExpertizePerInstanta = $dbConnection->query($selectExpertizePerInstanta)->fetchAll();

$labelsInstante = [];
$nr_expertizeTotal = $nr_expertizeInchise = $nr_expertizeActive = $nr_expertizeInlocuite = $nr_expertizeEronate = 0;

foreach ($rezultatExpertizePerInstanta as $row) {
    $labelsInstante[] = $row['denumire_instanta'];
    $nr_expertizeTotal += $row['nr_total_expertize'];
    $nr_expertizeInchise += $row['nr_expertize_inchise'];
    $nr_expertizeActive += $row['nr_expertize_active'];
    $nr_expertizeInlocuite += $row['nr_expertize_inlocuite'];
    $nr_expertizeEronate += $row['nr_expertize_eronate'];
}

// Interogare pentru expertize per status
$selectExpertizePerStatus = "
    SELECT
        ns.status denumire_status,
        COUNT(e.id) as nr_expertize
    FROM exp_jud.expertize e
    JOIN exp_jud.nstatus_expertize ns ON e.idStatusExpertiza = ns.id
    GROUP BY ns.id, ns.status
    ORDER BY nr_expertize desc";
$rezultatExpertizePerStatus = $dbConnection->query($selectExpertizePerStatus)->fetchAll();

$labelsStatus = [];
$valuesStatus = [];

foreach ($rezultatExpertizePerStatus as $row) {
    $labelsStatus[] = $row['denumire_status'];
    $valuesStatus[] = (int)$row['nr_expertize'];
}

// Interogare pentru expertize active/anulate per instanță
$selectExpertizeActiveAnulatePeInstanta = "
    SELECT
        i.den AS instanta_nume,
        SUM(CASE WHEN e.idStatusExpertiza = 4 THEN 1 ELSE 0 END) AS nr_anulate,
        SUM(CASE WHEN e.idStatusExpertiza != 4 THEN 1 ELSE 0 END) AS nr_active,
        COUNT(e.id) AS nr_total
    FROM exp_jud.expertize e
    JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    GROUP BY u.idInstanta, i.den
    HAVING SUM(CASE WHEN e.idStatusExpertiza = 4 THEN 1 ELSE 0 END) > 0
    ORDER BY nr_anulate DESC";

$rezultatExpertizeActiveAnulate = $dbConnection->query($selectExpertizeActiveAnulatePeInstanta)->fetchAll();

$labelsInstanteActiveAnulate = [];
$valuesActive = [];
$valuesAnulate = [];
$totalExpertize = 0;
$totalActive = 0;
$totalAnulate = 0;
foreach ($rezultatExpertizeActiveAnulate as $row) {
    $labelsInstanteActiveAnulate[] = $row['instanta_nume'];
    $valuesActive[] = (int)$row['nr_active'];
    $valuesAnulate[] = (int)$row['nr_anulate'];
    $totalExpertize += (int)$row['nr_total'];
    $totalActive += (int)$row['nr_active'];
    $totalAnulate += (int)$row['nr_anulate'];
}

// Release database connection
DatabasePool::releaseConnection($dbConnection);

// Handle Excel export if requested
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    // Prepare data for multiple sheets
    $sheets = [
        [
            'name' => 'Expertize per Instanta',
            'title' => 'Distribuția expertizelor pe instanțe',
            'description' => 'Total expertize: ' . $nr_expertizeTotal,
            'headers' => ['Instanță', 'Total expertize', 'Expertize închise', 'Expertize active', 'Expertize înlocuite', 'Expertize eronate'],
            'data' => array_map(function($index) use ($rezultatExpertizePerInstanta) {
                $row = $rezultatExpertizePerInstanta[$index];
                return [
                    $row['denumire_instanta'],
                    $row['nr_total_expertize'],
                    $row['nr_expertize_inchise'],
                    $row['nr_expertize_active'],
                    $row['nr_expertize_inlocuite'],
                    $row['nr_expertize_eronate']
                ];
            }, array_keys($rezultatExpertizePerInstanta))
        ],
        [
            'name' => 'Expertize per Status',
            'title' => 'Distribuția expertizelor pe status',
            'description' => 'Total expertize: ' . $nr_expertizeTotal,
            'headers' => ['Status', 'Număr expertize'],
            'data' => formatDataForExcel($labelsStatus, $valuesStatus)
        ],
        [
            'name' => 'Expertize Active-Eronate',
            'title' => 'Distribuția expertizelor active și eronate pe instanțe',
            'description' => 'Total expertize active: ' . $totalActive . ' | Total expertize eronate: ' . $totalAnulate,
            'headers' => ['Instanță', 'Expertize active', 'Expertize eronate'],
            'data' => array_map(function($index) use ($labelsInstanteActiveAnulate, $valuesActive, $valuesAnulate) {
                return [
                    $labelsInstanteActiveAnulate[$index],
                    $valuesActive[$index],
                    $valuesAnulate[$index]
                ];
            }, array_keys($labelsInstanteActiveAnulate))
        ]
    ];

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'statistici-expertize-instante-' . date('Y-m-d') . '.xls');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add export button
    $exportButton = '<div class="text-right mb-3">
        <a href="stats/expertizeInstante.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';

    $response = [
        'exportButton' => $exportButton,
        'charts' => [
            'instante' => [
                'type' => 'bar',
                'labels' => $labelsInstante,
                'datasets' => [
                    [
                        'label' => 'Expertize Active',
                        'data' => array_map(function ($row) {
                            return (int)$row['nr_expertize_active'];
                        }, $rezultatExpertizePerInstanta),
                        'backgroundColor' => 'rgba(75, 192, 75, 0.5)',
                        'borderColor' => 'rgba(75, 192, 75, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Expertize Închise',
                        'data' => array_map(function ($row) {
                            return (int)$row['nr_expertize_inchise'];
                        }, $rezultatExpertizePerInstanta),
                        'backgroundColor' => 'rgba(54, 162, 235, 0.5)',
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Expertize Înlocuite',
                        'data' => array_map(function ($row) {
                            return (int)$row['nr_expertize_inlocuite'];
                        }, $rezultatExpertizePerInstanta),
                        'backgroundColor' => 'rgba(255, 206, 86, 0.5)',
                        'borderColor' => 'rgba(255, 206, 86, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Expertize Eronate',
                        'data' => array_map(function ($row) {
                            return (int)$row['nr_expertize_eronate'];
                        }, $rezultatExpertizePerInstanta),
                        'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'maintainAspectRatio' => false,
                    'scales' => [
                        'x' => [
                            'stacked' => true,
                        ],
                        'y' => [
                            'stacked' => true,
                            'beginAtZero' => true,
                            'ticks' => [
                                'stepSize' => 1
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => [
                                'Distribuția expertizelor pe instanțe',
                                'Total expertize: ' . $nr_expertizeTotal,
                                'Expertize active: ' . $nr_expertizeActive,
                                'Expertize închise: ' . $nr_expertizeInchise,
                                'Expertize înlocuite: ' . $nr_expertizeInlocuite,
                                'Expertize eronate: ' . $nr_expertizeEronate
                            ]
                        ]
                    ]
                ]
            ],
            'status' => [
                'type' => 'bar',
                'labels' => $labelsStatus,
                'datasets' => [
                    [
                        'label' => 'Număr expertize',
                        'data' => $valuesStatus,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.5)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'maintainAspectRatio' => false,
                    'scales' => [
                        'y' => [
                            'beginAtZero' => true,
                            'ticks' => [
                                'stepSize' => 1
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Distribuția expertizelor pe status'
                        ]
                    ]
                ]
            ],
            'active_anulate' => [
                'type' => 'bar',
                'labels' => $labelsInstanteActiveAnulate,
                'datasets' => [
                    [
                        'label' => 'Expertize Active',
                        'data' => $valuesActive,
                        'backgroundColor' => 'rgba(75, 192, 75, 0.5)',
                        'borderColor' => 'rgba(75, 192, 75, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Expertize Eronate',
                        'data' => $valuesAnulate,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'maintainAspectRatio' => false,
                    'indexAxis' => 'y',
                    'scales' => [
                        'x' => [
                            'stacked' => true,
                            'beginAtZero' => true,
                            'ticks' => [
                                'stepSize' => 1
                            ]
                        ],
                        'y' => [
                            'stacked' => true
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Distribuția expertizelor pe instanțe după status'
                        ]
                    ]
                ]
            ]
        ]
    ];

    echo json_encode($response);
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
?>
