<?php
// Only set JSON header if not exporting to Excel
if (!isset($_GET['export']) || $_GET['export'] !== 'excel') {
    header('Content-Type: application/json; charset=UTF-8');
}
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db.php';
require_once 'excel_export.php';
$dbConnection = DatabasePool::getConnection();
$idEncoder = new UniqueIdEncoder($env["HASH_SECRET"]);

$selectDosareInchiseEronat = "select u.utilizator, i.den, e.* from exp_jud.expertize e
join exp_jud.utilizatori u on u.id = e.idUtilizatorInchidere
join exp_jud.z_instante i on i.id = u.idInstanta
where document_justificativ is not null
order by e.dataInchidere desc";
$selectDosareInchiseEronat = $dbConnection->query($selectDosareInchiseEronat)->fetchAll();

$i=0;$tr='';
$excelData = [];
foreach ($selectDosareInchiseEronat as $row) {
    $i++;
    $utilizatorInchidere = $row['utilizator'];
    $instanta = $row['den'];
    $dataInchidere = data_afisare($row['dataInchidere']);
    $document_justificativ = "<a href='../controller/viewDocument.php?id=" . $idEncoder->encode($row['id']) . "'
                                target='_blank' class='btn btn-sm btn-danger'><i class='fa fa-file-pdf'></i>
                                Vezi document
                            </a>";
    $tr .= "
        <tr>
            <td>$i</td>
            <td>$utilizatorInchidere</td>
            <td>$instanta</td>
            <td>$dataInchidere</td>
            <td>$document_justificativ</td>
        </tr>";

    // Store data for Excel export
    $excelData[] = [
        $i,
        $utilizatorInchidere,
        $instanta,
        $dataInchidere,
        'Document disponibil'
    ];
}

// Handle Excel export if requested
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    $sheets = [
        [
            'name' => 'Dosare inchise eronat',
            'title' => 'Dosare închise eronat',
            'description' => 'Total dosare: ' . count($excelData),
            'headers' => ['Nr.crt.', 'Utilizator închidere', 'Instanță', 'Data închidere', 'Document justificativ'],
            'data' => $excelData
        ]
    ];

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'dosare-inchise-eronat-' . date('Y-m-d') . '.xls');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add export button
    $exportButton = '<div class="text-right mb-3">
        <a href="stats/dosareEronate.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';

    $data = "
    <table class='table table-hover'>
        <thead>
        <tr>
            <th>Nr.crt.</th>
            <th>Utilizator inchidere</th>
            <th>Instanta</th>
            <th>Data inchidere</th>
            <th>Document justificativ</th>
        </tr>
        </thead>
        <tbody>$tr</tbody>
    </table>";
    echo json_encode(["exportButton" => $exportButton, "data" => $data]);
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
?>