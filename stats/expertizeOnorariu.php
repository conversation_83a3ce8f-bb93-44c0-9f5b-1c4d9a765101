<?php
$basePath = __DIR__ . '/..';

// Check for Excel export first to avoid header issues
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    require_once $basePath . '/inc/cfg_functions.php';
    require_once $basePath . '/inc/cfg_db.php';
    require_once 'excel_export.php';

    $dbConnection = DatabasePool::getConnection();

    // Get the data needed for export
    $selectSpecializari = "SELECT ss.id_specializare, ss.nume_specializare, COUNT(DISTINCT e.id) AS total_expertize, MIN(e.onorariu) AS min_onorariu, MAX(e.onorariu) AS max_onorariu, AVG(e.onorariu) AS avg_onorariu, SUM(e.onorariu) AS sum_onorariu FROM exp_jud.expertize e JOIN exp_jud.specializarisubspecializari ss ON FIND_IN_SET(ss.id_specializare, e.idSpecializare) > 0 WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0 and e.id not in (8666, 12060)
GROUP BY ss.id_specializare, ss.nume_specializare ORDER BY avg_onorariu DESC, ss.nume_specializare ASC;";

    $selectSubspecializari = "SELECT ss.id_subspecializare, ss.nume_subspecializare, ss.id_specializare, ss.nume_specializare, COUNT(DISTINCT e.id) AS total_expertize, MIN(e.onorariu) AS min_onorariu, MAX(e.onorariu) AS max_onorariu, AVG(e.onorariu) AS avg_onorariu, SUM(e.onorariu) AS sum_onorariu FROM exp_jud.expertize e JOIN exp_jud.specializarisubspecializari ss ON FIND_IN_SET(ss.id_subspecializare, e.idSubspecializare) > 0 WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0 	and e.id not in (8666, 12060)
 GROUP BY ss.id_subspecializare, ss.nume_subspecializare, ss.id_specializare, ss.nume_specializare ORDER BY avg_onorariu DESC, ss.nume_specializare ASC, ss.nume_subspecializare ASC;";

    $selectTotalStats = "SELECT COUNT(DISTINCT id) AS total_expertize, MIN(onorariu) AS min_onorariu, MAX(onorariu) AS max_onorariu, AVG(onorariu) AS avg_onorariu, SUM(onorariu) AS sum_onorariu FROM exp_jud.expertize e WHERE idStatusExpertiza = 1 AND onorariu > 0 and e.id not in (8666, 12060)
;";

    $rezultatSpecializari = $dbConnection->query($selectSpecializari)->fetchAll(PDO::FETCH_ASSOC);
    $rezultatSubspecializari = $dbConnection->query($selectSubspecializari)->fetchAll(PDO::FETCH_ASSOC);
    $rezultatTotalStats = $dbConnection->query($selectTotalStats)->fetchAll(PDO::FETCH_ASSOC);

    // Prepare data for Excel export - Specializari
    $specializariData = [];
    foreach ($rezultatSpecializari as $row) {
        $specializariData[] = [
            $row['nume_specializare'],
            $row['total_expertize'],
            number_format($row['min_onorariu'], 2, '.', ','),
            number_format($row['max_onorariu'], 2, '.', ','),
            number_format($row['avg_onorariu'], 2, '.', ','),
            number_format($row['sum_onorariu'], 2, '.', ',')
        ];
    }

    // Prepare data for Excel export - Subspecializari
    $subspecializariData = [];
    foreach ($rezultatSubspecializari as $row) {
        $subspecializariData[] = [
            $row['nume_specializare'],
            $row['nume_subspecializare'],
            $row['total_expertize'],
            number_format($row['min_onorariu'], 2, '.', ','),
            number_format($row['max_onorariu'], 2, '.', ','),
            number_format($row['avg_onorariu'], 2, '.', ','),
            number_format($row['sum_onorariu'], 2, '.', ',')
        ];
    }

    // Prepare data for Excel export - Total Stats
    $totalStatsData = [];
    foreach ($rezultatTotalStats as $row) {
        $totalStatsData[] = [
            $row['total_expertize'],
            number_format($row['min_onorariu'], 2, '.', ','),
            number_format($row['max_onorariu'], 2, '.', ','),
            number_format($row['avg_onorariu'], 2, '.', ','),
            number_format($row['sum_onorariu'], 2, '.', ',')
        ];
    }

    $sheets = [
        [
            'name' => 'Statistici generale',
            'title' => 'Statistici generale onorarii',
            'description' => 'Statistici generale pentru toate expertizele cu onorarii',
            'headers' => ['Total expertize', 'Onorariu minim', 'Onorariu maxim', 'Onorariu mediu', 'Suma totală onorarii'],
            'data' => $totalStatsData
        ],
        [
            'name' => 'Specializari',
            'title' => 'Statistici onorarii pe specializări',
            'description' => 'Total specializări: ' . count($rezultatSpecializari),
            'headers' => ['Specializare', 'Total expertize', 'Onorariu minim', 'Onorariu maxim', 'Onorariu mediu', 'Suma totală onorarii'],
            'data' => $specializariData
        ],
        [
            'name' => 'Subspecializari',
            'title' => 'Statistici onorarii pe subspecializări',
            'description' => 'Total subspecializări: ' . count($rezultatSubspecializari),
            'headers' => ['Specializare', 'Subspecializare', 'Total expertize', 'Onorariu minim', 'Onorariu maxim', 'Onorariu mediu', 'Suma totală onorarii'],
            'data' => $subspecializariData
        ]
    ];

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'statistici-onorarii-' . date('Y-m-d') . '.xls');
    exit;

    // Release database connection
    DatabasePool::releaseConnection($dbConnection);
} else {
    header('Content-Type: application/json; charset=UTF-8');

    require_once $basePath . '/inc/cfg_functions.php';
    require_once $basePath . '/inc/cfg_db.php';

    $dbConnection = DatabasePool::getConnection();

    $selectSpecializari = "
SELECT
    ss.id_specializare,
    ss.nume_specializare,
    COUNT(DISTINCT e.id) AS total_expertize,
    MIN(e.onorariu) AS min_onorariu,
    MAX(e.onorariu) AS max_onorariu,
    AVG(e.onorariu) AS avg_onorariu,
    SUM(e.onorariu) AS sum_onorariu
FROM exp_jud.expertize e
JOIN exp_jud.specializarisubspecializari ss ON
    FIND_IN_SET(ss.id_specializare, e.idSpecializare) > 0
WHERE e.idStatusExpertiza = 1
    AND e.onorariu > 0
    AND e.idSubspecializare REGEXP '^0(,0)*$'
	and e.id not in (8666, 12060)
GROUP BY ss.id_specializare, ss.nume_specializare
ORDER BY avg_onorariu DESC, ss.nume_specializare ASC;";

    $selectSubspecializari = "
SELECT
    ss.id_subspecializare,
    ss.nume_subspecializare,
    ss.id_specializare,
    ss.nume_specializare,
    COUNT(DISTINCT e.id) AS total_expertize,
    MIN(e.onorariu) AS min_onorariu,
    MAX(e.onorariu) AS max_onorariu,
    AVG(e.onorariu) AS avg_onorariu,
    SUM(e.onorariu) AS sum_onorariu
FROM exp_jud.expertize e
JOIN exp_jud.specializarisubspecializari ss ON
    FIND_IN_SET(ss.id_subspecializare, e.idSubspecializare) > 0
WHERE e.idStatusExpertiza = 1
    AND e.onorariu > 0
	and e.id not in (8666, 12060)
    AND NOT e.idSubspecializare REGEXP '^0(,0)*$'
GROUP BY ss.id_subspecializare, ss.nume_subspecializare, ss.id_specializare, ss.nume_specializare
ORDER BY avg_onorariu DESC, ss.nume_specializare ASC, ss.nume_subspecializare ASC;";

    $rezultatSpecializari = $dbConnection->query($selectSpecializari)->fetchAll(PDO::FETCH_ASSOC);
    $rezultatSubspecializari = $dbConnection->query($selectSubspecializari)->fetchAll(PDO::FETCH_ASSOC);

    $selectTotalStats = "
SELECT
    COUNT(DISTINCT id) as total_expertize,
    MIN(onorariu) as min_onorariu,
    MAX(onorariu) as max_onorariu,
    AVG(onorariu) as avg_onorariu,
    SUM(onorariu) as sum_onorariu
FROM exp_jud.expertize e
WHERE e.onorariu > 0 and e.idStatusExpertiza = 1
	and e.id not in (8666, 12060)
;";
    $totalStats = $dbConnection->query($selectTotalStats)->fetch(PDO::FETCH_ASSOC);

    $response = [
        'total_stats' => $totalStats,
        'specializari' => $rezultatSpecializari,
        'subspecializari' => $rezultatSubspecializari
    ];

// Format data for charts if POST request
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Add export button
        $exportButton = '<div class="text-right mb-3">
        <a href="stats/expertizeOnorariu.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';
        // Prepare data for top 10 specializari chart
        $labelsSpecializari = [];
        $valuesMinSpecializari = [];
        $valuesMaxSpecializari = [];
        $valuesAvgSpecializari = [];
        $specializariTop10 = array_slice($rezultatSpecializari, 0, 10); // Get top 10

        foreach ($specializariTop10 as $row) {
            $labelsSpecializari[] = $row['nume_specializare'];
            $valuesMinSpecializari[] = round((float)$row['min_onorariu'], 2);
            $valuesMaxSpecializari[] = round((float)$row['max_onorariu'], 2);
            $valuesAvgSpecializari[] = round((float)$row['avg_onorariu'], 2);
        }

        $labelsSubspecializari = [];
        $valuesMinSubspecializari = [];
        $valuesMaxSubspecializari = [];
        $valuesAvgSubspecializari = [];
        $subspecializariTop10 = array_slice($rezultatSubspecializari, 0, 10); // Get top 10

        foreach ($subspecializariTop10 as $row) {
            $labelsSubspecializari[] = $row['nume_subspecializare'] . ' (' . $row['nume_specializare'] . ')';
            $valuesMinSubspecializari[] = round((float)$row['min_onorariu'], 2);
            $valuesMaxSubspecializari[] = round((float)$row['max_onorariu'], 2);
            $valuesAvgSubspecializari[] = round((float)$row['avg_onorariu'], 2);
        }

        $tabelSpecializari = '<div class="table-responsive mt-5">
        <h4 class="mb-3">Statistici onorarii pe specializări</h4>
        <table class="table table-striped table-bordered table-hover sortable-table" id="table-specializari">
            <thead class="table-primary">
                <tr>
                    <th data-sort="string">Specializare <i class="fas fa-sort"></i></th>
                    <th data-sort="int">Număr expertize <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Onorariu minim (RON) <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Onorariu maxim (RON) <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Onorariu mediu (RON) <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Total onorarii (RON) <i class="fas fa-sort"></i></th>
                </tr>
                <tr class="table-info no-sort">
                    <th>TOTAL</th>
                    <th>' . number_format($totalStats['total_expertize'], 0, ',', '.') . '</th>
                    <th>' . number_format(round($totalStats['min_onorariu'], 2), 2, ',','.') . '</th>
                    <th>' . number_format(round($totalStats['max_onorariu'], 2), 2, ',','.') . '</th>
                    <th>' . number_format(round($totalStats['avg_onorariu'], 2), 2, ',','.') . '</th>
                    <th>' . number_format(round($totalStats['sum_onorariu'], 2), 2, ',','.') . '</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($rezultatSpecializari as $row) {
            $tabelSpecializari .= '<tr>
            <td>' . htmlspecialchars($row['nume_specializare']) . '</td>
            <td>' . (int)$row['total_expertize'] . '</td>
            <td>' . round((float)$row['min_onorariu'], 2) . '</td>
            <td>' . round((float)$row['max_onorariu'], 2) . '</td>
            <td>' . round((float)$row['avg_onorariu'], 2) . '</td>
            <td>' . round((float)$row['sum_onorariu'], 2) . '</td>
        </tr>';
        }

        $tabelSpecializari .= '</tbody></table></div>';

        $totalSubspecializari = array_sum(array_column($rezultatSubspecializari, 'total_expertize'));
        $minSubspecializari = min(array_column($rezultatSubspecializari, 'min_onorariu'));
        $maxSubspecializari = max(array_column($rezultatSubspecializari, 'max_onorariu'));
        $sumSubspecializari = array_sum(array_column($rezultatSubspecializari, 'sum_onorariu'));
        $avgSubspecializari = $totalSubspecializari > 0 ? $sumSubspecializari / $totalSubspecializari : 0;

        $tabelSubspecializari = '<div class="table-responsive mt-5">
        <h4 class="mb-3">Statistici onorarii pe subspecializări</h4>
        <table class="table table-striped table-bordered table-hover sortable-table" id="table-subspecializari">
            <thead class="table-success">
                <tr>
                    <th data-sort="string">Subspecializare <i class="fas fa-sort"></i></th>
                    <th data-sort="string">Specializare <i class="fas fa-sort"></i></th>
                    <th data-sort="int">Număr expertize <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Onorariu minim (RON) <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Onorariu maxim (RON) <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Onorariu mediu (RON) <i class="fas fa-sort"></i></th>
                    <th data-sort="float">Total onorarii (RON) <i class="fas fa-sort"></i></th>
                </tr>
                <tr class="table-info no-sort">
                    <th colspan="2">TOTAL</th>
                    <th>' . number_format($totalSubspecializari, 0, ',', '.') . '</th>
                    <th>' . number_format(round($minSubspecializari, 2), 2, ',', '.') . '</th>
                    <th>' . number_format(round($maxSubspecializari, 2), 0, ',', '.') . '</th>
                    <th>' . number_format(round($avgSubspecializari, 2), 0, ',', '.') . '</th>
                    <th>' . number_format(round($sumSubspecializari, 2), 0, ',', '.') . '</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($rezultatSubspecializari as $row) {
            $tabelSubspecializari .= '<tr>
            <td>' . htmlspecialchars($row['nume_subspecializare']) . '</td>
            <td>' . htmlspecialchars($row['nume_specializare']) . '</td>
            <td>' . (int)$row['total_expertize'] . '</td>
            <td>' . round((float)$row['min_onorariu'], 2) . '</td>
            <td>' . round((float)$row['max_onorariu'], 2) . '</td>
            <td>' . round((float)$row['avg_onorariu'], 2) . '</td>
            <td>' . round((float)$row['sum_onorariu'], 2) . '</td>
        </tr>';
        }

        $tabelSubspecializari .= '</tbody></table></div>';

        $sortingScript = '<script>
    $(document).ready(function() {
        $(".sortable-table thead tr:not(.no-sort) th").click(function() {
            const table = $(this).parents("table").eq(0);
            const rows = table.find("tbody tr").toArray();
            const header = $(this);
            const index = header.index();
            const sortType = header.data("sort");
            const direction = header.hasClass("asc") ? -1 : 1;

            table.find("th i").attr("class", "fas fa-sort");
            header.find("i").attr("class", direction === 1 ? "fas fa-sort-up" : "fas fa-sort-down");

            header.toggleClass("asc", direction === 1);
            header.toggleClass("desc", direction === -1);

            rows.sort(function(a, b) {
                const aValue = $(a).children("td").eq(index).text().trim();
                const bValue = $(b).children("td").eq(index).text().trim();

                if (sortType === "int") {
                    return direction * (parseInt(aValue.replace(/[^\d]/g, "")) - parseInt(bValue.replace(/[^\d]/g, "")));
                } else if (sortType === "float") {
                    return direction * (parseFloat(aValue.replace(/[^\d.]/g, "")) - parseFloat(bValue.replace(/[^\d.]/g, "")));
                } else {
                    return direction * aValue.localeCompare(bValue);
                }
            });

            $.each(rows, function(index, row) {
                table.children("tbody").append(row);
            });
        });

        $(".sortable-table thead tr:not(.no-sort) th").css("cursor", "pointer");
    });
    </script>';

        $specializari = [
            'type' => 'bar',
            'labels' => $labelsSpecializari,
            'datasets' => [
                [
                    'label' => 'Onorariu minim (RON)',
                    'data' => $valuesMinSpecializari,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.5)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Onorariu mediu (RON)',
                    'data' => $valuesAvgSpecializari,
                    'backgroundColor' => 'rgba(255, 206, 86, 0.5)',
                    'borderColor' => 'rgba(255, 206, 86, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Onorariu maxim (RON)',
                    'data' => $valuesMaxSpecializari,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 1
                ]
            ],
            'options' => [
                'responsive' => true,
                'scales' => [
                    'y' => [
                        'beginAtZero' => true
                    ]
                ],
                'plugins' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Top 10 specializări - Statistici onorarii (Total expertize: ' . $totalStats['total_expertize'] . ')'
                    ]
                ]
            ]
        ];
        $subspecializari_top10 = [
            'type' => 'bar',
            'labels' => $labelsSubspecializari,
            'datasets' => [
                [
                    'label' => 'Onorariu minim (RON)',
                    'data' => $valuesMinSubspecializari,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.5)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Onorariu mediu (RON)',
                    'data' => $valuesAvgSubspecializari,
                    'backgroundColor' => 'rgba(255, 206, 86, 0.5)',
                    'borderColor' => 'rgba(255, 206, 86, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Onorariu maxim (RON)',
                    'data' => $valuesMaxSubspecializari,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 1
                ]
            ],
            'options' => [
                'responsive' => true,
                'scales' => [
                    'y' => [
                        'beginAtZero' => true
                    ]
                ],
                'plugins' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Top 10 subspecializări - Statistici onorarii'
                    ]
                ]
            ]
        ];
        $response = [
//        'charts' => [
//            'specializari' => $specializari,
//            'subspecializari_top10' => $subspecializari_top10
//        ],
            'data' => $sortingScript . $tabelSpecializari . $tabelSubspecializari
        ];

        header('Content-Type: application/json; charset=UTF-8');
        $response['exportButton'] = $exportButton;
        echo json_encode($response);
        exit;
    } else {
        echo json_encode($response);
    }

    DatabasePool::releaseConnection($dbConnection);

    $selectToateLaolalta = "
-- Combined query to get both specializations and subspecializations with statistics
SELECT
    CASE
        WHEN ss.id_subspecializare IS NULL THEN 'specializare'
        ELSE 'subspecializare'
    END AS type,
    COALESCE(ss.id_subspecializare, sp.id_specializare) AS id_entry,
    COALESCE(ss.nume_subspecializare, sp.nume_specializare) AS nume_entry,
    COALESCE(sp.id_specializare, ss.id_specializare) AS id_specializare,
    COALESCE(sp.nume_specializare, ss.nume_specializare) AS nume_specializare,
    COUNT(DISTINCT e.id) AS total_expertize,
    MIN(e.onorariu) AS min_onorariu,
    MAX(e.onorariu) AS max_onorariu,
    AVG(e.onorariu) AS avg_onorariu,
    SUM(e.onorariu) AS sum_onorariu
FROM exp_jud.expertize e
LEFT JOIN exp_jud.specializarisubspecializari sp ON
    e.idSubspecializare REGEXP '^0(,0)*$' AND
    FIND_IN_SET(sp.id_specializare, e.idSpecializare) > 0
LEFT JOIN exp_jud.specializarisubspecializari ss ON
    NOT e.idSubspecializare REGEXP '^0(,0)*$' AND
    FIND_IN_SET(ss.id_subspecializare, e.idSubspecializare) > 0
WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0
	and e.id not in (8666, 12060)
GROUP BY type, id_entry, nume_entry, id_specializare, nume_specializare
ORDER BY avg_onorariu DESC, nume_specializare ASC, nume_entry ASC;";
}