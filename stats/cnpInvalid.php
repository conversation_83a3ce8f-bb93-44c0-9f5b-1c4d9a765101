<?php
// Only set JSON header if not exporting to Excel
if (!isset($_GET['export']) || $_GET['export'] !== 'excel') {
    header('Content-Type: application/json; charset=UTF-8');
}
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'excel_export.php';
global $pool_EXP_TEHNICI_OLD, $conn_EXP_TEHNICI_OLD;
$sql = "SELECT s.*, j.nume judet FROM Specialist s
    JOIN SituatieSpecialist sit on s.id_situatie=sit.id
    JOIN NJudet j ON s.id_judet = j.id
         where sit.activ = 1
         order by judet";
$stmt = sqlsrv_query($conn_EXP_TEHNICI_OLD, $sql);

if ($stmt === false) {
    die(print_r(sqlsrv_errors(), true));
}

$tr = null;
$i = 0;
while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    $cnp = $row['cnp'];
    $nume = $row['nume'];
    $prenume = $row['prenume'];
    $adresa = $row['adresa'];
    $judet = $row['judet'];
    $telefon = $row['telefon'];
    $telefon2 = $row['telefon2'];
    $id_localitate = $row['id_localitate'];
    $id_judet = $row['id_judet'];
    $id_situatie = $row['id_situatie'];
    $nr_dosar = $row['nr_dosar'];
    $nr_ordine = $row['nr_ordine'];
    $expert = $row['expert'];
    $email = $row['email'];

	if(cnpuriValideExceptii($cnp)){
		continue;
	}

    if ($cnp == null) {
        $i++;
        $tr .= "
        <tr>
            <td>$i</td>
            <td>$nume $prenume</td>
            <td>CNP $cnp</td>
            <td>$judet</td>
            <td>$adresa / $telefon / $telefon2</td>
            <td>$email</td>
        </tr>";
    } elseif (validCNP($cnp) == false && $cnp != null) {
        $i++;
        $tr .= "
        <tr>
            <td>$i</td>
            <td>$nume $prenume</td>
            <td>CNP $cnp</td>
            <td>$judet</td>
            <td>$adresa / $telefon / $telefon2</td>
            <td>$email</td>
        </tr>";
    }
}
sqlsrv_free_stmt($stmt);
$pool_EXP_TEHNICI_OLD->releaseConnection($conn_EXP_TEHNICI_OLD);

// Store data for Excel export
$expertData = [];
$i = 0;

// Reset the statement to get data again for Excel
$stmt = sqlsrv_query($conn_EXP_TEHNICI_OLD, $sql);
while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    $cnp = $row['cnp'];
    $nume = $row['nume'];
    $prenume = $row['prenume'];
    $adresa = $row['adresa'];
    $judet = $row['judet'];
    $telefon = $row['telefon'];
    $telefon2 = $row['telefon2'];
    $email = $row['email'];

    if (in_array($cnp, ['1790929221143', '1670129381297'])) { // valide
        continue;
    }

    if ($cnp == null || (validCNP($cnp) == false && $cnp != null)) {
        $i++;
        $expertData[] = [
            $i,
            $nume . ' ' . $prenume,
            $judet,
            $cnp,
            $adresa . ' / ' . $telefon . ' / ' . $telefon2,
            $email
        ];
    }
}
sqlsrv_free_stmt($stmt);

// Handle Excel export if requested
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    $sheets = [
        [
            'name' => 'CNP Invalid',
            'title' => 'Experți cu CNP invalid sau lipsă',
            'description' => 'Total experți: ' . count($expertData),
            'headers' => ['Nr.crt.', 'Nume Prenume', 'Județ', 'CNP', 'Adresa / Telefon / Telefon 2', 'E-mail'],
            'data' => $expertData
        ]
    ];

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'experti-cnp-invalid-' . date('Y-m-d') . '.xls');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add export button
    $exportButton = '<div class="text-right mb-3">
        <a href="stats/cnpInvalid.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';

    $data = "
    <div class='row'><div class='col-md-12 small' style='text-align: center;'>CNP-uri invalide in aplicatia ETJ nu DAETJ</div></div>
    <table class='table table-hover'>
        <thead>
        <tr>
            <th>Nr.crt.</th>
            <th>Nume Prenume</th>
            <th>Judet</th>
            <th>CNP</th>
            <th>Adresa / Telefon / Telefon 2</th>
            <th>E-mail</th>
        </tr>
        </thead>
        <tbody>$tr</tbody>
    </table>";
    echo json_encode(["exportButton" => $exportButton, "data" => $data]);
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}?>


