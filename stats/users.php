<?php
// Only set JSON header if not exporting to Excel
if (!isset($_GET['export']) || $_GET['export'] !== 'excel') {
    header('Content-Type: application/json; charset=UTF-8');
}
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db.php';
require_once 'excel_export.php';
$dbConnection = DatabasePool::getConnection();

$selectUtilizatoriPerInstanta = "
    SELECT
    i.den denumire_instanta,
    COUNT(u.id) as nr_utilizatori,
    SUM(CASE
        WHEN u.uidInactivare > 1 AND u.dataInactivare IS NOT NULL THEN 1
        ELSE 0
    END) as nr_utilizatori_inactivi,
    SUM(CASE
        WHEN u.uidInactivare = 0 AND u.dataInactivare IS NULL THEN 1
        ELSE 0
    END) as nr_utilizatori_activi
FROM exp_jud.utilizatori u
JOIN exp_jud.z_instante i ON u.idInstanta = i.id
GROUP BY i.id, i.den
ORDER BY i.den";
$rezultatUtilizatoriPerInstanta = $dbConnection->query($selectUtilizatoriPerInstanta)->fetchAll();

$labelsInstante = [];
$valuesInstante = [];
$nr_utilizatoriTotal = $nr_utilizatoriInactivi = 0;

foreach ($rezultatUtilizatoriPerInstanta as $row) {
    $labelsInstante[] = $row['denumire_instanta'];
    $valuesInstante[] = (int)$row['nr_utilizatori'];
    $nr_utilizatoriTotal += $row['nr_utilizatori'];
    $nr_utilizatoriInactivi += $row['nr_utilizatori_inactivi'];
}

$selectUtilizatoriPerRol = "
    SELECT
        f.tip denumire_functie,
        COUNT(u.id) as nr_utilizatori,
        SUM(CASE WHEN u.uidInactivare > 1 AND u.dataInactivare IS NOT NULL THEN 1 ELSE 0 END) as nr_utilizatori_inactivi,
        SUM(CASE WHEN u.uidInactivare = 0 AND u.dataInactivare IS NULL THEN 1 ELSE 0 END) as nr_utilizatori_activi
    FROM exp_jud.utilizatori u
    JOIN exp_jud.z_rol_utilizator f ON u.idRol = f.id
    GROUP BY f.id, f.tip
    ORDER BY f.tip";
$rezultatUtilizatoriPerRol = $dbConnection->query($selectUtilizatoriPerRol)->fetchAll();

$labelsRoluri = [];
$valuesRoluri = [];

$selectUserActiviInactiviPebazaDeInstanta = "
SELECT
    i.den AS instanta_nume,
    SUM(CASE WHEN u.dataInactivare IS NOT NULL THEN 1 ELSE 0 END) AS nr_inactivi,
    SUM(CASE WHEN u.dataInactivare IS NULL THEN 1 ELSE 0 END) AS nr_activi,
    COUNT(u.id) AS nr_total
FROM exp_jud.utilizatori u
JOIN exp_jud.z_instante i ON i.id = u.idInstanta
GROUP BY u.idInstanta, i.den
HAVING SUM(CASE WHEN u.dataInactivare IS NOT NULL THEN 1 ELSE 0 END) > 0
ORDER BY nr_inactivi DESC";

$rezultatUserActiviInactivi = $dbConnection->query($selectUserActiviInactiviPebazaDeInstanta)->fetchAll();

$labelsInstanteActiviInactivi = [];
$valuesActivi = [];
$valuesInactivi = [];
$totalUsers = 0;
$totalActivi = 0;
$totalInactivi = 0;

foreach ($rezultatUserActiviInactivi as $row) {
    $labelsInstanteActiviInactivi[] = $row['instanta_nume'];
    $valuesActivi[] = (int)$row['nr_activi'];
    $valuesInactivi[] = (int)$row['nr_inactivi'];
    $totalUsers += (int)$row['nr_total'];
    $totalActivi += (int)$row['nr_activi'];
    $totalInactivi += (int)$row['nr_inactivi'];
}


foreach ($rezultatUtilizatoriPerRol as $row) {
    $labelsRoluri[] = $row['denumire_functie'];
    $valuesRoluri[] = (int)$row['nr_utilizatori'];
}

// Release database connection
DatabasePool::releaseConnection($dbConnection);

// Handle Excel export if requested
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    // Prepare data for multiple sheets
    $sheets = [
        [
            'name' => 'Utilizatori per Instanta',
            'title' => 'Distribuția utilizatorilor pe instanțe',
            'description' => 'Total utilizatori: ' . $nr_utilizatoriTotal . ' | Total utilizatori inactivi: ' . $nr_utilizatoriInactivi,
            'headers' => ['Instanță', 'Total utilizatori', 'Utilizatori activi', 'Utilizatori inactivi'],
            'data' => array_map(function($index) use ($rezultatUtilizatoriPerInstanta) {
                $row = $rezultatUtilizatoriPerInstanta[$index];
                return [
                    $row['denumire_instanta'],
                    (int)$row['nr_utilizatori'],
                    (int)$row['nr_utilizatori_activi'],
                    (int)$row['nr_utilizatori_inactivi']
                ];
            }, array_keys($rezultatUtilizatoriPerInstanta))
        ],
        [
            'name' => 'Utilizatori per Rol',
            'title' => 'Distribuția utilizatorilor pe roluri',
            'description' => 'Total utilizatori: ' . $nr_utilizatoriTotal,
            'headers' => ['Rol', 'Total utilizatori', 'Utilizatori activi', 'Utilizatori inactivi'],
            'data' => array_map(function($index) use ($rezultatUtilizatoriPerRol) {
                $row = $rezultatUtilizatoriPerRol[$index];
                return [
                    $row['denumire_functie'],
                    (int)$row['nr_utilizatori'],
                    (int)$row['nr_utilizatori_activi'],
                    (int)$row['nr_utilizatori_inactivi']
                ];
            }, array_keys($rezultatUtilizatoriPerRol))
        ],
        [
            'name' => 'Utilizatori Activi-Inactivi',
            'title' => 'Distribuția utilizatorilor activi/inactivi pe instanțe',
            'description' => 'Total utilizatori: ' . $totalUsers . ' | Activi: ' . $totalActivi . ' | Inactivi: ' . $totalInactivi,
            'headers' => ['Instanță', 'Utilizatori activi', 'Utilizatori inactivi', 'Total utilizatori'],
            'data' => array_map(function($index) use ($rezultatUserActiviInactivi) {
                $row = $rezultatUserActiviInactivi[$index];
                return [
                    $row['instanta_nume'],
                    (int)$row['nr_activi'],
                    (int)$row['nr_inactivi'],
                    (int)$row['nr_total']
                ];
            }, array_keys($rezultatUserActiviInactivi))
        ]
    ];

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'statistici-utilizatori-' . date('Y-m-d') . '.xls');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add export button
    $exportButton = '<div class="text-right mb-3">
        <a href="stats/users.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';

    $response = [
        'exportButton' => $exportButton,
        'charts' => [
            'instante' => [
                'type' => 'bar',
                'labels' => $labelsInstante,
                'datasets' => [
                    [
                        'label' => 'Utilizatori Activi',
                        'data' => array_map(function ($row) {
                            return (int)$row['nr_utilizatori_activi'];
                        }, $rezultatUtilizatoriPerInstanta),
                        'backgroundColor' => 'rgba(75, 192, 75, 0.5)',
                        'borderColor' => 'rgba(75, 192, 75, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Utilizatori Inactivi',
                        'data' => array_map(function ($row) {
                            return (int)$row['nr_utilizatori_inactivi'];
                        }, $rezultatUtilizatoriPerInstanta),
                        'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'maintainAspectRatio' => false,
                    'scales' => [
                        'x' => [
                            'stacked' => true,
                        ],
                        'y' => [
                            'stacked' => true,
                            'beginAtZero' => true,
                            'ticks' => [
                                'stepSize' => 1
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => [
                                'Distribuția utilizatorilor pe instanțe',
                                'Total utilizatori: ' . $nr_utilizatoriTotal,
                                'Total utilizatori inactivi: ' . $nr_utilizatoriInactivi
                            ]
                        ]
                    ]
                ]
            ],
            'roluri' => [
                'type' => 'bar',
                'labels' => $labelsRoluri,
                'datasets' => [
                    [
                        'label' => 'Număr utilizatori',
                        'data' => $valuesRoluri,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.5)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'maintainAspectRatio' => false,
                    'scales' => [
                        'y' => [
                            'beginAtZero' => true,
                            'ticks' => [
                                'stepSize' => 1
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Distribuția utilizatorilor pe roluri'
                        ]
                    ]
                ]
            ],
            'activi_inactivi' => [
                'type' => 'bar',
                'labels' => $labelsInstanteActiviInactivi,
                'datasets' => [
                    [
                        'label' => 'Utilizatori Activi',
                        'data' => $valuesActivi,
                        'backgroundColor' => 'rgba(75, 192, 75, 0.5)',
                        'borderColor' => 'rgba(75, 192, 75, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Utilizatori Inactivi',
                        'data' => $valuesInactivi,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'maintainAspectRatio' => false,
                    'indexAxis' => 'y',
                    'scales' => [
                        'x' => [
                            'stacked' => true,
                            'beginAtZero' => true,
                            'ticks' => [
                                'stepSize' => 1
                            ]
                        ],
                        'y' => [
                            'stacked' => true
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Distribuția utilizatorilor activi/inactivi pe instanțe'
                        ]
                    ]
                ]
            ]
        ]
    ];

    echo json_encode($response);
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
?>