<?php
//Mai pune, te rog, un grafic/counter cu nr de experți desemnați (ori pe instanta -nr din circumscripție și nr din afara circumscripției - umpic mai complex, ori pe specializare)
//gândește-te și tu la o variantă sugestivă/cu informații
//eventual poți să pui un set nou de bare pe modulul expertize
//astfel, lângă nr de expertize (de ex., pe ore) sa vad și nr de experți desemnați
//la fel pe distribuția de 30 zile
//și cea lunară

$basePath = __DIR__ . '/..';

// Check for Excel export first to avoid header issues
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    require_once $basePath . '/inc/cfg_functions.php';
    require_once $basePath . '/inc/cfg_db.php';
    require_once 'excel_export.php';

    $dbConnection = DatabasePool::getConnection();

    // Get data for export
    $queryCircumscriptie = "
    SELECT 
        jd.numeJudet as judet_desemnare,
        COUNT(DISTINCT e.cnpExpert) as total_experti,
        COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
        COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) * 100.0 / 
               COUNT(DISTINCT e.cnpExpert)), 2) as procent_circumscriptie,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 / 
               COUNT(DISTINCT e.cnpExpert)), 2) as procent_alt_judet,
        COUNT(e.id) as total_expertize,
        COUNT(CASE WHEN et.id_judet = i.id_judet THEN e.id END) as expertize_din_circumscriptie,
        COUNT(CASE WHEN et.id_judet != i.id_judet THEN e.id END) as expertize_din_alt_judet,
        SUM(e.onorariu) as suma_totala_onorarii,
        SUM(CASE WHEN et.id_judet = i.id_judet THEN e.onorariu ELSE 0 END) as onorarii_din_circumscriptie,
        SUM(CASE WHEN et.id_judet != i.id_judet THEN e.onorariu ELSE 0 END) as onorarii_din_alt_judet
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    INNER JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    LEFT JOIN exp_jud.njudete jd ON i.id_judet = jd.idJudet
    WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0
    GROUP BY i.id_judet, jd.numeJudet
    ORDER BY total_experti DESC;";

    $querySpecializari = "
    SELECT 
        ss.nume_specializare,
        COUNT(DISTINCT e.cnpExpert) as total_experti,
        COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
        COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
        COUNT(e.id) as total_expertize,
        SUM(e.onorariu) as suma_totala_onorarii,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 / 
               COUNT(DISTINCT e.cnpExpert)), 2) as procent_mobilitate
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    INNER JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    INNER JOIN exp_jud.specializarisubspecializari ss ON 
        FIND_IN_SET(ss.id_specializare, e.idSpecializare) > 0
    WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0
        AND e.idSubspecializare REGEXP '^0(,0)*$'
    GROUP BY ss.id_specializare, ss.nume_specializare
    HAVING COUNT(DISTINCT e.cnpExpert) >= 5
    ORDER BY procent_mobilitate DESC, total_experti DESC";

    $rezultatCircumscriptie = $dbConnection->query($queryCircumscriptie)->fetchAll(PDO::FETCH_ASSOC);
    $rezultatSpecializari = $dbConnection->query($querySpecializari)->fetchAll(PDO::FETCH_ASSOC);

    // Prepare data for Excel export
    $circumscriptieData = [];
    foreach ($rezultatCircumscriptie as $row) {
        $circumscriptieData[] = [
            $row['judet_desemnare'],
            $row['total_experti'],
            $row['experti_din_circumscriptie'],
            $row['experti_din_alt_judet'],
            $row['procent_circumscriptie'] . '%',
            $row['procent_alt_judet'] . '%',
            $row['total_expertize'],
            number_format($row['suma_totala_onorarii'], 2, '.', ',')
        ];
    }

    $specializariData = [];
    foreach ($rezultatSpecializari as $row) {
        $specializariData[] = [
            $row['nume_specializare'],
            $row['total_experti'],
            $row['experti_din_circumscriptie'],
            $row['experti_din_alt_judet'],
            $row['procent_mobilitate'] . '%',
            $row['total_expertize'],
            number_format($row['suma_totala_onorarii'], 2, '.', ',')
        ];
    }

    $sheets = [
        [
            'name' => 'Experti pe judete',
            'title' => 'Statistici experți pe județe/circumscripții',
            'description' => 'Distribuția experților în funcție de județul de desemnare și circumscripția de proveniență',
            'headers' => ['Județ desemnare', 'Total experți', 'Experți din circumscripție', 'Experți din alt județ', '% Circumscripție', '% Alt județ', 'Total expertize', 'Suma totală onorarii'],
            'data' => $circumscriptieData
        ],
        [
            'name' => 'Experti pe specializari',
            'title' => 'Statistici experți pe specializări',
            'description' => 'Distribuția experților în funcție de specializare și mobilitatea geografică',
            'headers' => ['Specializare', 'Total experți', 'Experți din circumscripție', 'Experți din alt județ', '% Mobilitate', 'Total expertize', 'Suma totală onorarii'],
            'data' => $specializariData
        ]
    ];

    exportToExcelMultipleSheets($sheets, 'statistici-experti-desemnati-' . date('Y-m-d') . '.xls');
    exit;

    DatabasePool::releaseConnection($dbConnection);
}
 else {
    header('Content-Type: application/json; charset=UTF-8');

    require_once $basePath . '/inc/cfg_functions.php';
    require_once $basePath . '/inc/cfg_db.php';

    $dbConnection = DatabasePool::getConnection();

// Query principal pentru analiza experților pe circumscripții și județe
    $queryCircumscriptie = "
        SELECT 
            jd.numeJudet as judet_desemnare,
            COUNT(DISTINCT e.cnpExpert) as total_experti,
            COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
            COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
            ROUND((COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) * 100.0 /
            COUNT(DISTINCT e.cnpExpert)), 2) as procent_circumscriptie,
            ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 /
            COUNT(DISTINCT e.cnpExpert)), 2) as procent_alt_judet,
            COUNT(e.id) as total_expertize,
            COUNT(CASE WHEN et.id_judet = i.id_judet THEN e.id END) as expertize_din_circumscriptie,
            COUNT(CASE WHEN et.id_judet != i.id_judet THEN e.id END) as expertize_din_alt_judet,
            SUM(e.onorariu) as suma_totala_onorarii,
            SUM(CASE WHEN et.id_judet = i.id_judet THEN e.onorariu ELSE 0 END) as onorarii_din_circumscriptie,
            SUM(CASE WHEN et.id_judet != i.id_judet THEN e.onorariu ELSE 0 END) as onorarii_din_alt_judet
        FROM exp_jud.expertize e
        INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
        INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
        INNER JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
        LEFT JOIN exp_jud.njudete jd ON i.id_judet = jd.idJudet
        WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0 and e.id not in (8666, 12060)
        GROUP BY i.id_judet, jd.numeJudet
        ORDER BY total_experti DESC;";


// Query pentru statistici pe specializări cu informații geografice
    $querySpecializari = "
    SELECT 
        ss.nume_specializare, ss.id_specializare, ss.id_subspecializare,
        COUNT(DISTINCT e.cnpExpert) as total_experti,
        COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
        COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
        COUNT(e.id) as total_expertize,
        COUNT(CASE WHEN et.id_judet = i.id_judet THEN e.id END) as expertize_din_circumscriptie,
        COUNT(CASE WHEN et.id_judet != i.id_judet THEN e.id END) as expertize_din_alt_judet,
        SUM(e.onorariu) as suma_totala_onorarii,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 /
        COUNT(DISTINCT e.cnpExpert)), 2) as procent_mobilitate,
        ROUND(AVG(e.onorariu), 2) as avg_onorariu
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    LEFT JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    INNER JOIN exp_jud.specializarisubspecializari ss ON 
        FIND_IN_SET(ss.id_specializare, e.idSpecializare) > 0
    WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0 and e.id not in (8666, 12060)
    GROUP BY ss.id_specializare, ss.nume_specializare
    ORDER BY suma_totala_onorarii desc, procent_mobilitate DESC, total_experti DESC;";

    // Query pentru distribuția pe 30 de zile cu informații geografice
    $query30Zile = "
    SELECT 
        DATE(e.dataDesemnare) as data_desemnare,
        COUNT(DISTINCT e.cnpExpert) as nr_experti_desemnati,
        COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
        COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
        COUNT(e.id) as nr_expertize,
        SUM(e.onorariu) as total_onorarii,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 /
        NULLIF(COUNT(DISTINCT e.cnpExpert), 0)), 2) as procent_mobilitate
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    INNER JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    WHERE e.idStatusExpertiza = 1
        AND e.onorariu > 0 and e.id not in (8666, 12060)
        AND e.dataDesemnare >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY DATE(e.dataDesemnare)
    ORDER BY DATE(e.dataDesemnare) DESC;";

    // Query pentru distribuția lunară cu informații geografice
    $queryLunar = "
    SELECT 
        YEAR(e.dataDesemnare) as anul,
        MONTH(e.dataDesemnare) as luna,
        CONCAT(YEAR(e.dataDesemnare), '-', LPAD(MONTH(e.dataDesemnare), 2, '0')) as perioada,
        COUNT(DISTINCT e.cnpExpert) as nr_experti_desemnati,
        COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
        COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
        COUNT(e.id) as nr_expertize,
        SUM(e.onorariu) as total_onorarii,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 /
        NULLIF(COUNT(DISTINCT e.cnpExpert), 0)), 2) as procent_mobilitate
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    INNER JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    WHERE e.idStatusExpertiza = 1
        AND e.onorariu > 0 and e.id not in (8666, 12060)
        AND e.dataDesemnare >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY YEAR(e.dataDesemnare), MONTH(e.dataDesemnare)
    ORDER BY anul DESC, luna DESC;";

    // Query pentru top județe cu experți din afara circumscripției (detaliat)
    $queryTopJudete = "
    SELECT 
        jd.numeJudet as judet_desemnare,
        je.numeJudet as judet_expert,
        COUNT(DISTINCT e.cnpExpert) as nr_experti,
        COUNT(e.id) as nr_expertize,
        SUM(e.onorariu) as total_onorarii,
        GROUP_CONCAT(DISTINCT CONCAT(et.nume, ' ', et.prenume) ORDER BY et.nume SEPARATOR '; ') as lista_experti
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    INNER JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    LEFT JOIN exp_jud.njudete jd ON i.id_judet = jd.idJudet
    LEFT JOIN exp_jud.njudete je ON et.id_judet = je.idJudet
    WHERE e.idStatusExpertiza = 1 AND e.onorariu > 0 and e.id not in (8666, 12060) AND et.id_judet != i.id_judet
    GROUP BY i.id_judet, et.id_judet, jd.numeJudet, je.numeJudet
    ORDER BY jd.numeJudet, COUNT(DISTINCT e.cnpExpert) DESC;";

    // Query pentru statistici generale cu informații geografice
    $queryGenerale = "
    SELECT 
        COUNT(DISTINCT e.cnpExpert) as total_experti_unici_desemnati,
		(SELECT COUNT(DISTINCT cnp) FROM exp_jud.experti_tehnici WHERE id not in (8666, 12060)) as total_experti_unici,
        COUNT(DISTINCT CASE WHEN et.id_judet = i.id_judet THEN e.cnpExpert END) as experti_din_circumscriptie,
        COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) as experti_din_alt_judet,
        COUNT(DISTINCT i.id_judet) as nr_judete_cu_desemnari,
        COUNT(DISTINCT et.id_judet) as nr_judete_cu_experti,
        COUNT(e.id) as total_expertize,
        SUM(e.onorariu) as total_onorarii,
        ROUND(AVG(e.onorariu), 2) as avg_onorariu,
        ROUND((COUNT(DISTINCT CASE WHEN et.id_judet != i.id_judet THEN e.cnpExpert END) * 100.0 /
        COUNT(DISTINCT e.cnpExpert)), 2) as procent_mobilitate_generala
    FROM exp_jud.expertize e
    INNER JOIN exp_jud.utilizatori u ON e.idUtilizatorInsert = u.id
    INNER JOIN exp_jud.z_instante i ON u.idInstanta = i.id
    #INNER 
	left JOIN exp_jud.experti_tehnici et ON e.cnpExpert = et.cnp
    WHERE 
	#e.idStatusExpertiza = 1 AND 
	e.onorariu > 0 and e.id not in (8666, 12060);";


    $queryCircumscriptie = $dbConnection->query($queryCircumscriptie)->fetchAll(PDO::FETCH_ASSOC);
    $querySpecializari = $dbConnection->query($querySpecializari)->fetchAll(PDO::FETCH_ASSOC);
    $query30Zile = $dbConnection->query($query30Zile)->fetchAll(PDO::FETCH_ASSOC);
    $queryLunar = $dbConnection->query($queryLunar)->fetchAll(PDO::FETCH_ASSOC);
    $queryTopJudete = $dbConnection->query($queryTopJudete)->fetchAll(PDO::FETCH_ASSOC);
    $queryGenerale = $dbConnection->query($queryGenerale)->fetchAll(PDO::FETCH_ASSOC);

    $response = [
        'queryCircumscriptie' => $queryCircumscriptie,
        'querySpecializari' => $querySpecializari,
        'query30Zile' => $query30Zile,
        'queryLunar' => $queryLunar,
        'queryTopJudete' => $queryTopJudete,
        'queryGenerale' => $queryGenerale,
    ];

    // Format data for charts if POST request
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Export button
        $exportButton = '<div class="text-right mb-3">
            <a href="stats/expertiNr.php?export=excel" class="btn btn-sm btn-outline-success">
                <i class="fas fa-file-excel mr-1"></i> Export Excel
            </a>
        </div>';

        // Prepare data for charts

        // 1. Chart for counties (circumscriptii)
        $labelsJudete = [];
        $valuesJudete = [];
        $valuesCircumscriptie = [];
        $valuesAltJudet = [];
        $valuesOnorariiCircumscriptie = [];
        $valuesOnorariiAltJudet = [];
        $valuesExpertizeCircumscriptie = [];
        $valuesExpertizeAltJudet = [];

        foreach (array_slice($queryCircumscriptie, 0, 15) as $row) {
            $labelsJudete[] = $row['judet_desemnare'];
            $valuesJudete[] = (int)$row['total_experti'];
            $valuesCircumscriptie[] = (int)$row['experti_din_circumscriptie'];
            $valuesAltJudet[] = (int)$row['experti_din_alt_judet'];
            $valuesOnorariiCircumscriptie[] = (float)$row['onorarii_din_circumscriptie'];
            $valuesOnorariiAltJudet[] = (float)$row['onorarii_din_alt_judet'];
            $valuesExpertizeCircumscriptie[] = (int)$row['expertize_din_circumscriptie'];
            $valuesExpertizeAltJudet[] = (int)$row['expertize_din_alt_judet'];
        }

        // 2. Chart for specializations
        $labelsSpecializari = [];
        $valuesSpecializari = [];
        $valuesMobilitate = [];
        $valuesOnorariiSpecializari = [];
        $valuesAvgOnorariiSpecializari = [];
        $valuesExpertizeSpecializari = [];

        foreach (array_slice($querySpecializari, 0, 10) as $row) {
            $labelsSpecializari[] = $row['nume_specializare'];
            $valuesSpecializari[] = (int)$row['total_experti'];
            $valuesMobilitate[] = (float)$row['procent_mobilitate'];
            $valuesOnorariiSpecializari[] = (float)$row['suma_totala_onorarii'];
            $valuesAvgOnorariiSpecializari[] = (float)$row['avg_onorariu'];
            $valuesExpertizeSpecializari[] = (int)$row['total_expertize'];
        }

        // 3. Chart for 30 days
        $labels30Zile = [];
        $values30ZileExperti = [];
        $values30ZileCircumscriptie = [];
        $values30ZileAltJudet = [];
        $values30ZileOnorarii = [];
        $values30ZileExpertize = [];

        foreach (array_reverse($query30Zile) as $row) {
            $labels30Zile[] = date('d.m', strtotime($row['data_desemnare']));
            $values30ZileExperti[] = (int)$row['nr_experti_desemnati'];
            $values30ZileCircumscriptie[] = (int)$row['experti_din_circumscriptie'];
            $values30ZileAltJudet[] = (int)$row['experti_din_alt_judet'];
            $values30ZileOnorarii[] = (float)$row['total_onorarii'];
            $values30ZileExpertize[] = (int)$row['nr_expertize'];
        }

        // 4. Chart for monthly data
        $labelsLunar = [];
        $valuesLunarExperti = [];
        $valuesLunarCircumscriptie = [];
        $valuesLunarAltJudet = [];
        $valuesLunarOnorarii = [];
        $valuesLunarExpertize = [];

        foreach (array_reverse($queryLunar) as $row) {
            $labelsLunar[] = $row['perioada'];
            $valuesLunarExperti[] = (int)$row['nr_experti_desemnati'];
            $valuesLunarCircumscriptie[] = (int)$row['experti_din_circumscriptie'];
            $valuesLunarAltJudet[] = (int)$row['experti_din_alt_judet'];
            $valuesLunarOnorarii[] = (float)$row['total_onorarii'];
            $valuesLunarExpertize[] = (int)$row['nr_expertize'];
        }

        // 6. Chart for top counties with external experts
        $labelsTopJudete = [];
        $valuesTopJudeteExperti = [];
        $valuesTopJudeteExpertize = [];
        $valuesTopJudeteOnorarii = [];

        // Group by destination county and sum up the data
        $groupedTopJudete = [];
        foreach ($queryTopJudete as $row) {
            $judet = $row['judet_desemnare'];
            if (!isset($groupedTopJudete[$judet])) {
                $groupedTopJudete[$judet] = [
                    'nr_experti' => 0,
                    'nr_expertize' => 0,
                    'total_onorarii' => 0
                ];
            }
            $groupedTopJudete[$judet]['nr_experti'] += (int)$row['nr_experti'];
            $groupedTopJudete[$judet]['nr_expertize'] += (int)$row['nr_expertize'];
            $groupedTopJudete[$judet]['total_onorarii'] += (float)$row['total_onorarii'];
        }

        // Sort by number of experts and take top 10
        arsort($groupedTopJudete);
        $topJudeteSliced = array_slice($groupedTopJudete, 0, 10, true);

        foreach ($topJudeteSliced as $judet => $data) {
            $labelsTopJudete[] = $judet;
            $valuesTopJudeteExperti[] = $data['nr_experti'];
            $valuesTopJudeteExpertize[] = $data['nr_expertize'];
            $valuesTopJudeteOnorarii[] = $data['total_onorarii'];
        }

      

        $response['exportButton'] = $exportButton;
        $response['charts'] = [
            'judete' => [
                'type' => 'bar',
                'labels' => $labelsJudete,
                'datasets' => [
                    [
                        'label' => 'Experți din circumscripție',
                        'data' => $valuesCircumscriptie,
                        'backgroundColor' => 'rgba(54, 162, 235, 0.7)',
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ],
                    [
                        'label' => 'Experți din alt județ',
                        'data' => $valuesAltJudet,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.7)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'scales' => [
                        'x' => [
                            'stacked' => true
                        ],
                        'y' => [
                            'beginAtZero' => true,
                            'stacked' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Număr experți'
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Top 15 județe - Distribuția experților (din circumscripție vs. din alt județ)'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ]
                    ]
                ]
            ],
            'judeteOnorarii' => [
                'type' => 'bar',
                'labels' => $labelsJudete,
                'datasets' => [
                    [
                        'label' => 'Onorarii din circumscripție (RON)',
                        'data' => $valuesOnorariiCircumscriptie,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.7)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Onorarii din alt județ (RON)',
                        'data' => $valuesOnorariiAltJudet,
                        'backgroundColor' => 'rgba(255, 159, 64, 0.7)',
                        'borderColor' => 'rgba(255, 159, 64, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'scales' => [
                        'x' => [
                            'stacked' => true
                        ],
                        'y' => [
                            'beginAtZero' => true,
                            'stacked' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Onorarii (RON)'
                            ],
                            'ticks' => [
                                'callback' => null
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Top 15 județe - Distribuția onorariilor (din circumscripție vs. din alt județ)'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ],
                        'tooltip' => [
                            'enabled' => true
                        ]
                    ]
                ]
            ],
            'specializariOnorarii' => [
                'type' => 'bar',
                'labels' => $labelsSpecializari,
                'datasets' => [
                    [
                        'type' => 'bar',
                        'label' => 'Total onorarii (RON)',
                        'data' => $valuesOnorariiSpecializari,
                        'backgroundColor' => 'rgba(153, 102, 255, 0.7)',
                        'borderColor' => 'rgba(153, 102, 255, 1)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ],
                    [
                        'type' => 'line',
                        'label' => 'Onorariu mediu (RON)',
                        'data' => $valuesAvgOnorariiSpecializari,
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                        'borderWidth' => 2,
                        'pointRadius' => 4,
                        'pointBackgroundColor' => 'rgba(255, 99, 132, 1)',
                        'fill' => false,
                        'yAxisID' => 'y1'
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'interaction' => [
                        'intersect' => false,
                        'mode' => 'index'
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Onorarii pe specializări - Total și medie'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ],
                        'tooltip' => [
                            'enabled' => true
                        ]
                    ],
                    'scales' => [
                        'y' => [
                            'type' => 'linear',
                            'display' => true,
                            'position' => 'left',
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Total onorarii (RON)'
                            ],
                            'ticks' => [
                                'stepSize' => 1000
                            ]
                        ],
                        'y1' => [
                            'type' => 'linear',
                            'display' => true,
                            'position' => 'right',
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Onorariu mediu (RON)'
                            ],
                            'grid' => [
                                'drawOnChartArea' => false
                            ],
                            'ticks' => [
                                'stepSize' => 100
                            ]
                        ]
                    ]
                ]
            ],
            'zile30' => [
                'type' => 'line',
                'labels' => $labels30Zile,
                'datasets' => [
                    [
                        'label' => 'Experți din circumscripție',
                        'data' => $values30ZileCircumscriptie,
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                        'borderWidth' => 2,
                        'pointRadius' => 3,
                        'pointBackgroundColor' => 'rgba(54, 162, 235, 1)',
                        'fill' => true
                    ],
                    [
                        'label' => 'Experți din alt județ',
                        'data' => $values30ZileAltJudet,
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                        'borderWidth' => 2,
                        'pointRadius' => 3,
                        'pointBackgroundColor' => 'rgba(255, 99, 132, 1)',
                        'fill' => true
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'interaction' => [
                        'intersect' => false,
                        'mode' => 'index'
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Experți desemnați pe ultimele 30 de zile'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ]
                    ],
                    'scales' => [
                        'y' => [
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Număr experți'
                            ]
                        ],
                        'x' => [
                            'title' => [
                                'display' => true,
                                'text' => 'Data'
                            ]
                        ]
                    ]
                ]
            ],
            'zile30Onorarii' => [
                'type' => 'bar',
                'labels' => $labels30Zile,
                'datasets' => [
                    [
                        'type' => 'bar',
                        'label' => 'Total onorarii (RON)',
                        'data' => $values30ZileOnorarii,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.7)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ],
                    [
                        'type' => 'line',
                        'label' => 'Număr expertize',
                        'data' => $values30ZileExpertize,
                        'borderColor' => 'rgba(255, 159, 64, 1)',
                        'backgroundColor' => 'rgba(255, 159, 64, 0.2)',
                        'borderWidth' => 2,
                        'pointRadius' => 4,
                        'pointBackgroundColor' => 'rgba(255, 159, 64, 1)',
                        'fill' => false,
                        'yAxisID' => 'y1'
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'interaction' => [
                        'intersect' => false,
                        'mode' => 'index'
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Onorarii și expertize pe ultimele 30 de zile'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ],
                        'tooltip' => [
                            'enabled' => true
                        ]
                    ],
                    'scales' => [
                        'y' => [
                            'type' => 'linear',
                            'display' => true,
                            'position' => 'left',
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Onorarii (RON)'
                            ],
                            'ticks' => [
                                'stepSize' => 1000
                            ]
                        ],
                        'y1' => [
                            'type' => 'linear',
                            'display' => true,
                            'position' => 'right',
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Număr expertize'
                            ],
                            'grid' => [
                                'drawOnChartArea' => false
                            ]
                        ],
                        'x' => [
                            'title' => [
                                'display' => true,
                                'text' => 'Data'
                            ]
                        ]
                    ]
                ]
            ],
            'lunar' => [
                'type' => 'bar',
                'labels' => $labelsLunar,
                'datasets' => [
                    [
                        'label' => 'Experți din circumscripție',
                        'data' => $valuesLunarCircumscriptie,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.7)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Experți din alt județ',
                        'data' => $valuesLunarAltJudet,
                        'backgroundColor' => 'rgba(255, 159, 64, 0.7)',
                        'borderColor' => 'rgba(255, 159, 64, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'scales' => [
                        'x' => [
                            'stacked' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Perioada'
                            ]
                        ],
                        'y' => [
                            'beginAtZero' => true,
                            'stacked' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Număr experți'
                            ]
                        ]
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Experți desemnați pe luni (ultimele 12 luni)'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ]
                    ]
                ]
            ],
            'lunarOnorarii' => [
                'type' => 'bar',
                'labels' => $labelsLunar,
                'datasets' => [
                    [
                        'type' => 'bar',
                        'label' => 'Total onorarii (RON)',
                        'data' => $valuesLunarOnorarii,
                        'backgroundColor' => 'rgba(153, 102, 255, 0.7)',
                        'borderColor' => 'rgba(153, 102, 255, 1)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ],
                    [
                        'type' => 'line',
                        'label' => 'Număr expertize',
                        'data' => $valuesLunarExpertize,
                        'borderColor' => 'rgba(255, 206, 86, 1)',
                        'backgroundColor' => 'rgba(255, 206, 86, 0.2)',
                        'borderWidth' => 2,
                        'pointRadius' => 4,
                        'pointBackgroundColor' => 'rgba(255, 206, 86, 1)',
                        'fill' => false,
                        'yAxisID' => 'y1'
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'interaction' => [
                        'intersect' => false,
                        'mode' => 'index'
                    ],
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Onorarii și expertize pe luni (ultimele 12 luni)'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ],
                        'tooltip' => [
                            'enabled' => true
                        ]
                    ],
                    'scales' => [
                        'y' => [
                            'type' => 'linear',
                            'display' => true,
                            'position' => 'left',
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Onorarii (RON)'
                            ],
                            'ticks' => [
                                'stepSize' => 1000
                            ]
                        ],
                        'y1' => [
                            'type' => 'linear',
                            'display' => true,
                            'position' => 'right',
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Număr expertize'
                            ],
                            'grid' => [
                                'drawOnChartArea' => false
                            ]
                        ],
                        'x' => [
                            'title' => [
                                'display' => true,
                                'text' => 'Perioada'
                            ]
                        ]
                    ]
                ]
            ],
            'topJudete' => [
                'type' => 'bar',
                'labels' => $labelsTopJudete,
                'datasets' => [
                    [
                        'label' => 'Experți din alt județ',
                        'data' => $valuesTopJudeteExperti,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.7)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 1
                    ],
                    [
                        'label' => 'Expertize realizate',
                        'data' => $valuesTopJudeteExpertize,
                        'backgroundColor' => 'rgba(54, 162, 235, 0.7)',
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'borderWidth' => 1
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Top 10 județe cu experți din afara circumscripției'
                        ],
                        'legend' => [
                            'position' => 'top'
                        ]
                    ],
                    'scales' => [
                        'y' => [
                            'beginAtZero' => true,
                            'title' => [
                                'display' => true,
                                'text' => 'Număr'
                            ]
                        ]
                    ]
                ]
            ],
            'generale' => [
                'type' => 'doughnut',
                'labels' => ['Experți din circumscripție', 'Experți din alt județ'],
                'datasets' => [
                    [
                        'label' => 'Distribuția experților',
                        'data' => [
                            (int)$queryGenerale[0]['experti_din_circumscriptie'],
                            (int)$queryGenerale[0]['experti_din_alt_judet']
                        ],
                        'backgroundColor' => [
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 99, 132, 0.8)'
                        ],
                        'borderColor' => [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        'borderWidth' => 2
                    ]
                ],
                'options' => [
                    'responsive' => true,
                    'plugins' => [
                        'title' => [
                            'display' => true,
                            'text' => 'Distribuția generală a experților - Total: ' . number_format($queryGenerale[0]['total_experti_unici_desemnati']) . ' experți unici desemnați în dosare (din ' . number_format($queryGenerale[0]['total_experti_unici']) . ' experți activi)'
                        ],
                        'legend' => [
                            'position' => 'bottom'
                        ],
                        'tooltip' => [
                            'enabled' => true
                        ]
                    ]
                ]
            ]
        ];

        DatabasePool::releaseConnection($dbConnection);

        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    } else {
        DatabasePool::releaseConnection($dbConnection);
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
}
?>