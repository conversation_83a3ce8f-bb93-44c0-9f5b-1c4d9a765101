<?php
// Only set JSON header if not exporting to Excel
if (!isset($_GET['export']) || $_GET['export'] !== 'excel') {
    header('Content-Type: application/json; charset=UTF-8');
}
require_once '../inc/cfg_functions.php';
require_once '../inc/cfg_db.php';
require_once 'excel_export.php';
$dbConnection = DatabasePool::getConnection();

$selectInstanteCuDesemnariSubNr = "SELECT i.id, i.den, COUNT(e.id) AS nr_expertize
FROM exp_jud.z_instante i
LEFT JOIN exp_jud.utilizatori u ON i.id = u.idInstanta
LEFT JOIN exp_jud.expertize e ON u.id = e.idUtilizatorInsert
GROUP BY i.id, i.den
HAVING COUNT(e.id) < 10
order by nr_expertize asc, den";
$rezultatInstanteCuDesemnariSub = $dbConnection->query($selectInstanteCuDesemnariSubNr)->fetchAll();

$labelsInstanteSub = [];
$valuesInstanteSub = [];
foreach ($rezultatInstanteCuDesemnariSub as $row) {
    $labelsInstanteSub[] = $row['den'];
    $valuesInstanteSub[] = (int)$row['nr_expertize'];
}

$tabelInstanteSub = '<div class="table-responsive mb-4">';
$tabelInstanteSub .= '<h4 class="mb-3">Instanțe cu mai puțin de 10 expertize</h4>';
$tabelInstanteSub .= '<table class="table table-striped table-hover sortable-table" id="table-instante-sub">';
$tabelInstanteSub .= '<thead><tr><th data-sort="int">Nr. <i class="fas fa-sort"></i></th><th data-sort="string">Instanță <i class="fas fa-sort"></i></th><th data-sort="int">Număr expertize <i class="fas fa-sort"></i></th></tr></thead>';
$tabelInstanteSub .= '<tbody>';

$nr = 1;
foreach ($rezultatInstanteCuDesemnariSub as $row) {
    $tabelInstanteSub .= '<tr>';
    $tabelInstanteSub .= '<td>' . $nr . '</td>';
    $tabelInstanteSub .= '<td>' . htmlspecialchars($row['den']) . '</td>';
    $tabelInstanteSub .= '<td>' . (int)$row['nr_expertize'] . '</td>';
    $tabelInstanteSub .= '</tr>';
    $nr++;
}

$tabelInstanteSub .= '</tbody></table></div>';

$sortingScript = '<script>
    $(document).ready(function() {
        $(".sortable-table thead th").click(function() {
            const table = $(this).parents("table").eq(0);
            const rows = table.find("tbody tr").toArray();
            const header = $(this);
            const index = header.index();
            const sortType = header.data("sort");
            const direction = header.hasClass("asc") ? -1 : 1;

            table.find("th i").attr("class", "fas fa-sort");
            header.find("i").attr("class", direction === 1 ? "fas fa-sort-up" : "fas fa-sort-down");

            header.toggleClass("asc", direction === 1);
            header.toggleClass("desc", direction === -1);

            rows.sort(function(a, b) {
                const aValue = $(a).children("td").eq(index).text().trim();
                const bValue = $(b).children("td").eq(index).text().trim();

                if (sortType === "int") {
                    return direction * (parseInt(aValue.replace(/[^\d]/g, "")) - parseInt(bValue.replace(/[^\d]/g, "")));
                } else if (sortType === "float") {
                    return direction * (parseFloat(aValue.replace(/[^\d.]/g, "")) - parseFloat(bValue.replace(/[^\d.]/g, "")));
                } else {
                    return direction * aValue.localeCompare(bValue);
                }
            });

            $.each(rows, function(index, row) {
                table.children("tbody").append(row);
            });
        });

        $(".sortable-table thead th").css("cursor", "pointer");
    });
</script>';

$tabelInstanteSub .= $sortingScript;

// Release database connection
DatabasePool::releaseConnection($dbConnection);

// Handle Excel export if requested
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    // Prepare data for Excel export
    $excelData = [];
    $nr = 1;
    foreach ($rezultatInstanteCuDesemnariSub as $row) {
        $excelData[] = [
            $nr,
            $row['den'],
            (int)$row['nr_expertize']
        ];
        $nr++;
    }

    $sheets = [
        [
            'name' => 'Instante cu putine expertize',
            'title' => 'Instanțe cu mai puțin de 10 expertize',
            'description' => 'Total instanțe: ' . count($rezultatInstanteCuDesemnariSub),
            'headers' => ['Nr.', 'Instanță', 'Număr expertize'],
            'data' => $excelData
        ]
    ];

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'instante-cu-putine-expertize-' . date('Y-m-d') . '.xls');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add export button
    $exportButton = '<div class="text-right mb-3">
        <a href="stats/expertizeInstanteDesemnariSubNr.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';

    $response = [
        'exportButton' => $exportButton,
        'data' => $tabelInstanteSub
    ];

    echo json_encode($response);
    exit;
} else {
    http_response_code(400);
    echo json_encode(["error" => "Cerere invalidă"]);
    exit;
}
?>
