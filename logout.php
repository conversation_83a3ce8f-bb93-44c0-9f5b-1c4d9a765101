<?php
require_once 'vendor/autoload.php';
require_once 'inc/cfg_db.php';
require_once 'inc/cfg_functions.php';

$session_factory = new \Aura\Session\SessionFactory;
$session = $session_factory->newInstance($_COOKIE);
$sesiune = $session->getSegment('Vendor\Package\ClassName');

// Get user information for audit log before destroying the session
$username = $sesiune->get('username');
$user_id = $sesiune->get('id_utilizator');

// Log the logout event if user was logged in
if ($username && $user_id) {
    $dbConnection = DatabasePool::getConnection();
    log_auth_event($dbConnection, 'logout', [
        'user_id' => $user_id,
        'username' => $username
    ]);
}

$session->destroy();
header("Location: login.php");
exit();
?>