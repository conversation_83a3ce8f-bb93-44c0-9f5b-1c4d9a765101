<?php

require_once 'inc/cfg_functions.php';
require_once 'inc/cfg_db.php';
require_once 'inc/cfg_db_app_experti_old.php';
$judete = [1, 2, 3, 4, 5, 6];
$specializari = [[204,68]];
$vectorJudete = range(1, 42);



//getExp($vectorJudete, $specializari);
$result = lottery(240, 23, [[187, 0]], 1);

$vect = $result['experti'];
$output = $result['output'];
print_r($vect);

//print_r(lottery(205,3,[[190,0]],3));
//print_r(count(checkExpertAlocat('99/100/2025', [[184,0]])));
?>