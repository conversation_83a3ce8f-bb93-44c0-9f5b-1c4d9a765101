<?php
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_db.php';
require_once 'inc/cfg_session.php';
require_once 'inc/cfg_functions.php';
?>
<link href="vendor/select2/select2/dist/css/select2.min.css" rel="stylesheet"/>
<link href="assets/css/audit-logs-fix.css" rel="stylesheet"/>
<?php
$dbConnection = DatabasePool::getConnection();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
$offset = ($page - 1) * $limit;

// Filters
$filters = [];
$params = [];
$whereClause = "1=1";

if (isset($_GET['action_type']) && !empty($_GET['action_type'])) {
    $filters[] = "action_type = :action_type";
    $params[':action_type'] = $_GET['action_type'];
}

if (isset($_GET['action_category']) && !empty($_GET['action_category'])) {
    $filters[] = "action_category = :action_category";
    $params[':action_category'] = $_GET['action_category'];
}

if (isset($_GET['action_level']) && !empty($_GET['action_level'])) {
    $filters[] = "action_level = :action_level";
    $params[':action_level'] = $_GET['action_level'];
}

if (isset($_GET['user_id']) && !empty($_GET['user_id'])) {
    $filters[] = "user_id = :user_id";
    $params[':user_id'] = $_GET['user_id'];
}

if (isset($_GET['ip_address']) && !empty($_GET['ip_address'])) {
    $filters[] = "ip_address LIKE :ip_address";
    $params[':ip_address'] = '%' . $_GET['ip_address'] . '%';
}

if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    $filters[] = "created_at >= :date_from";
    $date_from = DateTime::createFromFormat('d.m.Y', $_GET['date_from']);
    if ($date_from) {
        $params[':date_from'] = $date_from->format('Y-m-d') . ' 00:00:00';
    } else {
        $params[':date_from'] = $_GET['date_from'] . ' 00:00:00';
    }
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    $filters[] = "created_at <= :date_to";
    $date_to = DateTime::createFromFormat('d.m.Y', $_GET['date_to']);
    if ($date_to) {
        $params[':date_to'] = $date_to->format('Y-m-d') . ' 23:59:59';
    } else {
        $params[':date_to'] = $_GET['date_to'] . ' 23:59:59';
    }
}

if (!empty($filters)) {
    $whereClause = implode(' AND ', $filters);
}

// Get total count
$countQuery = "SELECT COUNT(*) FROM audit_log WHERE $whereClause";
$countStmt = $dbConnection->prepare($countQuery);
$countStmt->execute($params);
$totalRecords = $countStmt->fetchColumn();

// Get audit logs
$query = "SELECT a.*, u.utilizator as username
          FROM audit_log a
          LEFT JOIN exp_jud.utilizatori u ON a.user_id = u.id
          WHERE $whereClause
          ORDER BY a.created_at DESC
          LIMIT :offset, :limit";

$stmt = $dbConnection->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->execute();
$auditLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get distinct action types and categories for filters
$actionTypesQuery = "SELECT DISTINCT action_type FROM audit_log ORDER BY action_type";
$actionTypesStmt = $dbConnection->query($actionTypesQuery);
$actionTypes = $actionTypesStmt->fetchAll(PDO::FETCH_COLUMN);

$actionCategoriesQuery = "SELECT DISTINCT action_category FROM audit_log WHERE action_category IS NOT NULL ORDER BY action_category";
$actionCategoriesStmt = $dbConnection->query($actionCategoriesQuery);
$actionCategories = $actionCategoriesStmt->fetchAll(PDO::FETCH_COLUMN);

$actionLevelsQuery = "SELECT DISTINCT action_level FROM audit_log ORDER BY action_level";
$actionLevelsStmt = $dbConnection->query($actionLevelsQuery);
$actionLevels = $actionLevelsStmt->fetchAll(PDO::FETCH_COLUMN);

// Get distinct IP addresses for filter
$ipAddressesQuery = "SELECT DISTINCT ip_address FROM audit_log where ip_address is not null ORDER BY ip_address";
$ipAddressesStmt = $dbConnection->query($ipAddressesQuery);
$ipAddresses = $ipAddressesStmt->fetchAll(PDO::FETCH_COLUMN);

// Get users for filter
$usersQuery = "SELECT id, utilizator FROM exp_jud.utilizatori ORDER BY utilizator";
$usersStmt = $dbConnection->query($usersQuery);
$users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total pages
$totalPages = ceil($totalRecords / $limit);
?>
<div class="container-fluid mb-3 mt-4 pb-3">
    <h2>Jurnal de Audit</h2>

    <div class="filter-section">
        <form method="GET">
            <div class="row g-3">
                <div class="col-md-2">
                    <label for="action_type" class="form-label">Tip Acțiune</label>
                    <select name="action_type" id="action_type" class="form-select">
                        <option value="">Toate</option>
                        <?php foreach ($actionTypes as $type): ?>
                            <option value="<?= htmlspecialchars($type) ?>" <?= isset($_GET['action_type']) && $_GET['action_type'] === $type ? 'selected' : '' ?>>
                                <?= htmlspecialchars($type) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="action_category" class="form-label">Categorie</label>
                    <select name="action_category" id="action_category" class="form-select">
                        <option value="">Toate</option>
                        <?php foreach ($actionCategories as $category): ?>
                            <option value="<?= htmlspecialchars($category) ?>" <?= isset($_GET['action_category']) && $_GET['action_category'] === $category ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="action_level" class="form-label">Nivel</label>
                    <select name="action_level" id="action_level" class="form-select">
                        <option value="">Toate</option>
                        <?php foreach ($actionLevels as $level): ?>
                            <option value="<?= htmlspecialchars($level) ?>" <?= isset($_GET['action_level']) && $_GET['action_level'] === $level ? 'selected' : '' ?>>
                                <?= htmlspecialchars($level) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="user_id" class="form-label">Utilizator</label>
                    <select name="user_id" id="user_id" class="form-select select2-users">
                        <option value="">Toți</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?= $user['id'] ?>" <?= isset($_GET['user_id']) && $_GET['user_id'] == $user['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($user['utilizator']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="ip_address" class="form-label">Adresă IP</label>
                    <select name="ip_address" id="ip_address" class="form-select select2-ip">
                        <option value="">Toate</option>
                        <?php foreach ($ipAddresses as $ip): ?>
                            <option value="<?= htmlspecialchars($ip) ?>" <?= isset($_GET['ip_address']) && $_GET['ip_address'] === $ip ? 'selected' : '' ?>>
                                <?= htmlspecialchars($ip) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="row g-3">
                <div class="col-md-2">
                    <label for="date_from" class="form-label">De la data</label>
                    <input type="text" name="date_from" id="date_from" class="form-control" style="background-color: white;" placeholder="zz.ll.aaaa"
                           value="<?= isset($_GET['date_from']) ? htmlspecialchars($_GET['date_from']) : '' ?>" readonly>
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">Până la data</label>
                    <input type="text" name="date_to" id="date_to" class="form-control" style="background-color: white;" placeholder="zz.ll.aaaa"
                           value="<?= isset($_GET['date_to']) ? htmlspecialchars($_GET['date_to']) : '' ?>" readonly>
                </div>
            </div>
            <div class="row">
                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">Filtrează</button>
                    <a href="audit_logs.php" class="btn btn-secondary">Resetează</a>
                </div>
            </div>
        </form>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered audit-table">
            <thead class="table-dark">
            <tr>
                <th>ID</th>
                <th>Data și Ora</th>
                <th>Utilizator</th>
                <th>Tip Acțiune</th>
                <th>Categorie</th>
                <th>Nivel</th>
                <th>Descriere</th>
                <th>Tabelă</th>
                <th>ID Înregistrare</th>
                <th style="width:300px; max-width:300px;">Date</th>
                <th>IP</th>
            </tr>
            </thead>
            <tbody>
            <?php if (empty($auditLogs)): ?>
                <tr>
                    <td colspan="11" class="text-center">Nu s-au găsit înregistrări</td>
                </tr>
            <?php else: ?>
                <?php foreach ($auditLogs as $log): ?>
                    <tr>
                        <td><?= $log['id'] ?></td>
                        <td><?= date('d.m.Y H:i:s', strtotime($log['created_at'])) ?></td>
                        <td><?= htmlspecialchars($log['username'] ?? 'N/A') ?></td>
                        <td><?= htmlspecialchars($log['action_type']) ?></td>
                        <td><?= htmlspecialchars($log['action_category'] ?? 'N/A') ?></td>
                        <td>
                            <?php
                            $badgeClass = 'badge-info';
                            if ($log['action_level'] === 'warning') {
                                $badgeClass = 'badge-warning';
                            } elseif ($log['action_level'] === 'critical') {
                                $badgeClass = 'badge-danger';
                            }
                            ?>
                            <span class="badge <?= $badgeClass ?>"><?= htmlspecialchars($log['action_level']) ?></span>
                        </td>
                        <td><?= htmlspecialchars($log['description']) ?></td>
                        <td><?= htmlspecialchars($log['table_name'] ?? 'N/A') ?></td>
                        <td><?= $log['record_id'] ?? 'N/A' ?></td>
                        <td style="width:300px; max-width:300px; position:relative;">
                            <?php if ($log['data_before']): ?>
                                <div class="mb-2">
                                    <strong>Înainte:</strong>
                                    <div class="json-data" style="max-height:150px !important; overflow-y:scroll !important; display:block !important;">
                                        <?php
                                        $data = $log['data_before'];
                                        $keywords = ['CNP', 'SQL', 'INSERT'];
                                        $shouldAnonymize = false;
                                        foreach ($keywords as $keyword) {
                                            if (str_contains($data ?? '', $keyword)) {
                                                $shouldAnonymize = true;
                                                break;
                                            }
                                        }
                                        if ($shouldAnonymize) {
                                            $data = anonymizeString($data, 5);
                                        }
                                        echo htmlspecialchars($data);
                                        ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($log['data_after']): ?>
                                <div>
                                    <strong>După:</strong>
                                    <div class="json-data" style="max-height:150px !important; overflow-y:scroll !important; display:block !important;">
                                        <?php
                                        $data = $log['data_after'];
                                        $keywords = ['CNP', 'SQL', 'INSERT'];
                                        $shouldAnonymize = false;
                                        foreach ($keywords as $keyword) {
                                            if (str_contains($data ?? '', $keyword)) {
                                                $shouldAnonymize = true;
                                                break;
                                            }
                                        }
                                        if ($shouldAnonymize) {
                                            $data = anonymizeString($data, 5);
                                        }
                                        echo htmlspecialchars($data);
                                        ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td><?= htmlspecialchars($log['ip_address']) ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <nav aria-label="Navigare pagini">
            <ul class="pagination justify-content-center">
                <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">
                    <a class="page-link"
                       href="?page=<?= $page - 1 ?>&limit=<?= $limit ?><?= !empty(array_filter($_GET, function ($key) {
                           return $key !== 'page' && $key !== 'limit';
                       }, ARRAY_FILTER_USE_KEY)) ? '&' . http_build_query(array_filter($_GET, function ($key) {
                               return $key !== 'page' && $key !== 'limit';
                           }, ARRAY_FILTER_USE_KEY)) : '' ?>" aria-label="Anterior">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>

                <?php
                $startPage = max(1, $page - 2);
                $endPage = min($totalPages, $page + 2);

                if ($startPage > 1) {
                    $queryParams = array_filter($_GET, function ($key) {
                        return $key !== 'page' && $key !== 'limit';
                    }, ARRAY_FILTER_USE_KEY);
                    $queryString = !empty($queryParams) ? '&' . http_build_query($queryParams) : '';
                    echo '<li class="page-item"><a class="page-link" href="?page=1&limit=' . $limit . $queryString . '">1</a></li>';
                    if ($startPage > 2) {
                        echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                    }
                }

                for ($i = $startPage; $i <= $endPage; $i++) {
                    $queryParams = array_filter($_GET, function ($key) {
                        return $key !== 'page' && $key !== 'limit';
                    }, ARRAY_FILTER_USE_KEY);
                    $queryString = !empty($queryParams) ? '&' . http_build_query($queryParams) : '';
                    echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '"><a class="page-link" href="?page=' . $i . '&limit=' . $limit . $queryString . '">' . $i . '</a></li>';
                }

                if ($endPage < $totalPages) {
                    $queryParams = array_filter($_GET, function ($key) {
                        return $key !== 'page' && $key !== 'limit';
                    }, ARRAY_FILTER_USE_KEY);
                    $queryString = !empty($queryParams) ? '&' . http_build_query($queryParams) : '';
                    if ($endPage < $totalPages - 1) {
                        echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                    }
                    echo '<li class="page-item"><a class="page-link" href="?page=' . $totalPages . '&limit=' . $limit . $queryString . '">' . $totalPages . '</a></li>';
                }
                ?>

                <li class="page-item <?= $page >= $totalPages ? 'disabled' : '' ?>">
                    <a class="page-link"
                       href="?page=<?= $page + 1 ?>&limit=<?= $limit ?><?= !empty(array_filter($_GET, function ($key) {
                           return $key !== 'page' && $key !== 'limit';
                       }, ARRAY_FILTER_USE_KEY)) ? '&' . http_build_query(array_filter($_GET, function ($key) {
                               return $key !== 'page' && $key !== 'limit';
                           }, ARRAY_FILTER_USE_KEY)) : '' ?>" aria-label="Următor">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
    <?php endif; ?>

    <div class="text-center mb-4">
        <p>Total înregistrări: <?= $totalRecords ?></p>
    </div>
</div>


<?php
require_once 'inc/cfg_footer.php';
?>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/jquery-ui.min.js"></script>
<link rel="stylesheet" href="assets/css/jquery-ui.min.css">
<script src="vendor/select2/select2/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function () {
        $("#date_from, #date_to").datepicker({
            dateFormat: "dd.mm.yy",  // Note: yy means full year in jQuery UI datepicker
            changeMonth: true,
            changeYear: true,
            firstDay: 1, // Monday as first day of week
            dayNamesMin: ["Du", "Lu", "Ma", "Mi", "Jo", "Vi", "Sâ"],
            monthNamesShort: ["Ian", "Feb", "Mar", "Apr", "Mai", "Iun", "Iul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        });

        // Initialize select2 for users dropdown
        $('.select2-users').select2({
            placeholder: 'Selectează utilizator',
            allowClear: true,
            width: '100%'
        });

        // Initialize select2 for IP address dropdown
        $('.select2-ip').select2({
            placeholder: 'Selectează adresă IP',
            allowClear: true,
            width: '100%'
        });

        // Style the select2 components to match Bootstrap
        $('.select2-container--default .select2-selection--single').css({
            'height': '38px',
            'padding-top': '4px',
            'border': '1px solid #ced4da'
        });

        // Add input mask for date fields
        $('#date_from, #date_to').on('input', function() {
            let value = $(this).val();
            value = value.replace(/[^0-9.]/g, '');

            // Format as dd.mm.yyyy
            if (value.length > 0) {
                value = value.replace(/\./g, '');
                if (value.length > 2) {
                    value = value.substring(0, 2) + '.' + value.substring(2);
                }
                if (value.length > 5) {
                    value = value.substring(0, 5) + '.' + value.substring(5);
                }
                if (value.length > 10) {
                    value = value.substring(0, 10);
                }
            }

            $(this).val(value);
        });
    });
</script>
