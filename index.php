<link rel="stylesheet" href="assets/css/jqueryDatatable.css">
<link href="vendor/select2/select2/dist/css/select2.min.css" rel="stylesheet"/>
<link rel="stylesheet" href="assets/css/buttons.dataTables.min.css">
<?php
require_once 'vendor/autoload.php';
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_db.php';
require_once 'inc/cfg_session.php';
require_once 'inc/cfg_functions.php';
?>

<div class="container-xxl py-5">
    <div class="container">
        <div class="row g-5 mb-5 align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <p class="text-center"><b>Expertize</b></p>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-3">
                <select id="statusFilter" class="form-select">
                    <option value="">Toate statusurile</option>
                    <option value="0">Închis</option>
                    <option value="1">În lucru</option>
                    <option value="2">Expert înlocuit</option>
                    <option value="3">Refacere</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="specializareFilter" class="form-select select2">
                    <option value="">Toate specializările</option>
                    <?= getSpecializariSelectOptgroup(); ?>
                </select>
            </div>
        </div>

        <div class="row g-5 mb-5 align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <table id="tabelExpertize" class="table table-hover">
                    <thead>
                    <tr>
                        <th>Nr. Dosar</th>
                        <th>Expert Desemnat</th>
                        <th>Specializare</th>
                        <th>Data Desemnare Expert</th>
                        <th>Status Expertiză</th>
                        <th>Acțiuni</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'inc/cfg_footer.php';
?>
<script src="assets/js/jqueryDatatable.js"></script>
<script src="assets/js/dataTables.buttons.min.js"></script>
<script src="assets/js/jszip.min.js"></script>
<script src="assets/js/pdfmake.min.js"></script>
<script src="assets/js/vfs_fonts.js"></script>
<script src="assets/js/buttons.html5.min.js"></script>
<script src="vendor/select2/select2/dist/js/select2.min.js"></script>
<script src="assets/js/expertize.js"></script>
