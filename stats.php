<?php
require_once 'vendor/autoload.php';
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
require_once 'inc/cfg_functions.php';
require_once 'inc/cfg_db.php';
$dbConnection = DatabasePool::getConnection();
global $SESSION_id_rol;
?>

<div class="container-xxl py-5">
    <div class="container">
        <div class="row g-5 mb-5 align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <p class="text-center"><b>Statistici</b></p>
            </div>
        </div>
        <div class="row g-5 mb-5 align-items-start wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-3 align-self-start">
                <table class="table table-hover">
                    <thead>
                    <tr>
                        <th style="width: min-content">Nr.crt.</th>
                        <th>Raport</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $select = "select * from exp_jud.stats where id_rol like '%$SESSION_id_rol%'";
                    $select = $dbConnection->query($select)->fetchAll();
                    $i = 1;
                    foreach ($select as $stat) {
                        echo "<tr><td>{$i}</td><td><a href='stats/{$stat['url']}' class='showStat'>{$stat['nume']}</a></td></tr>";
                        $i++;
                    }
                    ?>
                    </tbody>
                </table>
            </div>

            <div class="col-md-9">
                <div id="export-container"></div>
                <div class="raspuns"></div>
                <div id="chartsContainer"></div>
            </div>

        </div>
    </div>
</div>

<?php
require_once 'inc/cfg_footer.php';
?>
<script src="assets/js/chart.js"></script>
<script>
    $(document).ready(function () {
        let charts = {};

        $(document).off('click', '.showStat').on('click', '.showStat', function (e) {
            e.stopImmediatePropagation();
            e.preventDefault();

            const url = $(this).attr('href');
            const postForm = new FormData();
            postForm.append('showStat', 1);

            $.ajax({
                url: url,
                cache: false,
                contentType: false,
                processData: false,
                data: postForm,
                type: 'POST',
                dataType: 'json',
                success: function (response) {
                    Object.values(charts).forEach(chart => {
                        if (chart) {
                            chart.destroy();
                        }
                    });
                    charts = {};

                    $('.raspuns').empty();
                    $('#chartsContainer, #export-container').empty();

                    if (response.charts) {
                        Object.entries(response.charts).forEach(([chartId, chartData], index) => {
                            const canvasHtml = `<div class="chart-container" style="position: relative; height:400px; width:100%; margin-bottom: 30px;">
                                <canvas id="statsChart_${chartId}" style="display:block; width:100%;"></canvas>
                            </div>`;
                            $('#chartsContainer').append(canvasHtml);

                            const ctx = document.getElementById(`statsChart_${chartId}`).getContext('2d');
                            // Ensure maintainAspectRatio is false to fill container
                            const chartOptions = chartData.options || {};
                            chartOptions.maintainAspectRatio = false;

                            charts[chartId] = new Chart(ctx, {
                                type: chartData.type,
                                data: {
                                    labels: chartData.labels,
                                    datasets: chartData.datasets
                                },
                                options: chartOptions
                            });
                        });
                    }

                    if (response.data) {
                        $('.raspuns').html(response.data);
                    }

                    if(response.exportButton) {
                        $('#export-container').html(response.exportButton);
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                    $('.raspuns').html('<div class="alert alert-danger">A apărut o eroare la încărcarea datelor.</div>');
                }
            });
        });
    });
</script>