<?php
// Increase PHP execution time limit
ini_set('max_execution_time', 600); // 10 minutes
ini_set('memory_limit', '512M'); // Increase memory limit

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'utils.php';
require_once 'DataImporter.php';
require_once 'ExpertiImporter.php';

// Check if the current IP is allowed to run imports
checkIPAccess();

// Create backup
$startTime = microtime(true);

// Create HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Create Backup</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Include application CSS -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/app.min.css">
    <link rel="stylesheet" href="../vendor/fontawesome-free/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i> Step 1: Create Backup</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h4><i class="fas fa-info-circle"></i> Step 1: Create Backup</h4>
                    <p>This step creates a backup of the current experts table. This is important because:</p>
                    <ul>
                        <li>The import process will <strong>replace all existing experts data</strong></li>
                        <li>The backup allows you to compare what changed after the import</li>
                        <li>The backup can be used to restore data if needed</li>
                    </ul>
                </div>

                <?php
                // Capture output
                ob_start();
                $importer = new ExpertiImporter();
                $result = $importer->createBackupTable();
                $output = ob_get_clean();

                if ($result) {
                    echo '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i> Backup created successfully! You can now proceed to Step 2: Import Experts.</div>';
                } else {
                    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i> Failed to create backup. Check the output for details.</div>';
                }

                // Display detailed output
                echo '<div class="card mt-3">';
                echo '<div class="card-header">Detailed Output</div>';
                echo '<div class="card-body">';
                echo '<pre class="bg-light p-3">' . htmlspecialchars($output) . '</pre>';
                echo '</div>';
                echo '</div>';

                // Display execution time
                $executionTime = round(microtime(true) - $startTime, 2);
                echo "<div class='alert alert-info'><i class='fas fa-clock me-2'></i> Execution time: $executionTime seconds</div>";
                ?>

                <div class="mt-3 d-flex gap-2">
                    <a href="index.php" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i> Back to Import Tools</a>
                    <?php if ($result): ?>
                        <a href="index.php?type=experti" class="btn btn-primary"><i class="fas fa-user-tie me-2"></i> Continue to Step 2: Import Experts</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
