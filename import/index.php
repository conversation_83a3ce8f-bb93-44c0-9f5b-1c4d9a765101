<?php
// Increase PHP execution time limit
ini_set('max_execution_time', 1200); // 20 minutes
ini_set('memory_limit', '512M'); // Increase memory limit

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'utils.php';
require_once 'DataImporter.php';
require_once 'ExpertiImporter.php';
require_once 'LocalitatiImporter.php';
require_once 'SpecializariImporter.php';
require_once 'ImportReport.php';

// Check if the current IP is allowed to run imports
checkIPAccess();

// Get import type from query string
$importType = isset($_GET['type']) ? $_GET['type'] : '';

// Get report type from query string
$reportType = isset($_GET['report']) ? $_GET['report'] : '';

// Display header
?><!DOCTYPE html>
<html>
<head>
    <title>Import Tools</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Include application CSS -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/app.min.css">
    <link rel="stylesheet" href="../vendor/fontawesome-free/css/all.min.css">
    <style>
        .import-card {
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .import-card:hover {
            transform: translateY(-5px);
        }
        .import-header {
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .import-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        .stat-card i {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        @media (max-width: 768px) {
            .stat-card {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="import-header">
        <div class="container">
            <h1><i class="fas fa-database"></i> Import Tools</h1>
        </div>
    </header>
    <div class="container">
<?php

// Process import request
if (!empty($importType)) {
    $results = [];
    $startTime = microtime(true);

    ?>
    <div class="card import-card">
        <div class="card-header">
            <h3><i class="fas fa-sync-alt"></i> Import Process</h3>
        </div>
        <div class="card-body">
    <?php
    // Import localities
    if ($importType == 'all' || $importType == 'localitati') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Starting import of localities...</div>
        <?php
        // Capture output
        ob_start();
        $localitatiImporter = new LocalitatiImporter();
        $results['localitati'] = $localitatiImporter->import();
        $results['localitati']['output'] = ob_get_clean();
    }

    // Import experts
    if ($importType == 'all' || $importType == 'experti') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Starting import of experts...</div>
        <?php
        // Capture output
        ob_start();
        $expertiImporter = new ExpertiImporter();
        $results['experti'] = $expertiImporter->import();
        $results['experti']['output'] = ob_get_clean();
    }

    // Import specializations
    if ($importType == 'all' || $importType == 'specializari') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Starting import of specializations...</div>
        <?php
        // Capture output
        ob_start();
        $specializariImporter = new SpecializariImporter();
        $results['specializari'] = $specializariImporter->import();
        $results['specializari']['output'] = ob_get_clean();
    }

    // Display results if we have them
    if (!empty($results)) {
        // Calculate total execution time
        $totalTime = round(microtime(true) - $startTime, 2);
        ?>
        <div class="alert alert-info mb-4"><i class="fas fa-clock"></i> Import process completed in <?php echo $totalTime; ?> seconds</div>

        <!-- Display import results first -->
        <?php if (isset($results['localitati'])) {
            $statusClass = $results['localitati']['status'] == 'success' ? 'alert-success' : 'alert-danger';
            $statusIcon = $results['localitati']['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
            <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <strong>Localities:</strong> <?php echo $results['localitati']['message']; ?></div>
        <?php } ?>

        <?php if (isset($results['experti'])) {
            $statusClass = $results['experti']['status'] == 'success' ? 'alert-success' : 'alert-danger';
            $statusIcon = $results['experti']['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
            <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <strong>Experts:</strong> <?php echo $results['experti']['message']; ?></div>
        <?php } ?>

        <?php if (isset($results['specializari'])) {
            $statusClass = $results['specializari']['status'] == 'success' ? 'alert-success' : 'alert-danger';
            $statusIcon = $results['specializari']['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
            <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <strong>Specializations:</strong> <?php echo $results['specializari']['message']; ?></div>
        <?php } ?>

        <!-- Display stats cards -->
        <div class="row import-stats mb-4">
        <?php
        if (isset($results['localitati'])) {
            $status = $results['localitati']['status'] == 'success' ? 'success' : 'danger';
            $icon = $status == 'success' ? 'fas fa-map-marker-alt' : 'fas fa-exclamation-circle';
            $time = $results['localitati']['execution_time'] ?? 'N/A';
            $statusLabel = ucfirst($results['localitati']['status']);
            $statusClass = strtolower($statusLabel) == 'success' ? 'bg-success text-white' :
                          (strtolower($statusLabel) == 'error' ? 'bg-danger text-white' :
                          (strtolower($statusLabel) == 'warning' ? 'bg-warning text-dark' : 'bg-secondary text-white'));
            ?>
            <div class="col-md-4">
                <div class="card stat-card">
                    <div class="card-header <?php echo $statusClass; ?>">
                        <h5 class="mb-0"><i class="<?php echo $icon; ?> me-2"></i> Localities</h5>
                    </div>
                    <div class="card-body text-center">
                        <h4 class="text-<?php echo $status; ?>"><?php echo $statusLabel; ?></h4>
                        <p class="mb-0">Time: <?php echo $time; ?></p>
                    </div>
                </div>
            </div>
            <?php
        }

        if (isset($results['experti'])) {
            $status = $results['experti']['status'] == 'success' ? 'success' : 'danger';
            $icon = $status == 'success' ? 'fas fa-user-tie' : 'fas fa-exclamation-circle';
            $time = $results['experti']['execution_time'] ?? 'N/A';
            $statusLabel = ucfirst($results['experti']['status']);
            $statusClass = strtolower($statusLabel) == 'success' ? 'bg-success text-white' :
                          (strtolower($statusLabel) == 'error' ? 'bg-danger text-white' :
                          (strtolower($statusLabel) == 'warning' ? 'bg-warning text-dark' : 'bg-secondary text-white'));
            ?>
            <div class="col-md-4">
                <div class="card stat-card">
                    <div class="card-header <?php echo $statusClass; ?>">
                        <h5 class="mb-0"><i class="<?php echo $icon; ?> me-2"></i> Experts</h5>
                    </div>
                    <div class="card-body text-center">
                        <h4 class="text-<?php echo $status; ?>"><?php echo $statusLabel; ?></h4>
                        <p class="mb-0">Time: <?php echo $time; ?></p>
                    </div>
                </div>
            </div>
            <?php
        }

        if (isset($results['specializari'])) {
            $status = $results['specializari']['status'] == 'success' ? 'success' : 'danger';
            $icon = $status == 'success' ? 'fas fa-tags' : 'fas fa-exclamation-circle';
            $time = $results['specializari']['execution_time'] ?? 'N/A';
            $statusLabel = ucfirst($results['specializari']['status']);
            $statusClass = strtolower($statusLabel) == 'success' ? 'bg-success text-white' :
                          (strtolower($statusLabel) == 'error' ? 'bg-danger text-white' :
                          (strtolower($statusLabel) == 'warning' ? 'bg-warning text-dark' : 'bg-secondary text-white'));
            ?>
            <div class="col-md-4">
                <div class="card stat-card">
                    <div class="card-header <?php echo $statusClass; ?>">
                        <h5 class="mb-0"><i class="<?php echo $icon; ?> me-2"></i> Specializations</h5>
                    </div>
                    <div class="card-body text-center">
                        <h4 class="text-<?php echo $status; ?>"><?php echo $statusLabel; ?></h4>
                        <p class="mb-0">Time: <?php echo $time; ?></p>
                    </div>
                </div>
            </div>
            <?php
        }
        ?>
        </div>

        <!-- Display detailed results -->
        <div class="accordion" id="importDetailsAccordion">
            <?php if (isset($results['localitati'])) { ?>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingLocalities">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLocalities" aria-expanded="false" aria-controls="collapseLocalities">
                            <i class="fas fa-map-marker-alt me-2"></i> Localities Import Details
                        </button>
                    </h2>
                    <div id="collapseLocalities" class="accordion-collapse collapse" aria-labelledby="headingLocalities" data-bs-parent="#importDetailsAccordion">
                        <div class="accordion-body">
                            <h5>Import Output:</h5>
                            <pre class="bg-light p-3 mb-3"><?php echo htmlspecialchars($results['localitati']['output'] ?? ''); ?></pre>

                            <h5>Import Result:</h5>
                            <pre><?php
                            $resultCopy = $results['localitati'];
                            unset($resultCopy['output']); // Remove output to avoid duplication
                            echo json_encode($resultCopy, JSON_PRETTY_PRINT);
                            ?></pre>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <?php if (isset($results['experti'])) { ?>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingExperts">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExperts" aria-expanded="false" aria-controls="collapseExperts">
                            <i class="fas fa-user-tie me-2"></i> Experts Import Details
                        </button>
                    </h2>
                    <div id="collapseExperts" class="accordion-collapse collapse" aria-labelledby="headingExperts" data-bs-parent="#importDetailsAccordion">
                        <div class="accordion-body">
                            <h5>Import Output:</h5>
                            <pre class="bg-light p-3 mb-3"><?php echo htmlspecialchars($results['experti']['output'] ?? ''); ?></pre>

                            <h5>Import Result:</h5>
                            <pre><?php
                            $resultCopy = $results['experti'];
                            unset($resultCopy['output']); // Remove output to avoid duplication
                            echo json_encode($resultCopy, JSON_PRETTY_PRINT);
                            ?></pre>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <?php if (isset($results['specializari'])) { ?>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSpecializations">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSpecializations" aria-expanded="false" aria-controls="collapseSpecializations">
                            <i class="fas fa-tags me-2"></i> Specializations Import Details
                        </button>
                    </h2>
                    <div id="collapseSpecializations" class="accordion-collapse collapse" aria-labelledby="headingSpecializations" data-bs-parent="#importDetailsAccordion">
                        <div class="accordion-body">
                            <h5>Import Output:</h5>
                            <pre class="bg-light p-3 mb-3"><?php echo htmlspecialchars($results['specializari']['output'] ?? ''); ?></pre>

                            <h5>Import Result:</h5>
                            <pre><?php
                            $resultCopy = $results['specializari'];
                            unset($resultCopy['output']); // Remove output to avoid duplication
                            echo json_encode($resultCopy, JSON_PRETTY_PRINT);
                            ?></pre>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
        <?php
    }


    ?>
        </div>
    </div>
    <?php

    // Process report request
} else if (!empty($reportType)) {
    $startTime = microtime(true);

    ?>
    <div class="card import-card">
        <div class="card-header bg-warning text-dark">
            <h3><i class="fas fa-chart-bar"></i> Step 3: Import Difference Report</h3>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <h4><i class="fas fa-info-circle"></i> Step 3: View Differences</h4>
                <p>This step shows the differences between the backup (created in Step 1) and the current data (after import in Step 2):</p>
                <ul>
                    <li><strong>New CNPs:</strong> Experts that were added in the new import</li>
                    <li><strong>Removed CNPs:</strong> Experts that were in the backup but not in the new import</li>
                    <li><strong>Modified Experts:</strong> Experts whose data changed during the import</li>
                </ul>
            </div>
    <?php

    if ($reportType == 'differences') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Generating report of differences between current and previous import...</div>
        <?php

        $report = new ImportReport();
        $result = $report->generateDifferenceReport();

        $statusClass = $result['status'] == 'success' ? 'alert-success' : 'alert-danger';
        $statusIcon = $result['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
        <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <?php echo $result['message']; ?></div>

        <?php if ($result['status'] == 'success'): ?>
            <div class="row import-stats mb-4">
                <div class="col-md-4">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-plus-circle text-success mb-3"></i>
                            <h4>New Experts</h4>
                            <p><?php echo $result['summary']['new_count']; ?> new CNPs</p>
                            <a href="?report=differences&export=new" class="btn btn-sm btn-primary">Export CSV</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-minus-circle text-danger mb-3"></i>
                            <h4>Removed Experts</h4>
                            <p><?php echo $result['summary']['removed_count']; ?> removed CNPs</p>
                            <a href="?report=differences&export=removed" class="btn btn-sm btn-primary">Export CSV</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-edit text-warning mb-3"></i>
                            <h4>Modified Experts</h4>
                            <p><?php echo $result['summary']['modified_count']; ?> modified records</p>
                            <a href="?report=differences&export=modified" class="btn btn-sm btn-primary">Export CSV</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="?report=differences&export=all" class="btn btn-success"><i class="fas fa-file-export"></i> Export All to CSV</a>
            </div>

            <?php if (isset($_GET['export'])): ?>
                <?php
                $exportType = $_GET['export'];
                $exportResult = $report->exportToCsv($result);
                if ($exportResult['status'] == 'success') {
                    ?>
                    <div class="alert alert-success mt-3"><i class="fas fa-check-circle"></i> Export successful! Files saved to: <?php echo implode(', ', $exportResult['files']); ?></div>
                    <?php
                } else {
                    ?>
                    <div class="alert alert-danger mt-3"><i class="fas fa-exclamation-triangle"></i> Export failed: <?php echo $exportResult['message']; ?></div>
                    <?php
                }
                ?>
            <?php endif; ?>

            <?php if (!empty($result['new_cnps'])): ?>
                <h4 class="mt-4">New CNPs</h4>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>CNP</th>
                                <th>Nume</th>
                                <th>Prenume</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($result['new_cnps'] as $expert): ?>
                                <tr>
                                    <td><?php echo $expert['id']; ?></td>
                                    <td><?php echo $expert['cnp']; ?></td>
                                    <td><?php echo $expert['nume']; ?></td>
                                    <td><?php echo $expert['prenume']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <?php if (!empty($result['removed_cnps'])): ?>
                <h4 class="mt-4">Removed CNPs</h4>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>CNP</th>
                                <th>Nume</th>
                                <th>Prenume</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($result['removed_cnps'] as $expert): ?>
                                <tr>
                                    <td><?php echo $expert['id']; ?></td>
                                    <td><?php echo $expert['cnp']; ?></td>
                                    <td><?php echo $expert['nume']; ?></td>
                                    <td><?php echo $expert['prenume']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <?php
    }

    // Display total execution time
    $totalTime = round(microtime(true) - $startTime, 2);
    ?>
    <div class="alert alert-info mt-3"><i class="fas fa-clock"></i> Report generated in <?php echo $totalTime; ?> seconds</div>

    <div class="mt-3">
        <a href="index.php" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i> Back to Import Tools</a>
    </div>
        </div>
    </div>
    <?php

    ?>
    <div class="card import-card">
        <div class="card-header">
            <h3><i class="fas fa-sync-alt"></i> Import Process</h3>
        </div>
        <div class="card-body">
    <?php
    // Import localities
    if ($importType == 'all' || $importType == 'localitati') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Starting import of localities...</div>
        <?php
        $localitatiImporter = new LocalitatiImporter();
        $results['localitati'] = $localitatiImporter->import();

        $statusClass = $results['localitati']['status'] == 'success' ? 'alert-success' : 'alert-danger';
        $statusIcon = $results['localitati']['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
        <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <?php echo $results['localitati']['message']; ?></div>
        <pre><?php echo json_encode($results['localitati'], JSON_PRETTY_PRINT); ?></pre>
        <?php
    }

    // Import experts
    if ($importType == 'all' || $importType == 'experti') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Starting import of experts...</div>
        <?php
        $expertiImporter = new ExpertiImporter();
        $results['experti'] = $expertiImporter->import();

        $statusClass = $results['experti']['status'] == 'success' ? 'alert-success' : 'alert-danger';
        $statusIcon = $results['experti']['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
        <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <?php echo $results['experti']['message']; ?></div>
        <pre><?php echo json_encode($results['experti'], JSON_PRETTY_PRINT); ?></pre>
        <?php
    }

    // Import specializations
    if ($importType == 'all' || $importType == 'specializari') {
        ?>
        <div class="alert alert-info"><i class="fas fa-info-circle"></i> Starting import of specializations...</div>
        <?php
        $specializariImporter = new SpecializariImporter();
        $results['specializari'] = $specializariImporter->import();

        $statusClass = $results['specializari']['status'] == 'success' ? 'alert-success' : 'alert-danger';
        $statusIcon = $results['specializari']['status'] == 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        ?>
        <div class="alert <?php echo $statusClass; ?>"><i class="<?php echo $statusIcon; ?>"></i> <?php echo $results['specializari']['message']; ?></div>
        <pre><?php echo json_encode($results['specializari'], JSON_PRETTY_PRINT); ?></pre>
        <?php
    }

    // Display total execution time and stats
    $totalTime = round(microtime(true) - $startTime, 2);
    ?>
    <div class="alert alert-success"><i class="fas fa-check-circle"></i> Import process completed in <?php echo $totalTime; ?> seconds</div>

    <?php
    // Display stats if we have results
    if (!empty($results)) {
        ?>
        <div class="row import-stats">
        <?php
        if (isset($results['localitati'])) {
            $status = $results['localitati']['status'] == 'success' ? 'success' : 'danger';
            $icon = $status == 'success' ? 'fas fa-map-marker-alt' : 'fas fa-exclamation-circle';
            $time = $results['localitati']['execution_time'] ?? 'N/A';
            ?>
            <div class="col-md-4">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="<?php echo $icon; ?> text-<?php echo $status; ?> mb-3"></i>
                        <h4>Localities</h4>
                        <p>Status: <?php echo ucfirst($results['localitati']['status']); ?></p>
                        <p>Time: <?php echo $time; ?></p>
                    </div>
                </div>
            </div>
            <?php
        }

        if (isset($results['experti'])) {
            $status = $results['experti']['status'] == 'success' ? 'success' : 'danger';
            $icon = $status == 'success' ? 'fas fa-user-tie' : 'fas fa-exclamation-circle';
            $time = $results['experti']['execution_time'] ?? 'N/A';
            ?>
            <div class="col-md-4">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="<?php echo $icon; ?> text-<?php echo $status; ?> mb-3"></i>
                        <h4>Experts</h4>
                        <p>Status: <?php echo ucfirst($results['experti']['status']); ?></p>
                        <p>Time: <?php echo $time; ?></p>
                    </div>
                </div>
            </div>
            <?php
        }

        if (isset($results['specializari'])) {
            $status = $results['specializari']['status'] == 'success' ? 'success' : 'danger';
            $icon = $status == 'success' ? 'fas fa-tags' : 'fas fa-exclamation-circle';
            $time = $results['specializari']['execution_time'] ?? 'N/A';
            ?>
            <div class="col-md-4">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="<?php echo $icon; ?> text-<?php echo $status; ?> mb-3"></i>
                        <h4>Specializations</h4>
                        <p>Status: <?php echo ucfirst($results['specializari']['status']); ?></p>
                        <p>Time: <?php echo $time; ?></p>
                    </div>
                </div>
            </div>
            <?php
        }
        ?>
        </div>
        <?php
    }
    ?>
        </div>
    </div>
    <?php
}

// Display import options
?>
<div class="card import-card">
    <div class="card-header">
        <h3><i class="fas fa-cogs"></i> Import Type</h3>
    </div>
    <div class="card-body">
        <p>Select which data you want to import from the old database:</p>
                <div class="alert alert-info mb-3">
            <i class="fas fa-info-circle"></i> <strong>Important:</strong> The import process will replace all existing experts data. Please create a backup before importing.
        </div>

        <div class="d-flex flex-wrap mt-3" style="gap: 10px;">
            <a href="?type=localitati" class="btn btn-primary"><i class="fas fa-map-marker-alt"></i> Import Localities</a>

            <!-- Step 1, 2 and 3 for Experts Import -->
            <div class="d-flex flex-column" style="gap: 5px;">
                <div class="alert alert-secondary mb-0 p-2"><strong>Experts Import Process:</strong></div>
                <a href="createBackup.php" class="btn btn-info"><i class="fas fa-save"></i> Step 1: Create Backup</a>
                <a href="?type=experti" class="btn btn-primary"><i class="fas fa-user-tie"></i> Step 2: Import Experts</a>
                <a href="?report=differences" class="btn btn-warning"><i class="fas fa-chart-bar"></i> Step 3: View Differences</a>
                <a href="cnp_update.php" class="btn btn-danger"><i class="fas fa-sync"></i> Step 4: Update CNPs in Workload</a>
            </div>

            <a href="?type=specializari" class="btn btn-primary"><i class="fas fa-tags"></i> Import Specializations</a>
            <a href="?type=all" class="btn btn-success"><i class="fas fa-database"></i> Import All Data</a>
        </div>
        </div>
    </div>
</div>

<div class="card import-card">
    <div class="card-header">
        <h3><i class="fas fa-info-circle"></i> Information</h3>
    </div>
    <div class="card-body">
        <p>You can import each part separately or all at once. The import process may take some time depending on the amount of data.</p>

        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <strong>Important:</strong> Urmati acesti pasi in ordine:
            <ol>
                <li><strong>Creati un backup</strong> al tabelei experti_tehnici folosind <a href="createBackup.php" class="alert-link">Step 1: Create Backup</a> inainte de a face importul. Acest pas este OBLIGATORIU pentru a putea detecta corect modificarile CNP-urilor.</li>
                <li><strong>Importati expertii</strong> folosind <a href="?type=experti" class="alert-link">Step 2: Import Experts</a>.</li>
                <li><strong>Verificati diferentele</strong> folosind <a href="?report=differences" class="alert-link">Step 3: View Differences</a>.</li>
                <li><strong>Actualizati CNP-urile</strong> in tabela workload folosind <a href="cnp_update.php" class="alert-link">Step 4: Update CNPs in Workload</a>.</li>
            </ol>
            Acesti pasi sunt necesari pentru a mentine integritatea datelor in cazul in care CNP-urile expertilor s-au modificat in urma importului.
        </div>

        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> <strong>Note:</strong> This tool can only be accessed from localhost or the 10.1.4.* network for security reasons.
        </div>
    </div>
</div>

<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script>
    // Add some simple animations with vanilla JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Animate cards on page load
        const cards = document.querySelectorAll('.import-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'opacity 1.5s ease, transform 1.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 * index);
        });
    });
</script>
</body>
</html>
