<?php
/**
 * Base class for data importers
 * Contains common methods for importing data from the old database
 */
abstract class DataImporter {
    protected $dbConnection;
    protected $oldConnection;
    protected $startTime;
    protected $batchSize = 500;

    /**
     * Constructor
     * Initializes database connections and start time
     */
    public function __construct() {
        $this->startTime = microtime(true);
        $this->dbConnection = DatabasePool::getConnection();
        $this->oldConnection = $GLOBALS['conn_EXP_TEHNICI_OLD'];
    }

    /**
     * Main import method
     * This method should be implemented by child classes
     * @return array Import result
     */
    public function import() {
        try {
            // Create table
            $this->createTable();

            // Import data
            $count = $this->importData();

            // Cleanup
            DatabasePool::releaseConnection($this->dbConnection);
            $GLOBALS['pool_EXP_TEHNICI_OLD']->releaseConnection($this->oldConnection);

            $executionTime = round(microtime(true) - $this->startTime, 2);
            return [
                'status' => 'success',
                'message' => "Successfully imported $count records",
                'execution_time' => $executionTime . ' seconds'
            ];

        } catch (Exception $e) {
            $executionTime = round(microtime(true) - $this->startTime, 2);
            if ($this->dbConnection->inTransaction()) {
                $this->dbConnection->rollBack();
            }

            // Log the error for debugging
            error_log("Import error: " . $e->getMessage() . "\n" . $e->getTraceAsString());

            return [
                'status' => 'error',
                'message' => 'Error: ' . $e->getMessage(),
                'execution_time' => $executionTime . ' seconds'
            ];
        }
    }

    /**
     * Create table for import
     * This method should be implemented by child classes
     */
    abstract protected function createTable();

    /**
     * Import data from old database
     * This method should be implemented by child classes
     * @return int Number of imported records
     */
    abstract protected function importData();

    /**
     * Execute a batch insert
     * @param string $insertSQL SQL insert statement
     * @param array $values Values to insert
     * @param int $insertCount Number of records in this batch
     * @param int &$totalInserted Total number of inserted records (reference)
     * @param float $startTime Start time of the import
     * @param float &$lastReportTime Last time progress was reported (reference)
     */
    protected function executeBatch($insertSQL, $values, $insertCount, &$totalInserted, $startTime, &$lastReportTime) {
        try {
            $sql = $insertSQL . implode(",", $values);
            $this->dbConnection->exec($sql);
            $totalInserted += $insertCount;
            $this->dbConnection->commit();
            $this->dbConnection->beginTransaction();

            // Report progress
            $currentTime = microtime(true);
            $elapsedTime = round($currentTime - $startTime, 2);
            $recordsPerSecond = round($totalInserted / $elapsedTime, 2);
            echo "Processed $totalInserted records in $elapsedTime seconds ($recordsPerSecond records/sec)\n";
            $lastReportTime = $currentTime;

            // Reset memory if needed
            if ($elapsedTime > 60 && $elapsedTime % 60 < 5) {
                gc_collect_cycles(); // Force garbage collection
            }
        } catch (Exception $e) {
            if ($this->dbConnection->inTransaction()) {
                $this->dbConnection->rollBack();
            }
            $this->dbConnection->beginTransaction();
            throw $e;
        }
    }

    /**
     * Fix encoding issues in text
     * @param string $text Text to fix
     * @return string Fixed text
     */
    protected function fixEncoding($text) {
    if (empty($text)) {
        return '';
    }

    // Common character replacements for Romanian characters
    $replacements = [
        '?' => 'ș', // Replace question mark with ș
        '?' => 'ț', // Replace question mark with ț
        '?' => 'ă', // Replace question mark with ă
        '?' => 'î', // Replace question mark with î
        '?' => 'â', // Replace question mark with â
        'â' => 'â',
        'ş' => 'ș', // Replace old-style ş with ș
        'ţ' => 'ț', // Replace old-style ţ with ț
        '\xE2\x80\x99' => '\'', // Replace smart quote with regular quote
        '\xE2\x80\x98' => '\'', // Replace smart quote with regular quote
        '\xE2\x80\x9C' => '"', // Replace smart quote with regular quote
        '\xE2\x80\x9D' => '"', // Replace smart quote with regular quote
    ];

    // Try to detect and fix encoding issues
    if (mb_detect_encoding($text, 'UTF-8', true) === false) {
        // Try to convert from other encodings
        $encodings = ['ISO-8859-1', 'ISO-8859-2', 'Windows-1252', 'Windows-1250'];
        foreach ($encodings as $encoding) {
            $converted = mb_convert_encoding($text, 'UTF-8', $encoding);
            if (mb_detect_encoding($converted, 'UTF-8', true) !== false) {
                $text = $converted;
                break;
            }
        }
    }

    // Apply specific character replacements
    foreach ($replacements as $from => $to) {
        $text = str_replace($from, $to, $text);
    }

    // Fix common character misplacements
    $text = preg_replace('/C\x{0219}([a-z]+)/u', 'Câ$1', $text); // Cșmpina -> Câmpina
    $text = preg_replace('/c\x{0219}([a-z]+)/u', 'câ$1', $text); // cșmpina -> câmpina

    // Replace common Romanian character patterns
    $text = preg_replace('/C[^a-zA-Z0-9]l[^a-zA-Z0-9]ra/', 'Călăra', $text);

    // Fix specific problematic words
    $commonWords = [
        'Călăra?i' => 'Călărași',
        'Călăraâi' => 'Călărași',
        'Călăraâilor' => 'Călărașilor',
        'Călăra?ilor' => 'Călărașilor',
        'Cșmpina' => 'Câmpina',
        'Bucure?ti' => 'București',
        'Bucure?tiului' => 'Bucureștiului',
        'Timi?oara' => 'Timișoara',
        'Ia?i' => 'Iași',
        'Târgovi?te' => 'Târgoviște',
        'Pite?ti' => 'Pitești',
        'Ploie?ti' => 'Ploiești',
        'Constan?a' => 'Constanța',
        'Bra?ov' => 'Brașov',
        'Gala?i' => 'Galați',
        'Craiova' => 'Craiova',
        'Buz?u' => 'Buzău',
        'Foc?ani' => 'Focșani',
        'Bac?u' => 'Bacău',
        'Sibiu' => 'Sibiu',
        'Oradea' => 'Oradea',
        'Arad' => 'Arad',
        'Satu Mare' => 'Satu Mare',
        'Suceava' => 'Suceava',
        'Boto?ani' => 'Botoșani',
        'Piatra Neam?' => 'Piatra Neamț',
        'Târgu Mure?' => 'Târgu Mureș',
        'Bistri?a' => 'Bistrița',
        'Deva' => 'Deva',
        'Hunedoara' => 'Hunedoara',
        'Re?i?a' => 'Reșița',
        'Timi?' => 'Timiș',
        'Arge?' => 'Argeș',
        'Vâlcea' => 'Vâlcea',
        'Gorj' => 'Gorj',
        'Mehedin?i' => 'Mehedinți',
        'Dolj' => 'Dolj',
        'Olt' => 'Olt',
        'Teleorman' => 'Teleorman',
        'Giurgiu' => 'Giurgiu',
        'Ilfov' => 'Ilfov',
        'Ialomi?a' => 'Ialomița',
        'C?l?ra?i' => 'Călărași',
        'Br?ila' => 'Brăila',
        'Tulcea' => 'Tulcea',
        'Vaslui' => 'Vaslui',
        'Neam?' => 'Neamț',
        'Bac?u' => 'Bacău',
        'Vrancea' => 'Vrancea',
        'Covasna' => 'Covasna',
        'Harghita' => 'Harghita',
        'Mure?' => 'Mureș',
        'Alba' => 'Alba',
        'Sibiu' => 'Sibiu',
        'Bra?ov' => 'Brașov',
        'Hunedoara' => 'Hunedoara',
        'Cara?-Severin' => 'Caraș-Severin',
        'Bihor' => 'Bihor',
        'S?laj' => 'Sălaj',
        'Maramure?' => 'Maramureș',
        'Bistri?a-N?s?ud' => 'Bistrița-Năsăud',
        'Cluj' => 'Cluj',
        'Satu Mare' => 'Satu Mare',
        '?tefan cel Mare' => 'Ștefan cel Mare',
        'Mihai Viteazu' => 'Mihai Viteazu',
        'Pia?a' => 'Piața',
        'Pia?a Unirii' => 'Piața Unirii',
        'Pia?a Victoriei' => 'Piața Victoriei',
        'Pia?a Romana' => 'Piața Romană',
        'Pia?a Universit??ii' => 'Piața Universității',
        'Pia?a Revolu?iei' => 'Piața Revoluției',
        '?oseaua' => 'Șoseaua',
        'Bulevardul' => 'Bulevardul',
        'Strada' => 'Strada',
        'Aleea' => 'Aleea',
        'Intrarea' => 'Intrarea',
        'Pia?a' => 'Piața',
        'Calea' => 'Calea',
        'Drumul' => 'Drumul',
        'Splaiul' => 'Splaiul',
        'Fundătura' => 'Fundătura',
        'Pasajul' => 'Pasajul',
        'Prelungirea' => 'Prelungirea',
        'Cartierul' => 'Cartierul',
        'Zona' => 'Zona',
        'Sectorul' => 'Sectorul',
        'Jude?ul' => 'Județul',
        'Comuna' => 'Comuna',
        'Ora?ul' => 'Orașul',
        'Municipiul' => 'Municipiul',
        'Sat' => 'Sat',
        'Cătun' => 'Cătun',
        'Localitate' => 'Localitate',
        'Jude?' => 'Județ',
        'Ora?' => 'Oraș',
        // Add specific fixes for "Grâului" variants
        'Grșului' => 'Grâului',
        'Grâului' => 'Grâului', // Ensure correct form is preserved
        'str Grșului' => 'str Grâului',
        'Str Grșului' => 'Str Grâului',
    ];

    foreach ($commonWords as $wrong => $correct) {
        $text = str_ireplace($wrong, $correct, $text);
    }

    // Special case for Călărașilor
    if (stripos($text, 'Călăra') !== false && (stripos($text, 'ilor') !== false || stripos($text, 'i') !== false)) {
        $text = preg_replace('/C[ă|a]l[ă|a]ra[^ș]ilor/i', 'Călărașilor', $text);
        $text = preg_replace('/C[ă|a]l[ă|a]ra[^ș]i/i', 'Călărași', $text);
    }

    // Additional fix for any remaining "Grș" patterns that should be "Grâ"
    $text = preg_replace('/\bGrș([a-zA-ZăâîșțĂÂÎȘȚ]+)/u', 'Grâ$1', $text);

    return $text;
}
    /**
     * Clean address field by extracting email addresses and removing extra information
     * @param string $address Address string
     * @param string &$email Reference to store extracted email
     * @return string Cleaned address
     */
    protected function cleanAddress($address, &$email = null) {
        if (empty($address)) {
            $email = '';
            return '';
        }

        // Fix character encoding issues
        $address = $this->fixEncoding($address);

        // Extract email address
        $email = '';
        if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}/', $address, $matches)) {
            $email = $matches[0];
        }
        return $address;//STOP aici - mai departe e prea in detaliu pentru toate cazurile care sunt in acest moment in db ETJ

        // Remove email addresses and email prefixes
        $address = preg_replace('/\s*e-?mail:?\s*/', '', $address);
        $address = preg_replace('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}/', '', $address);

        // Remove fax numbers
        $address = preg_replace('/\s*fax:?\s*[0-9\/\-\+\s]+/', '', $address);

        // Remove phone numbers
        $address = preg_replace('/\s*(?:tel|telefon):?\s*[0-9\/\-\+\s]+/', '', $address);
        $address = preg_replace('/\s*[0-9]{6,}\s*/', '', $address);

        // Remove pipe characters and other separators
        $address = str_replace(['|', ':', ';'], ',', $address);

        // Remove company names and other non-address information
        $patterns = [
            '/\s*S\.?C\.?\s+[^,]+S\.?R\.?L\.?\s*/',
            '/\s*S\.?A\.?\s*/',
            '/\s*casuta postala\s+[^,]+\s*/',
            '/\s*oficiul postal\s+[^,]+\s*/',
            '/\s*\(Facultate:[^\)]+\)\s*/',
        ];

        foreach ($patterns as $pattern) {
            $address = preg_replace($pattern, '', $address);
        }

        // Remove phrases about availability
        $phrases = [
            'isi manifesta disponibilitatea',
            'isi manifesta dispnibilitatea',
            'disponibilitate',
            'efectua expertize la nivel national',
            'efectua expertize in toata tara',
            'efectua expertize si in',
            'expertize la nivel national',
            'expertize in toata tara',
            'nivel national',
            'de a efectua expertize in toata tara',
            'toata tara'
        ];

        foreach ($phrases as $phrase) {
            $address = str_ireplace($phrase, '', $address);
        }

        // Clean up commas and spaces
        $address = preg_replace('/\s*,\s*/', ', ', $address);
        $address = preg_replace('/,+/', ',', $address);
        $address = preg_replace('/\s+/', ' ', $address);
        $address = trim($address);
        $address = rtrim($address, ',');

        return $address;
    }

    /**
     * Clean phone numbers by removing duplicates and empty values
     * @param string $phoneString Comma-separated phone numbers
     * @return string Cleaned phone numbers
     */
    protected function cleanPhoneNumbers($phoneString) {
        if (empty($phoneString)) {
            return '';
        }

        // Split by comma
        $phones = explode(',', $phoneString);

        // Clean each phone number
        $cleanedPhones = [];
        foreach ($phones as $phone) {
            $phone = trim($phone);

            // Skip empty phone numbers
            if (empty($phone)) {
                continue;
            }

            // Standardize format - remove non-numeric characters except leading +
            if (substr($phone, 0, 1) === '+') {
                $prefix = '+';
                $phone = substr($phone, 1);
            } else {
                $prefix = '';
            }

            // Remove non-numeric characters
            $phone = preg_replace('/[^0-9]/', '', $phone);

            // Add prefix back
            $phone = $prefix . $phone;

            // Check for unusually long phone numbers (likely errors)
            if (strlen($phone) > 15) {
                // Try to extract a valid phone number
                if (preg_match('/(?:^|[^0-9])(0[0-9]{9})(?:$|[^0-9])/', $phone, $matches)) {
                    $phone = $matches[1];
                } else if (preg_match('/(?:^|[^0-9])([0-9]{9,10})(?:$|[^0-9])/', $phone, $matches)) {
                    $phone = $matches[1];
                    // Add Romanian prefix if missing and number is 9 digits starting with 7
                    if (strlen($phone) === 9 && substr($phone, 0, 1) === '7') {
                        $phone = '0' . $phone;
                    }
                } else {
                    // Can't extract a valid phone number
                    continue;
                }
            }

            // Ensure phone number has at least 6 digits and not more than 15
            if (strlen($phone) < 6 || strlen($phone) > 15) {
                continue;
            }

            // Add Romanian prefix if missing and number starts with 7
            if (strlen($phone) === 9 && substr($phone, 0, 1) === '7') {
                $phone = '0' . $phone;
            }

            // Add to cleaned phones if not already there
            if (!empty($phone) && !in_array($phone, $cleanedPhones)) {
                $cleanedPhones[] = $phone;
            }
        }

        return implode(',', $cleanedPhones);
    }

    /**
     * Ensure phone numbers don't overlap between telefon and telefon2
     * @param array $expert Expert data
     * @return array Updated expert data
     */
    protected function deduplicatePhoneNumbers($expert) {
        if (empty($expert['telefon']) || empty($expert['telefon2'])) {
            return $expert;
        }

        $phones1 = explode(',', $expert['telefon']);
        $phones2 = explode(',', $expert['telefon2']);

        // Remove duplicates
        $uniquePhones1 = [];
        foreach ($phones1 as $phone) {
            if (!in_array($phone, $uniquePhones1) && !empty($phone)) {
                $uniquePhones1[] = $phone;
            }
        }

        $uniquePhones2 = [];
        foreach ($phones2 as $phone) {
            if (!in_array($phone, $phones1) && !in_array($phone, $uniquePhones2) && !empty($phone)) {
                $uniquePhones2[] = $phone;
            }
        }

        $expert['telefon'] = implode(',', $uniquePhones1);
        $expert['telefon2'] = implode(',', $uniquePhones2);

        return $expert;
    }

    /**
     * Clean legitimatie field by removing duplicates and fixing format issues
     * @param string $legitimatieString Comma-separated legitimatie values
     * @return string Cleaned legitimatie values
     */
    protected function cleanLegitimatie($legitimatieString) {
        if (empty($legitimatieString)) {
            return '';
        }

        // Split by comma
        $legitimatii = explode(',', $legitimatieString);

        // Clean each legitimatie
        $cleanedLegitimatii = [];
        foreach ($legitimatii as $legitimatie) {
            $legitimatie = trim($legitimatie);

            // Skip empty values
            if (empty($legitimatie)) {
                continue;
            }

            // Preserve all legitimation formats, including those with negative prefixes
            // Only deduplicate identical values
            if (!in_array($legitimatie, $cleanedLegitimatii)) {
                $cleanedLegitimatii[] = $legitimatie;
            }
        }

        return implode(',', $cleanedLegitimatii);
    }
}
