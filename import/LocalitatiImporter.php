<?php
// Increase PHP execution time limit
ini_set('max_execution_time', 600); // 10 minutes
ini_set('memory_limit', '512M'); // Increase memory limit

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'utils.php';
require_once 'DataImporter.php';

// Check if the current IP is allowed to run imports
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    checkIPAccess(true);
}

/**
 * Class for importing localities from the old database
 */
class LocalitatiImporter extends DataImporter {
    /**
     * Create the localities table
     */
    protected function createTable() {
        $sql = "CREATE TABLE IF NOT EXISTS exp_jud.z_localitati (
            `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
            `localitate` VARCHAR(250) NULL,
            `id_judet` INT UNSIGNED NULL,
            PRIMARY KEY (`id`),
            <PERSON>EY `idx_judet` (`id_judet`),
            KEY `idx_localitate` (`localitate`(191))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

        // Drop and recreate table for clean import
        $this->dbConnection->exec("DROP TABLE IF EXISTS exp_jud.z_localitati");
        $this->dbConnection->exec($sql);
    }

    /**
     * Import localities data
     * @return int Number of imported localities
     */
    protected function importData() {
        // Get localities data
        echo "Fetching localities data...\n";
        $localitiesData = $this->fetchLocalitiesData();
        echo "Found " . count($localitiesData) . " localities.\n";

        // Process and insert data
        echo "Processing and inserting localities data...\n";
        return $this->insertLocalitiesData($localitiesData);
    }

    /**
     * Get localities query
     * @return string SQL query for localities
     */
    private function getLocalitatiQuery() {
        return "SELECT [id], [nume], [id_judet] FROM [experti_tehnici].[dbo].[NLocalitate] WITH (NOLOCK)";
    }

    /**
     * Fetch localities data from the old database
     * @return array Localities data
     */
    private function fetchLocalitiesData() {
        $dataStmt = sqlsrv_query($this->oldConnection, $this->getLocalitatiQuery(), [], ['QueryTimeout' => 300]);
        if ($dataStmt === false) {
            throw new Exception(print_r(sqlsrv_errors(), true));
        }

        $localities = [];
        while ($row = sqlsrv_fetch_array($dataStmt, SQLSRV_FETCH_ASSOC)) {
            // Clean and fix locality name
            $nume = isset($row['nume']) && $row['nume'] !== null ? $this->fixEncoding(trim($row['nume'])) : '';

            $localities[] = [
                'id' => $row['id'],
                'nume' => $nume,
                'id_judet' => $row['id_judet']
            ];
        }

        sqlsrv_free_stmt($dataStmt);
        return $localities;
    }

    /**
     * Insert localities data into the database
     * @param array $localitiesData Localities data
     * @return int Number of inserted localities
     */
    private function insertLocalitiesData($localitiesData) {
        $insertSQL = "INSERT INTO exp_jud.z_localitati (id, localitate, id_judet) VALUES ";

        $values = [];
        $insertCount = 0;
        $totalInserted = 0;
        $errorCount = 0;
        $batchSize = 100; // Smaller batch size for better error handling
        $startTime = microtime(true);
        $lastReportTime = $startTime;

        $this->dbConnection->setAttribute(PDO::ATTR_AUTOCOMMIT, false);
        $this->dbConnection->beginTransaction();

        foreach ($localitiesData as $locality) {
            $rowValues = [
                $locality['id'] === null ? "NULL" : $this->dbConnection->quote($locality['id']),
                $this->dbConnection->quote($locality['nume']),
                $locality['id_judet'] === null ? "NULL" : $this->dbConnection->quote($locality['id_judet'])
            ];

            $values[] = "(" . implode(",", $rowValues) . ")";

            $insertCount++;
            if ($insertCount >= $batchSize) {
                try {
                    $this->executeBatch($insertSQL, $values, $insertCount, $totalInserted, $startTime, $lastReportTime);
                } catch (Exception $e) {
                    $errorCount++;
                    echo "Error during batch insert: " . $e->getMessage() . "\n";

                    // Try inserting one by one to identify problematic records
                    foreach ($values as $value) {
                        try {
                            $singleSQL = $insertSQL . $value;
                            $this->dbConnection->exec($singleSQL);
                            $totalInserted++;
                        } catch (Exception $innerE) {
                            echo "Error inserting record: " . $innerE->getMessage() . "\n";
                            echo "Problematic record: " . $value . "\n";
                        }
                    }

                    // Start a new transaction
                    if ($this->dbConnection->inTransaction()) {
                        $this->dbConnection->rollBack();
                    }
                    $this->dbConnection->beginTransaction();
                }

                $values = [];
                $insertCount = 0;
                $lastReportTime = microtime(true);
            }
        }

        if (!empty($values)) {
            try {
                $this->executeBatch($insertSQL, $values, $insertCount, $totalInserted, $startTime, $lastReportTime);
            } catch (Exception $e) {
                $errorCount++;
                echo "Error during final batch insert: " . $e->getMessage() . "\n";

                // Try inserting one by one
                foreach ($values as $value) {
                    try {
                        $singleSQL = $insertSQL . $value;
                        $this->dbConnection->exec($singleSQL);
                        $totalInserted++;
                    } catch (Exception $innerE) {
                        echo "Error inserting record: " . $innerE->getMessage() . "\n";
                    }
                }
            }
        }

        if ($this->dbConnection->inTransaction()) {
            $this->dbConnection->commit();
        }

        $this->dbConnection->setAttribute(PDO::ATTR_AUTOCOMMIT, true);

        echo "Total errors during insert: $errorCount\n";
        return $totalInserted;
    }
}

// Execute import if this file is called directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $importer = new LocalitatiImporter();
    echo json_encode($importer->import());
}
