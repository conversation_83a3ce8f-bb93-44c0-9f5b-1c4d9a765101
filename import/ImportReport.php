<?php
require_once '../inc/cfg_db.php';
require_once 'utils.php';

// Check if the current IP is allowed to run imports
checkIPAccess();

/**
 * Class ImportReport
 * Generates reports about import differences
 */
class ImportReport {
    private $dbConnection;

    /**
     * Constructor
     */
    public function __construct() {
        $this->dbConnection = DatabasePool::getConnection();
    }

    /**
     * Generate a report of differences between current and previous import
     * @return array Report data
     */
    public function generateDifferenceReport() {
        // Check if backup table exists
        try {
            $stmt = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'experti_tehnici_backup'");
            $backupExists = $stmt && $stmt->rowCount() > 0;

            if (!$backupExists) {
                return [
                    'status' => 'error',
                    'message' => 'Backup table does not exist. Please create a backup first by clicking "Create Experts Backup" button.'
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Error checking backup table: ' . $e->getMessage()
            ];
        }

        // Get new CNPs (in current but not in backup)
        $newCnps = $this->getNewCnps();

        // Get removed CNPs (in backup but not in current)
        $removedCnps = $this->getRemovedCnps();

        // Get modified experts (in both but with differences)
        $modifiedExperts = $this->getModifiedExperts();

        // Generate summary
        $summary = [
            'new_count' => count($newCnps),
            'removed_count' => count($removedCnps),
            'modified_count' => count($modifiedExperts)
        ];

        return [
            'status' => 'success',
            'message' => 'Report generated successfully',
            'summary' => $summary,
            'new_cnps' => $newCnps,
            'removed_cnps' => $removedCnps,
            'modified_experts' => $modifiedExperts
        ];
    }

    /**
     * Get CNPs that are in the current table but not in the backup
     * @return array New CNPs
     */
    private function getNewCnps() {
        $sql = "SELECT e.id, e.cnp, e.nume, e.prenume
                FROM exp_jud.experti_tehnici e
                LEFT JOIN exp_jud.experti_tehnici_backup b ON e.cnp = b.cnp
                WHERE b.cnp IS NULL";

        $stmt = $this->dbConnection->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get CNPs that are in the backup but not in the current table
     * @return array Removed CNPs
     */
    private function getRemovedCnps() {
        $sql = "SELECT b.id, b.cnp, b.nume, b.prenume
                FROM exp_jud.experti_tehnici_backup b
                LEFT JOIN exp_jud.experti_tehnici e ON b.cnp = e.cnp
                WHERE e.cnp IS NULL";

        $stmt = $this->dbConnection->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get experts that exist in both tables but have differences
     * @return array Modified experts
     */
    private function getModifiedExperts() {
        $sql = "SELECT
                    e.id as current_id,
                    b.id as backup_id,
                    e.cnp,
                    e.nume as current_nume,
                    b.nume as backup_nume,
                    e.prenume as current_prenume,
                    b.prenume as backup_prenume,
                    e.specializari as current_specializari,
                    b.specializari as backup_specializari,
                    e.id_judet as current_judet,
                    b.id_judet as backup_judet
                FROM exp_jud.experti_tehnici e
                JOIN exp_jud.experti_tehnici_backup b ON e.cnp = b.cnp
                WHERE
                    e.nume != b.nume OR
                    e.prenume != b.prenume OR
                    e.specializari != b.specializari OR
                    e.subspecializari != b.subspecializari OR
                    e.id_judet != b.id_judet OR
                    e.id_localitate != b.id_localitate OR
                    e.telefon != b.telefon OR
                    e.telefon2 != b.telefon2 OR
                    e.legitimatie != b.legitimatie OR
                    e.adresa != b.adresa";

        $stmt = $this->dbConnection->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Export the report to CSV files
     * @param array $report Report data
     * @return array Result of export
     */
    public function exportToCsv($report) {
        if ($report['status'] !== 'success') {
            return [
                'status' => 'error',
                'message' => 'Cannot export invalid report'
            ];
        }

        $timestamp = date('Y-m-d_H-i-s');
        $exportDir = '../exports';

        // Create exports directory if it doesn't exist
        if (!file_exists($exportDir)) {
            mkdir($exportDir, 0755, true);
        }

        // Export new CNPs
        $newCnpsFile = "$exportDir/new_cnps_$timestamp.csv";
        $this->exportArrayToCsv($report['new_cnps'], $newCnpsFile);

        // Export removed CNPs
        $removedCnpsFile = "$exportDir/removed_cnps_$timestamp.csv";
        $this->exportArrayToCsv($report['removed_cnps'], $removedCnpsFile);

        // Export modified experts
        $modifiedExpertsFile = "$exportDir/modified_experts_$timestamp.csv";
        $this->exportArrayToCsv($report['modified_experts'], $modifiedExpertsFile);

        return [
            'status' => 'success',
            'message' => 'Report exported successfully',
            'files' => [
                'new_cnps' => $newCnpsFile,
                'removed_cnps' => $removedCnpsFile,
                'modified_experts' => $modifiedExpertsFile
            ]
        ];
    }

    /**
     * Export an array to a CSV file
     * @param array $data Data to export
     * @param string $filename Filename to export to
     * @return bool Success
     */
    private function exportArrayToCsv($data, $filename) {
        if (empty($data)) {
            file_put_contents($filename, "No data\n");
            return true;
        }

        $fp = fopen($filename, 'w');

        // Write headers
        fputcsv($fp, array_keys($data[0]));

        // Write data
        foreach ($data as $row) {
            fputcsv($fp, $row);
        }

        fclose($fp);
        return true;
    }
}

// Execute if this file is called directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $report = new ImportReport();
    $result = $report->generateDifferenceReport();

    if (isset($_GET['export']) && $_GET['export'] === 'true') {
        $exportResult = $report->exportToCsv($result);
        echo json_encode($exportResult);
    } else {
        echo json_encode($result);
    }
}
