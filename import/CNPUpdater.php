<?php
require_once '../inc/cfg_db.php';
require_once 'utils.php';

// Check if the current IP is allowed to run imports
checkIPAccess();

/**
 * Class CNPUpdater
 * Updates CNPs in workload table when they change in experti_tehnici
 */
class CNPUpdater {
    private $dbConnection;

    /**
     * Constructor
     */
    public function __construct() {
        $this->dbConnection = DatabasePool::getConnection();
    }

    /**
     * Create a backup of the workload table
     * @return array Result of the backup operation
     */
    public function createWorkloadBackup() {
        try {
            // Check if backup table already exists
            $stmt = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'workload_backup'");
            $backupExists = $stmt && $stmt->rowCount() > 0;

            // If backup exists, drop it
            if ($backupExists) {
                $this->dbConnection->exec("DROP TABLE exp_jud.workload_backup");
            }

            // Create backup table
            $this->dbConnection->exec("CREATE TABLE exp_jud.workload_backup LIKE exp_jud.workload");
            $this->dbConnection->exec("INSERT INTO exp_jud.workload_backup (CNP, incarcatura_ETJ, incarcatura_app, ts) SELECT CNP, incarcatura_ETJ, incarcatura_app, ts FROM exp_jud.workload");

            // Log the backup operation
            $this->logOperation('backup', 'workload', 'Backup creat pentru tabela workload');

            return [
                'status' => 'success',
                'message' => 'Backup-ul tabelei workload a fost creat cu succes.'
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Eroare la crearea backup-ului: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update CNPs in workload table based on changes in experti_tehnici
     * @return array Result of the update operation
     */
    public function updateCNPs() {
        try {
            // First, create a backup
            $backupResult = $this->createWorkloadBackup();
            if ($backupResult['status'] !== 'success') {
                return $backupResult;
            }

            // Check if experti_tehnici_backup exists
            $stmt = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'experti_tehnici_backup'");
            $backupExists = $stmt && $stmt->rowCount() > 0;

            if (!$backupExists) {
                return [
                    'status' => 'error',
                    'message' => 'Tabela experti_tehnici_backup nu există. Vă rugăm să creați mai întâi un backup al experților.'
                ];
            }

            // Find experts whose CNP has changed
            // This query identifies experts that exist in both current and backup tables
            // but with different CNPs, matching them by name and other identifying information
            $sql = "SELECT
                        e.id as current_id,
                        e.cnp as current_cnp,
                        b.cnp as old_cnp,
                        e.nume,
                        e.prenume,
                        e.id_judet,
                        e.updated_at as last_update
                    FROM exp_jud.experti_tehnici e
                    JOIN exp_jud.experti_tehnici_backup b
                    ON e.nume = b.nume
                    AND e.prenume = b.prenume
                    AND e.id_judet = b.id_judet
                    AND e.cnp != b.cnp";

            $stmt = $this->dbConnection->query($sql);
            $changedExperts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($changedExperts)) {
                return [
                    'status' => 'success',
                    'message' => 'Nu s-au găsit experți cu CNP-uri modificate.'
                ];
            }

            // Detect and handle cyclic CNP changes
            $expertsByName = [];
            foreach ($changedExperts as $expert) {
                $key = $expert['nume'] . '|' . $expert['prenume'] . '|' . $expert['id_judet'];
                if (!isset($expertsByName[$key])) {
                    $expertsByName[$key] = [];
                }
                $expertsByName[$key][] = $expert;
            }

            // Process each expert group to handle cyclic CNPs
            $processedExperts = [];
            $cyclicCnpCount = 0;

            foreach ($expertsByName as $key => $experts) {
                if (count($experts) > 1) {
                    // Cyclic CNP change detected
                    $cyclicCnpCount++;

                    // Sort experts by last update time (newest first)
                    usort($experts, function($a, $b) {
                        return strtotime($b['last_update']) - strtotime($a['last_update']);
                    });

                    // Use only the most recent expert record
                    $processedExperts[] = $experts[0];
                } else {
                    // Single CNP change
                    $processedExperts[] = $experts[0];
                }
            }

            // Update workload table for each expert with changed CNP
            $updatedCount = 0;
            $errors = [];

            foreach ($processedExperts as $expert) {
                try {
                    // Check if the old CNP exists in workload
                    $checkSql = "SELECT * FROM exp_jud.workload WHERE CNP = :old_cnp";
                    $checkStmt = $this->dbConnection->prepare($checkSql);
                    $checkStmt->execute([':old_cnp' => $expert['old_cnp']]);
                    $workloadEntry = $checkStmt->fetch(PDO::FETCH_ASSOC);

                    if ($workloadEntry) {
                        // Check if the new CNP already exists in workload
                        $checkNewSql = "SELECT * FROM exp_jud.workload WHERE CNP = :new_cnp";
                        $checkNewStmt = $this->dbConnection->prepare($checkNewSql);
                        $checkNewStmt->execute([':new_cnp' => $expert['current_cnp']]);
                        $newCnpExists = $checkNewStmt->fetch(PDO::FETCH_ASSOC);

                        if ($newCnpExists) {
                            // If new CNP already exists, merge the workload values
                            $updateSql = "UPDATE exp_jud.workload
                                         SET incarcatura_ETJ = incarcatura_ETJ + :etj,
                                             incarcatura_app = incarcatura_app + :app
                                         WHERE CNP = :new_cnp and idLoad > 0";

                            $updateStmt = $this->dbConnection->prepare($updateSql);
                            $updateStmt->execute([
                                ':etj' => $workloadEntry['incarcatura_ETJ'],
                                ':app' => $workloadEntry['incarcatura_app'],
                                ':new_cnp' => $expert['current_cnp']
                            ]);

                            // Delete the old CNP entry
                            $deleteSql = "DELETE FROM exp_jud.workload WHERE CNP = :old_cnp";
                            $deleteStmt = $this->dbConnection->prepare($deleteSql);
                            $deleteStmt->execute([':old_cnp' => $expert['old_cnp']]);
                        } else {
                            // If new CNP doesn't exist, just update the CNP
                            $updateSql = "UPDATE exp_jud.workload SET CNP = :new_cnp WHERE CNP = :old_cnp and idLoad > 0";
                            $updateStmt = $this->dbConnection->prepare($updateSql);
                            $updateStmt->execute([
                                ':new_cnp' => $expert['current_cnp'],
                                ':old_cnp' => $expert['old_cnp']
                            ]);
                        }

                        // Update CNP in expertize table if it exists
                        $updateExpertizeSql = "UPDATE exp_jud.expertize SET cnpExpert = :new_cnp WHERE cnpExpert = :old_cnp";
                        $updateExpertizeStmt = $this->dbConnection->prepare($updateExpertizeSql);
                        $updateExpertizeStmt->execute([
                            ':new_cnp' => $expert['current_cnp'],
                            ':old_cnp' => $expert['old_cnp']
                        ]);

                        $updatedCount++;

                        // Log the update operation
                        $this->logOperation(
                            'update',
                            'workload',
                            "CNP actualizat pentru expertul {$expert['nume']} {$expert['prenume']}",
                            ['old_cnp' => $expert['old_cnp']],
                            ['new_cnp' => $expert['current_cnp']]
                        );
                    }
                } catch (Exception $e) {
                    $errors[] = "Eroare la actualizarea CNP-ului pentru {$expert['nume']} {$expert['prenume']}: " . $e->getMessage();
                }
            }

            if (!empty($errors)) {
                return [
                    'status' => 'partial',
                    'message' => "S-au actualizat $updatedCount CNP-uri, dar au apărut următoarele erori:",
                    'errors' => $errors
                ];
            }

            $message = "S-au actualizat cu succes $updatedCount CNP-uri în tabela workload.";
            if ($cyclicCnpCount > 0) {
                $message .= " S-au detectat $cyclicCnpCount experți cu CNP-uri ciclice, s-a folosit cel mai recent CNP pentru fiecare.";
            }

            return [
                'status' => 'success',
                'message' => $message,
                'updated_count' => $updatedCount,
                'experts' => $processedExperts,
                'cyclic_count' => $cyclicCnpCount
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Eroare la actualizarea CNP-urilor: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Restore workload table from backup
     * @return array Result of the restore operation
     */
    public function restoreWorkloadFromBackup() {
        try {
            // Check if backup table exists
            $stmt = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'workload_backup'");
            $backupExists = $stmt && $stmt->rowCount() > 0;

            if (!$backupExists) {
                return [
                    'status' => 'error',
                    'message' => 'Nu există un backup al tabelei workload.'
                ];
            }

            // Restore from backup
            $this->dbConnection->exec("DROP TABLE IF EXISTS exp_jud.workload");
            $this->dbConnection->exec("CREATE TABLE exp_jud.workload LIKE exp_jud.workload_backup");
            $this->dbConnection->exec("INSERT INTO exp_jud.workload (CNP, incarcatura_ETJ, incarcatura_app, ts) SELECT CNP, incarcatura_ETJ, incarcatura_app, ts FROM exp_jud.workload_backup");

            // Log the restore operation
            $this->logOperation('restore', 'workload', 'Tabela workload restaurată din backup');

            return [
                'status' => 'success',
                'message' => 'Tabela workload a fost restaurată cu succes din backup.'
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Eroare la restaurarea tabelei workload: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate a report of CNP changes
     * @return array Report data
     */
    public function generateCNPChangeReport() {
        try {
            // Check if experti_tehnici_backup exists
            $stmt = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'experti_tehnici_backup'");
            $backupExists = $stmt && $stmt->rowCount() > 0;

            if (!$backupExists) {
                return [
                    'status' => 'error',
                    'message' => 'Tabela experti_tehnici_backup nu există. Vă rugăm să creați mai întâi un backup al experților.'
                ];
            }

            // Find experts whose CNP has changed
            $sql = "SELECT
                        e.id as current_id,
                        e.cnp as current_cnp,
                        b.cnp as old_cnp,
                        e.nume,
                        e.prenume,
                        e.id_judet,
                        j.numeJudet as judet,
                        e.updated_at as last_update
                    FROM exp_jud.experti_tehnici e
                    JOIN exp_jud.experti_tehnici_backup b
                    ON e.nume = b.nume
                    AND e.prenume = b.prenume
                    AND e.id_judet = b.id_judet
                    AND e.cnp != b.cnp
                    LEFT JOIN exp_jud.njudete j ON e.id_judet = j.idJudet";

            $stmt = $this->dbConnection->query($sql);
            $changedExperts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($changedExperts)) {
                return [
                    'status' => 'success',
                    'message' => 'Nu s-au găsit experți cu CNP-uri modificate.',
                    'experts' => []
                ];
            }

            // Detect cyclic CNP changes
            $expertsByName = [];
            foreach ($changedExperts as $expert) {
                $key = $expert['nume'] . '|' . $expert['prenume'] . '|' . $expert['id_judet'];
                if (!isset($expertsByName[$key])) {
                    $expertsByName[$key] = [];
                }
                $expertsByName[$key][] = $expert;
            }

            // Process each expert group
            $processedExperts = [];
            $cyclicCnpCount = 0;

            foreach ($expertsByName as $key => $experts) {
                if (count($experts) > 1) {
                    // Potential cyclic CNP change detected
                    $cyclicCnpCount++;

                    // Find all unique CNPs for this expert
                    $allCnps = [];
                    foreach ($experts as $expert) {
                        $allCnps[$expert['current_cnp']] = true;
                        $allCnps[$expert['old_cnp']] = true;
                    }

                    // Sort experts by last update time (newest first)
                    usort($experts, function($a, $b) {
                        return strtotime($b['last_update']) - strtotime($a['last_update']);
                    });

                    // Use the most recent expert record
                    $mostRecentExpert = $experts[0];
                    $mostRecentExpert['has_cyclic_cnp'] = true;
                    $mostRecentExpert['all_cnps'] = array_keys($allCnps);
                    $processedExperts[] = $mostRecentExpert;
                } else {
                    // Single CNP change
                    $experts[0]['has_cyclic_cnp'] = false;
                    $processedExperts[] = $experts[0];
                }
            }

            // Check which experts have entries in workload
            foreach ($processedExperts as &$expert) {
                // Check if old CNP exists in workload
                $checkSql = "SELECT * FROM exp_jud.workload WHERE CNP = :old_cnp";
                $checkStmt = $this->dbConnection->prepare($checkSql);
                $checkStmt->execute([':old_cnp' => $expert['old_cnp']]);
                $expert['in_workload'] = $checkStmt->rowCount() > 0;

                if ($expert['in_workload']) {
                    $workloadData = $checkStmt->fetch(PDO::FETCH_ASSOC);
                    $expert['incarcatura_ETJ'] = $workloadData['incarcatura_ETJ'];
                    $expert['incarcatura_app'] = $workloadData['incarcatura_app'];
                    $expert['total'] = $workloadData['total'];
                }

                // Check if the new CNP already exists in workload
                $checkNewSql = "SELECT * FROM exp_jud.workload WHERE CNP = :new_cnp";
                $checkNewStmt = $this->dbConnection->prepare($checkNewSql);
                $checkNewStmt->execute([':new_cnp' => $expert['current_cnp']]);
                $expert['new_cnp_in_workload'] = $checkNewStmt->rowCount() > 0;

                if ($expert['new_cnp_in_workload']) {
                    $newWorkloadData = $checkNewStmt->fetch(PDO::FETCH_ASSOC);
                    $expert['new_incarcatura_ETJ'] = $newWorkloadData['incarcatura_ETJ'];
                    $expert['new_incarcatura_app'] = $newWorkloadData['incarcatura_app'];
                    $expert['new_total'] = $newWorkloadData['total'];
                }

                // If this is a cyclic CNP change, check all possible CNPs in workload
                if ($expert['has_cyclic_cnp'] && isset($expert['all_cnps'])) {
                    $expert['all_cnp_workloads'] = [];
                    foreach ($expert['all_cnps'] as $cnp) {
                        if ($cnp == $expert['current_cnp'] || $cnp == $expert['old_cnp']) {
                            continue; // Already checked these
                        }

                        $checkOtherSql = "SELECT * FROM exp_jud.workload WHERE CNP = :cnp";
                        $checkOtherStmt = $this->dbConnection->prepare($checkOtherSql);
                        $checkOtherStmt->execute([':cnp' => $cnp]);

                        if ($checkOtherStmt->rowCount() > 0) {
                            $otherWorkloadData = $checkOtherStmt->fetch(PDO::FETCH_ASSOC);
                            $expert['all_cnp_workloads'][$cnp] = [
                                'incarcatura_ETJ' => $otherWorkloadData['incarcatura_ETJ'],
                                'incarcatura_app' => $otherWorkloadData['incarcatura_app'],
                                'total' => $otherWorkloadData['total']
                            ];
                        }
                    }
                }
            }

            return [
                'status' => 'success',
                'message' => 'Raport generat cu succes. ' . ($cyclicCnpCount > 0 ? "S-au detectat $cyclicCnpCount experți cu CNP-uri ciclice." : ''),
                'experts' => $processedExperts,
                'count' => count($processedExperts),
                'cyclic_count' => $cyclicCnpCount
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Eroare la generarea raportului: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Export the CNP change report to CSV
     * @param array $report Report data
     * @return array Result of export
     */
    public function exportToCsv($report) {
        if ($report['status'] !== 'success' || empty($report['experts'])) {
            return [
                'status' => 'error',
                'message' => 'Nu există date pentru export sau raportul este invalid.'
            ];
        }

        try {
            $timestamp = date('Y-m-d_H-i-s');
            $exportDir = '../exports';

            // Create exports directory if it doesn't exist
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0755, true);
            }

            $filename = "$exportDir/cnp_changes_$timestamp.csv";
            $fp = fopen($filename, 'w');

            // Write headers
            fputcsv($fp, [
                'ID', 'Nume', 'Prenume', 'Județ', 'CNP Vechi', 'CNP Nou',
                'În Workload', 'Încărcătură ETJ', 'Încărcătură APP', 'Total'
            ]);

            // Write data
            foreach ($report['experts'] as $expert) {
                fputcsv($fp, [
                    $expert['current_id'],
                    $expert['nume'],
                    $expert['prenume'],
                    $expert['judet'],
                    $expert['old_cnp'],
                    $expert['current_cnp'],
                    $expert['in_workload'] ? 'Da' : 'Nu',
                    $expert['in_workload'] ? $expert['incarcatura_ETJ'] : 'N/A',
                    $expert['in_workload'] ? $expert['incarcatura_app'] : 'N/A',
                    $expert['in_workload'] ? $expert['total'] : 'N/A'
                ]);
            }

            fclose($fp);

            return [
                'status' => 'success',
                'message' => 'Raportul a fost exportat cu succes.',
                'file' => $filename
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Eroare la exportul raportului: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Log an operation to the audit_log table
     * @param string $action_type Type of action (backup, update, restore)
     * @param string $table_name Name of the table
     * @param string $description Description of the operation
     * @param array $data_before Data before the operation (optional)
     * @param array $data_after Data after the operation (optional)
     */
    private function logOperation($action_type, $table_name, $description, $data_before = null, $data_after = null) {
        try {
            $sql = "INSERT INTO exp_jud.audit_log
                    (action_type, action_category, action_level, user_id, description, table_name, data_before, data_after, ip_address)
                    VALUES
                    (:action_type, 'import', 'info', :user_id, :description, :table_name, :data_before, :data_after, :ip_address)";

            $stmt = $this->dbConnection->prepare($sql);
            $stmt->execute([
                ':action_type' => $action_type,
                ':user_id' => $_SESSION['id_utilizator'] ?? null,
                ':description' => $description,
                ':table_name' => $table_name,
                ':data_before' => $data_before ? json_encode($data_before) : null,
                ':data_after' => $data_after ? json_encode($data_after) : null,
                ':ip_address' => $_SERVER['REMOTE_ADDR']
            ]);
        } catch (Exception $e) {
            // Just log the error, don't throw
            error_log("Error logging operation: " . $e->getMessage());
        }
    }
}

// Execute if this file is called directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // Redirect to the new interface
    header('Location: cnp_update.php');
    exit;
}
