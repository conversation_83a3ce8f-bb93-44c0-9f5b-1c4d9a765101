<?php
// Increase PHP execution time limit
ini_set('max_execution_time', 1200); // 20 minutes
ini_set('memory_limit', '512M'); // Increase memory limit

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'utils.php';
require_once 'CNPUpdater.php';

// Check if the current IP is allowed to run imports
checkIPAccess();

// Create CNP Updater instance
$updater = new CNPUpdater();

// Process action if specified
$message = '';
$messageType = '';
$reportData = null;

if (isset($_POST['action'])) {
    $action = $_POST['action'];

    switch ($action) {
        case 'backup':
            $result = $updater->createWorkloadBackup();
            $message = $result['message'];
            $messageType = $result['status'];
            break;
        case 'update':
            $result = $updater->updateCNPs();
            $message = $result['message'];
            $messageType = $result['status'];
            break;
        case 'restore':
            $result = $updater->restoreWorkloadFromBackup();
            $message = $result['message'];
            $messageType = $result['status'];
            break;
        case 'report':
            $result = $updater->generateCNPChangeReport();
            $message = $result['message'];
            $messageType = $result['status'];
            if ($result['status'] === 'success') {
                $reportData = $result;
            }
            break;
        case 'export':
            $report = $updater->generateCNPChangeReport();
            $result = $updater->exportToCsv($report);
            $message = $result['message'];
            $messageType = $result['status'];
            if ($result['status'] === 'success') {
                // Redirect to the file for download
                header('Location: ../' . $result['file']);
                exit;
            }
            break;
    }
}

// Generate report for display
if (!$reportData) {
    $reportResult = $updater->generateCNPChangeReport();
    if ($reportResult['status'] === 'success') {
        $reportData = $reportResult;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>CNP Updater Tool</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/app.min.css">
    <link rel="stylesheet" href="../vendor/fontawesome-free/css/all.min.css">
    <style>
        .import-card {
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .import-card:hover {
            transform: translateY(-5px);
        }
        .import-header {
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <header class="import-header bg-primary">
        <div class="container">
            <h1><i class="fas fa-sync"></i> CNP workload Updater</h1>
        </div>
    </header>
    <div class="container">
        <div class="card import-card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-info-circle"></i> Despre acest instrument</h3>
            </div>
            <div class="card-body">
                <p>Acest instrument permite actualizarea CNP-urilor in tabela workload atunci cand acestea s-au modificat in tabela experti_tehnici in urma unui import.</p>
                <p>Procesul va crea automat un backup al tabelei workload inainte de a face modificari.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> <strong>Atentie la CNP-urile ciclice!</strong> Unii experti pot avea CNP-uri care s-au schimbat inainte si inapoi (ciclice). In acest caz, sistemul va folosi cel mai recent CNP din baza de date.
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?>">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo $message; ?>
                        <?php if (isset($result['errors']) && !empty($result['errors'])): ?>
                            <ul>
                                <?php foreach ($result['errors'] as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card import-card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-cogs"></i> Actiuni disponibile</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <form method="post">
                            <input type="hidden" name="action" value="backup">
                            <button type="submit" class="btn btn-info btn-block w-100 mb-3">
                                <i class="fas fa-save"></i> Creaza backup workload
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="post">
                            <input type="hidden" name="action" value="report">
                            <button type="submit" class="btn btn-secondary btn-block w-100 mb-3">
                                <i class="fas fa-chart-bar"></i> Genereaza raport
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="post">
                            <input type="hidden" name="action" value="update">
                            <button type="submit" class="btn btn-success btn-block w-100 mb-3" onclick="return confirm('Sunteti sigur ca doriti sa actualizati CNP-urile in tabela workload?')">
                                <i class="fas fa-sync"></i> Actualizeaza CNP-uri
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="post">
                            <input type="hidden" name="action" value="restore">
                            <button type="submit" class="btn btn-warning btn-block w-100 mb-3" onclick="return confirm('Sunteti sigur ca doriti sa restaurati tabela workload din backup?')">
                                <i class="fas fa-undo"></i> Restaureaza din backup
                            </button>
                        </form>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Inapoi la Import Tools
                    </a>
                    <form method="post" class="d-inline-block">
                        <input type="hidden" name="action" value="export">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-export"></i> Exporta raport CSV
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <?php if ($reportData && isset($reportData['experts']) && !empty($reportData['experts'])): ?>
            <div class="card import-card">
                <div class="card-header bg-warning text-dark">
                    <h3><i class="fas fa-list"></i> Experti cu CNP-uri modificate (<?php echo count($reportData['experts']); ?>)
                    <?php if (isset($reportData['cyclic_count']) && $reportData['cyclic_count'] > 0): ?>
                        <span class="badge bg-danger ms-2">Atentie: <?php echo $reportData['cyclic_count']; ?> experti cu CNP-uri ciclice</span>
                    <?php endif; ?>
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Nume</th>
                                    <th>Prenume</th>
                                    <th>Judet</th>
                                    <th>CNP Vechi</th>
                                    <th>CNP Nou</th>
                                    <th>In Workload</th>
                                    <th>Incarcatura</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData['experts'] as $expert): ?>
                                    <tr <?php if (isset($expert['has_cyclic_cnp']) && $expert['has_cyclic_cnp']): ?>class="table-danger"<?php endif; ?>>
                                        <td><?php echo $expert['current_id']; ?></td>
                                        <td><?php echo htmlspecialchars($expert['nume']); ?></td>
                                        <td><?php echo htmlspecialchars($expert['prenume']); ?></td>
                                        <td><?php echo htmlspecialchars($expert['judet']); ?></td>
                                        <td>
                                            <?php echo htmlspecialchars($expert['old_cnp']); ?>
                                            <?php if (isset($expert['has_cyclic_cnp']) && $expert['has_cyclic_cnp']): ?>
                                                <span class="badge bg-warning text-dark" title="CNP ciclic">C</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($expert['current_cnp']); ?>
                                            <?php if (isset($expert['has_cyclic_cnp']) && $expert['has_cyclic_cnp']): ?>
                                                <span class="badge bg-success" title="CNP curent (cel mai recent)">Actual</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($expert['in_workload']): ?>
                                                <span class="badge bg-success">Da</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Nu</span>
                                            <?php endif; ?>
                                            <?php if ($expert['new_cnp_in_workload'] ?? false): ?>
                                                <span class="badge bg-info">CNP nou exista deja</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($expert['in_workload']): ?>
                                                ETJ: <?php echo $expert['incarcatura_ETJ']; ?><br>
                                                APP: <?php echo $expert['incarcatura_app']; ?><br>
                                                Total: <?php echo $expert['total']; ?>

                                                <?php if ($expert['new_cnp_in_workload'] ?? false): ?>
                                                    <hr>
                                                    ETJ: <?php echo $expert['new_incarcatura_ETJ']; ?><br>
                                                    APP: <?php echo $expert['new_incarcatura_app']; ?><br>
                                                    Total: <?php echo $expert['new_total']; ?>
                                                <?php endif; ?>

                                                <?php if (isset($expert['all_cnp_workloads']) && !empty($expert['all_cnp_workloads'])): ?>
                                                    <hr>
                                                    <strong>Alte CNP-uri:</strong><br>
                                                    <?php foreach ($expert['all_cnp_workloads'] as $cnp => $workload): ?>
                                                        <small><?php echo substr($cnp, 0, 6); ?>...<?php echo substr($cnp, -4); ?>:</small><br>
                                                        <small>ETJ: <?php echo $workload['incarcatura_ETJ']; ?>,
                                                        APP: <?php echo $workload['incarcatura_app']; ?>,
                                                        Total: <?php echo $workload['total']; ?></small><br>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php elseif ($reportData): ?>
            <div class="card import-card">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-info-circle"></i> Rezultat</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Nu s-au gasit experti cu CNP-uri modificate.
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add some simple animations with vanilla JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on page load
            const cards = document.querySelectorAll('.import-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'opacity 1.5s ease, transform 1.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 * index);
            });
        });
    </script>
</body>
</html>
