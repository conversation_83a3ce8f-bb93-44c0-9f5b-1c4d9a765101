<?php
/**
 * Utility functions for import scripts
 */

/**
 * Check if the current IP is allowed to run imports
 * Allows only localhost (127.0.0.1) and IPs in the 10.1.4.* range
 * 
 * @return bool True if the IP is allowed, false otherwise
 */
function isAllowedIP() {
    $ip = $_SERVER['REMOTE_ADDR'];
    
    // Allow localhost
    if ($ip === '127.0.0.1' || $ip === '::1') {
        return true;
    }
    
    // Allow IPs in the 10.1.4.* range
    if (preg_match('/^10\.1\.4\.\d+$/', $ip)) {
        return true;
    }
    
    return false;
}

/**
 * Check if the current IP is allowed to run imports and exit if not
 * 
 * @param bool $jsonResponse Whether to return a JSON response (true) or HTML (false)
 */
function checkIPAccess($jsonResponse = false) {
    if (!isAllowedIP()) {
        if ($jsonResponse) {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => 'Access denied. Imports can only be run from localhost or the 10.1.4.* network.'
            ]);
        } else {
            echo '<div style="color: red; font-weight: bold; padding: 20px; border: 1px solid red; margin: 20px;">
                Access denied. Imports can only be run from localhost or the 10.1.4.* network.
            </div>';
        }
        exit;
    }
}
