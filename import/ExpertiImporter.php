<?php
// Increase PHP execution time limit
ini_set('max_execution_time', 600); // 10 minutes
ini_set('memory_limit', '512M'); // Increase memory limit

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'utils.php';
require_once 'DataImporter.php';

// Check if the current IP is allowed to run imports
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    checkIPAccess(true);
}

/**
 * Class for importing experts from the old database
 */
class ExpertiImporter extends DataImporter
{
    /**
     * Create backup of experts table
     * This is a separate function to ensure it runs even if the table structure is already correct
     */
    public function createBackupTable()
    {
        try {
            // Verifică dacă tabela principală există
            $mainTableExists = false;
            $stmt = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'experti_tehnici'");
            if ($stmt) {
                $mainTableExists = $stmt->rowCount() > 0;
            }

            if (!$mainTableExists) {
                echo "Main table exp_jud.experti_tehnici does not exist. Cannot create backup.\n";
                return false;
            }

            // Creează tabela de backup
            echo "Starting backup creation...\n";

            // Pas 1: Șterge tabela de backup dacă există
            echo "Step 1: Dropping existing backup table if it exists...\n";
            $this->dbConnection->exec("DROP TABLE IF EXISTS exp_jud.experti_tehnici_backup");

            // Pas 2: Creează tabela de backup cu aceeași structură
            echo "Step 2: Creating backup table with same structure...\n";
            $this->dbConnection->exec("CREATE TABLE exp_jud.experti_tehnici_backup LIKE exp_jud.experti_tehnici");

            // Pas 3: Copiază datele în tabela de backup
            echo "Step 3: Copying data to backup table...\n";
            $this->dbConnection->exec("INSERT INTO exp_jud.experti_tehnici_backup SELECT * FROM exp_jud.experti_tehnici");

            // Pas 4: Verifică dacă tabela de backup a fost creată cu succes
            $backupExists = $this->dbConnection->query("SHOW TABLES FROM exp_jud LIKE 'experti_tehnici_backup'")->rowCount() > 0;
            if ($backupExists) {
                $count = $this->dbConnection->query("SELECT COUNT(*) FROM exp_jud.experti_tehnici_backup")->fetchColumn();
                echo "Backup created successfully with $count records.\n";
            } else {
                echo "Failed to create backup table.\n";
                return false;
            }

            echo "Backup process completed.\n";
            return true;
        } catch (Exception $e) {
            echo "Error creating backup table: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Create the experts table
     */
    protected function createTable()
    {
        // Verifică dacă tabela există deja
        $tableExists = $this->dbConnection->query("SHOW TABLES LIKE 'exp_jud.experti_tehnici'")->rowCount() > 0;

        if (!$tableExists) {
            // Creează tabela dacă nu există
            $sql = "CREATE TABLE IF NOT EXISTS exp_jud.experti_tehnici (
                `id` int NOT NULL AUTO_INCREMENT,
                `nume` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `prenume` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `cnp` varchar(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `specializari` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
                `subspecializari` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
                `id_judet` int DEFAULT NULL,
                `id_localitate` int DEFAULT NULL,
                `telefon` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `telefon2` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `legitimatie` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `adresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `status` enum('active','inactive','modified') DEFAULT 'active',
                PRIMARY KEY (`id`),
                UNIQUE KEY `idx_cnp` (`cnp`),
                KEY `idx_judet` (`id_judet`)
            ) ENGINE=InnoDB AUTO_INCREMENT=7083 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

            $this->dbConnection->exec($sql);
            echo "Created new experts table.\n";
        } else {

            // Verifică și adaugă coloanele necesare
            try {
                // Verifică dacă există coloana status
                $statusExists = false;
                $stmt = $this->dbConnection->query("SHOW COLUMNS FROM exp_jud.experti_tehnici LIKE 'status'");
                if ($stmt) {
                    $statusExists = $stmt->rowCount() > 0;
                }

                if (!$statusExists) {
                    $this->dbConnection->exec("ALTER TABLE exp_jud.experti_tehnici ADD COLUMN status enum('active','inactive','modified') DEFAULT 'active'");
                    echo "Added status column to experts table.\n";
                }

                // Verifică dacă există coloana updated_at
                $updatedAtExists = false;
                $stmt = $this->dbConnection->query("SHOW COLUMNS FROM exp_jud.experti_tehnici LIKE 'updated_at'");
                if ($stmt) {
                    $updatedAtExists = $stmt->rowCount() > 0;
                }

                if (!$updatedAtExists) {
                    $this->dbConnection->exec("ALTER TABLE exp_jud.experti_tehnici ADD COLUMN updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                    echo "Added updated_at column to experts table.\n";
                }

                // Verifică dacă există coloana email
                $emailExists = false;
                $stmt = $this->dbConnection->query("SHOW COLUMNS FROM exp_jud.experti_tehnici LIKE 'email'");
                if ($stmt) {
                    $emailExists = $stmt->rowCount() > 0;
                }

                if (!$emailExists) {
                    $this->dbConnection->exec("ALTER TABLE exp_jud.experti_tehnici ADD COLUMN email varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL AFTER adresa");
                    echo "Added email column to experts table.\n";
                }

                // Marchează toate înregistrările ca inactive înainte de import
                if ($statusExists) {
                    $this->dbConnection->exec("UPDATE exp_jud.experti_tehnici SET status = 'inactive'");
                    echo "Marked all existing experts as inactive.\n";
                }
            } catch (Exception $e) {
                echo "Error updating table structure: " . $e->getMessage() . "\n";
                // Continuă chiar dacă actualizarea structurii eșuează
            }

            // Verifică dacă idx_cnp este UNIQUE
            try {
                $indexInfo = [];
                $stmt = $this->dbConnection->query("SHOW INDEX FROM exp_jud.experti_tehnici WHERE Key_name = 'idx_cnp'");
                if ($stmt) {
                    $indexInfo = $stmt->fetchAll(PDO::FETCH_ASSOC);
                }

                $isUnique = false;
                foreach ($indexInfo as $index) {
                    if ($index['Non_unique'] == 0) {
                        $isUnique = true;
                        break;
                    }
                }

                if (!$isUnique && !empty($indexInfo)) {
                    // Șterge indexul existent și creează unul unic
                    $this->dbConnection->exec("ALTER TABLE exp_jud.experti_tehnici DROP INDEX idx_cnp");
                    $this->dbConnection->exec("ALTER TABLE exp_jud.experti_tehnici ADD UNIQUE INDEX idx_cnp (cnp)");
                    echo "Updated CNP index to be unique.\n";
                } else if (empty($indexInfo)) {
                    // Nu există niciun index, creează unul nou
                    $this->dbConnection->exec("ALTER TABLE exp_jud.experti_tehnici ADD UNIQUE INDEX idx_cnp (cnp)");
                    echo "Created new unique CNP index.\n";
                }
            } catch (Exception $e) {
                echo "Error updating CNP index: " . $e->getMessage() . "\n";
                // Continuă chiar dacă actualizarea indexului eșuează
            }
        }
    }

    /**
     * Import experts data
     * @return int Number of imported experts
     */
    protected function importData()
    {
        // Step 1: Get all experts basic data
        echo "Fetching basic expert data...\n";
        $expertsData = $this->fetchExpertsData();
        echo "Found " . count($expertsData) . " experts.\n";

        // Step 2: Get all specializations
        echo "Fetching specializations data...\n";
        $specializationsData = $this->fetchSpecializationsData();
        echo "Found specializations for " . count($specializationsData) . " experts.\n";

        // Step 3: Process and merge data
        echo "Processing and merging data...\n";
        $processedData = $this->processExpertsData($expertsData, $specializationsData);
        echo "Processed " . count($processedData) . " experts.\n";

        // Step 4: Insert into database
        echo "Inserting data into database...\n";
        return $this->insertProcessedData($processedData);
    }

    /**
     * Get basic expert data query
     * This query retrieves only the basic expert information
     */
    private function getExpertiQuery()
    {
        return "SELECT DISTINCT
            s.id,
            s.cnp,
            s.nume,
            s.prenume,
            s.adresa,
            s.telefon,
            s.telefon2,
            s.nr_dosar,
            s.nr_ordine,
            CASE
                WHEN s.nr_dosar IS NOT NULL AND s.nr_ordine IS NOT NULL THEN CAST(s.nr_dosar AS VARCHAR(50)) + '-' + CAST(s.nr_ordine AS VARCHAR(50))
                WHEN s.nr_dosar IS NOT NULL THEN CAST(s.nr_dosar AS VARCHAR(50)) + '-?'
                WHEN s.nr_ordine IS NOT NULL THEN '?-' + CAST(s.nr_ordine AS VARCHAR(50))
                ELSE NULL
            END AS legitimatie_compusa,
            s.id_localitate,
            j.id AS id_judet,
            s.email
        FROM [dbo].Specialist s WITH (NOLOCK)
        LEFT JOIN [dbo].NJudet j WITH (NOLOCK) ON s.id_judet = j.id
        LEFT JOIN [dbo].SituatieSpecialist sit WITH (NOLOCK) ON s.id_situatie = sit.id
        WHERE sit.activ = 1
          AND s.cnp IS NOT NULL
          AND s.cnp <> '*'
          AND LEN(s.cnp) = 13";
    }

    /**
     * Get specializations query
     * This query retrieves the specializations for all experts
     */
    private function getSpecializariQuery()
    {
        return "SELECT
            s.cnp,
            ss.id_specializare,
            ss.id_subspecializare
        FROM [dbo].Specialist s WITH (NOLOCK)
        LEFT JOIN [dbo].SituatieSpecialist sit WITH (NOLOCK) ON s.id_situatie = sit.id
        JOIN [dbo].SpecializareSpecialist ss WITH (NOLOCK) ON ss.id_specialist = s.id
        JOIN [dbo].NSpecializare sp WITH (NOLOCK) ON ss.id_specializare = sp.id
        WHERE sit.activ = 1
          AND s.cnp IS NOT NULL
          AND s.cnp <> '*'
          AND LEN(s.cnp) = 13
          AND sp.is_new = 1";
    }

    /**
     * Fetch all experts data from the database
     * @return array Associative array of experts data, keyed by CNP
     */
    private function fetchExpertsData()
    {
        $dataStmt = sqlsrv_query($this->oldConnection, $this->getExpertiQuery(), [], ['QueryTimeout' => 300]);
        if ($dataStmt === false) {
            throw new Exception(print_r(sqlsrv_errors(), true));
        }

        $experts = [];
        $duplicateCount = 0;
        $invalidCnpCount = 0;

        while ($row = sqlsrv_fetch_array($dataStmt, SQLSRV_FETCH_ASSOC)) {
            $cnp = isset($row['cnp']) && $row['cnp'] !== null ? trim($row['cnp']) : '';

            // Skip invalid CNPs
            if (empty($cnp) || $cnp == '*' || strlen($cnp) != 13 || !is_numeric($cnp)) {
                $invalidCnpCount++;
                continue;
            }

            // If we already have this expert, merge the data
            if (isset($experts[$cnp])) {
                $duplicateCount++;

                // Merge telefon
                if (!empty($row['telefon']) && $row['telefon'] != $experts[$cnp]['telefon']) {
                    $telefon = isset($row['telefon']) && $row['telefon'] !== null ? trim($row['telefon']) : '';
                    if (!empty($telefon) && !strstr($experts[$cnp]['telefon'], $telefon)) {
                        $experts[$cnp]['telefon'] = $experts[$cnp]['telefon']
                            ? $experts[$cnp]['telefon'] . ',' . $telefon
                            : $telefon;
                    }
                }

                // Merge telefon2
                if (!empty($row['telefon2']) && $row['telefon2'] != $experts[$cnp]['telefon2']) {
                    $telefon2 = isset($row['telefon2']) && $row['telefon2'] !== null ? trim($row['telefon2']) : '';
                    if (!empty($telefon2) && !strstr($experts[$cnp]['telefon2'], $telefon2)) {
                        $experts[$cnp]['telefon2'] = $experts[$cnp]['telefon2']
                            ? $experts[$cnp]['telefon2'] . ',' . $telefon2
                            : $telefon2;
                    }
                }

                // Update email if it's empty and the new record has one
                $email = isset($row['email']) && $row['email'] !== null ? trim($row['email']) : '';
                if (empty($experts[$cnp]['email']) && !empty($email)) {
                    $experts[$cnp]['email'] = $email;
                }

                // If email is still empty, try to extract it from address
                if (empty($experts[$cnp]['email'])) {
                    $adresa = isset($row['adresa']) && $row['adresa'] !== null ? trim($row['adresa']) : '';
                    if (!empty($adresa) && preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}/', $adresa, $matches)) {
                        $experts[$cnp]['email'] = $matches[0];
                    }
                }

                // Merge legitimatie using the pre-computed field
                if (!empty($row['legitimatie_compusa'])) {
                    $legitimatie = trim($row['legitimatie_compusa']);

                    // Check if this legitimation is already in the list - use exact matching
                    $existingLegitimatii = explode(',', $experts[$cnp]['legitimatie']);
                    $found = false;
                    foreach ($existingLegitimatii as $existingLegitimatie) {
                        if (trim($existingLegitimatie) === $legitimatie) {
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        $experts[$cnp]['legitimatie'] = $experts[$cnp]['legitimatie']
                            ? $experts[$cnp]['legitimatie'] . ',' . $legitimatie
                            : $legitimatie;
                    }
                }
            } else {
                // Extract email from address if not present in email field
                $email = isset($row['email']) && $row['email'] !== null ? trim($row['email']) : '';
                $adresa = isset($row['adresa']) && $row['adresa'] !== null ? trim($row['adresa']) : '';

                // If email is empty, try to extract it from address
                if (empty($email) && !empty($adresa)) {
                    if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}/', $adresa, $matches)) {
                        $email = $matches[0];
                    }
                }

                // Create a new expert entry
                $experts[$cnp] = [
                    'cnp' => $cnp,
                    'nume' => isset($row['nume']) && $row['nume'] !== null ? trim($row['nume']) : '',
                    'prenume' => isset($row['prenume']) && $row['prenume'] !== null ? trim($row['prenume']) : '',
                    'adresa' => $adresa,
                    'email' => $email,
                    'telefon' => isset($row['telefon']) && $row['telefon'] !== null ? trim($row['telefon']) : '',
                    'telefon2' => isset($row['telefon2']) && $row['telefon2'] !== null ? trim($row['telefon2']) : '',
                    'id_judet' => $row['id_judet'],
                    'id_localitate' => $row['id_localitate'],
                    'legitimatie' => isset($row['legitimatie_compusa']) && $row['legitimatie_compusa'] !== null ? trim($row['legitimatie_compusa']) : '',
                    'specializari' => '',
                    'subspecializari' => ''
                ];
            }
        }

        echo "Found $duplicateCount duplicate expert records (merged)\n";
        echo "Skipped $invalidCnpCount invalid CNP records\n";

        sqlsrv_free_stmt($dataStmt);
        return $experts;
    }

    /**
     * Fetch all specializations data from the database
     * @return array Associative array of specializations data, keyed by CNP
     */
    private function fetchSpecializationsData()
    {
        $dataStmt = sqlsrv_query($this->oldConnection, $this->getSpecializariQuery(), [], ['QueryTimeout' => 300]);
        if ($dataStmt === false) {
            throw new Exception(print_r(sqlsrv_errors(), true));
        }

        $specializations = [];
        $uniquePairs = []; // Track unique specialization-subspecialization pairs per CNP

        while ($row = sqlsrv_fetch_array($dataStmt, SQLSRV_FETCH_ASSOC)) {
            $cnp = isset($row['cnp']) && $row['cnp'] !== null ? trim($row['cnp']) : '';

            // Skip invalid CNPs
            if (empty($cnp) || $cnp == '*' || strlen($cnp) != 13 || !is_numeric($cnp)) {
                continue;
            }

            if (!isset($specializations[$cnp])) {
                $specializations[$cnp] = [];
                $uniquePairs[$cnp] = [];
            }

            // Ensure we have valid values for specializare and subspecializare
            $specializare = isset($row['id_specializare']) && !empty($row['id_specializare']) ? $row['id_specializare'] : 0;
            $subspecializare = isset($row['id_subspecializare']) && !empty($row['id_subspecializare']) ? $row['id_subspecializare'] : 0;

            // Skip if specializare is 0 or empty
            if ($specializare === 0 || empty($specializare)) {
                continue;
            }

            // Create a unique key for this specialization-subspecialization pair
            $key = $specializare . '-' . $subspecializare;

            // Only add if we haven't seen this pair for this CNP before
            if (!isset($uniquePairs[$cnp][$key])) {
                $specializations[$cnp][] = [
                    'id_specializare' => $specializare,
                    'id_subspecializare' => $subspecializare
                ];

                $uniquePairs[$cnp][$key] = true;
            }
        }

        sqlsrv_free_stmt($dataStmt);
        return $specializations;
    }

    /**
     * Process and merge experts data with their specializations
     * @param array $expertsData Experts data from fetchExpertsData()
     * @param array $specializationsData Specializations data from fetchSpecializationsData()
     * @return array Processed data ready for insertion
     */
    private function processExpertsData($expertsData, $specializationsData)
    {
        $processedData = [];
        $processedCnps = []; // Track processed CNPs to avoid duplicates
        $duplicateCount = 0;

        foreach ($expertsData as $cnp => $expert) {
            // Skip experts without CNP
            if (empty($cnp)) {
                echo "Skipping expert with empty CNP\n";
                continue;
            }

            // Check for duplicate CNP
            if (in_array($cnp, $processedCnps)) {
                $duplicateCount++;
                echo "Skipping duplicate CNP: $cnp (Total duplicates: $duplicateCount)\n";
                continue;
            }

            // Clean up phone numbers - remove duplicates and empty values
            $expert['telefon'] = $this->cleanPhoneNumbers($expert['telefon']);
            $expert['telefon2'] = $this->cleanPhoneNumbers($expert['telefon2']);

            // Make sure phone numbers don't overlap
            $expert = $this->deduplicatePhoneNumbers($expert);

            // Clean up legitimatie field
            $expert['legitimatie'] = $this->cleanLegitimatie($expert['legitimatie']);

            // Clean up address field and extract email only if email is not already set
            if (empty($expert['email'])) {
                $email = '';
                $expert['adresa'] = $this->cleanAddress($expert['adresa'], $email);
                $expert['email'] = $email;
            } else {
                // Just clean the address without extracting email
                $expert['adresa'] = $this->cleanAddress($expert['adresa']);
            }

            // Process specializations if available
            if (isset($specializationsData[$cnp])) {
                $specializari = [];
                $subspecializari = [];

                // Get unique specialization-subspecialization pairs
                $uniquePairs = [];
                foreach ($specializationsData[$cnp] as $spec) {
                    $key = $spec['id_specializare'] . '-' . $spec['id_subspecializare'];
                    if (!isset($uniquePairs[$key])) {
                        $uniquePairs[$key] = $spec;
                    }
                }

                // Build the specializari and subspecializari strings
                foreach ($uniquePairs as $spec) {
                    $specializari[] = $spec['id_specializare'];
                    // Ensure we have a valid subspecialization, default to 0 if empty
                    $subspecializari[] = !empty($spec['id_subspecializare']) ? $spec['id_subspecializare'] : '0';
                }

                $expert['specializari'] = implode(',', $specializari);
                $expert['subspecializari'] = implode(',', $subspecializari);
            }

            $processedData[] = $expert;
            $processedCnps[] = $cnp; // Mark this CNP as processed
        }

        echo "Found and skipped $duplicateCount duplicate CNPs\n";
        return $processedData;
    }

    /**
     * Insert processed data into the database
     * @param array $processedData Processed data from processExpertsData()
     * @return int Number of inserted records
     */
    private function insertProcessedData($processedData)
    {
        // Verifică dacă există coloana status
        $statusExists = $this->dbConnection->query("SHOW COLUMNS FROM exp_jud.experti_tehnici LIKE 'status'")->rowCount() > 0;

        // Verifică dacă există coloana email
        $emailExists = $this->dbConnection->query("SHOW COLUMNS FROM exp_jud.experti_tehnici LIKE 'email'")->rowCount() > 0;

        // Construiește SQL-ul de inserare în funcție de existența coloanelor status și email
        if ($statusExists && $emailExists) {
            $insertSQL = "INSERT INTO exp_jud.experti_tehnici
                            (cnp,prenume,nume,id_judet,specializari,subspecializari,adresa,email,id_localitate,telefon,telefon2,legitimatie,status)
                          VALUES ";
        } else if ($statusExists) {
            $insertSQL = "INSERT INTO exp_jud.experti_tehnici
                            (cnp,prenume,nume,id_judet,specializari,subspecializari,adresa,id_localitate,telefon,telefon2,legitimatie,status)
                          VALUES ";
        } else if ($emailExists) {
            $insertSQL = "INSERT INTO exp_jud.experti_tehnici
                            (cnp,prenume,nume,id_judet,specializari,subspecializari,adresa,email,id_localitate,telefon,telefon2,legitimatie)
                          VALUES ";
        } else {
            $insertSQL = "INSERT INTO exp_jud.experti_tehnici
                            (cnp,prenume,nume,id_judet,specializari,subspecializari,adresa,id_localitate,telefon,telefon2,legitimatie)
                          VALUES ";
        }

        // SQL pentru actualizarea înregistrărilor existente
        if ($statusExists && $emailExists) {
            $updateSQL = " ON DUPLICATE KEY UPDATE
                            prenume = VALUES(prenume),
                            nume = VALUES(nume),
                            id_judet = VALUES(id_judet),
                            specializari = VALUES(specializari),
                            subspecializari = VALUES(subspecializari),
                            adresa = VALUES(adresa),
                            email = VALUES(email),
                            id_localitate = VALUES(id_localitate),
                            telefon = VALUES(telefon),
                            telefon2 = VALUES(telefon2),
                            legitimatie = VALUES(legitimatie),
                            status = 'active',
                            updated_at = CURRENT_TIMESTAMP";
        } else if ($statusExists) {
            $updateSQL = " ON DUPLICATE KEY UPDATE
                            prenume = VALUES(prenume),
                            nume = VALUES(nume),
                            id_judet = VALUES(id_judet),
                            specializari = VALUES(specializari),
                            subspecializari = VALUES(subspecializari),
                            adresa = VALUES(adresa),
                            id_localitate = VALUES(id_localitate),
                            telefon = VALUES(telefon),
                            telefon2 = VALUES(telefon2),
                            legitimatie = VALUES(legitimatie),
                            status = 'active',
                            updated_at = CURRENT_TIMESTAMP";
        } else if ($emailExists) {
            $updateSQL = " ON DUPLICATE KEY UPDATE
                            prenume = VALUES(prenume),
                            nume = VALUES(nume),
                            id_judet = VALUES(id_judet),
                            specializari = VALUES(specializari),
                            subspecializari = VALUES(subspecializari),
                            adresa = VALUES(adresa),
                            email = VALUES(email),
                            id_localitate = VALUES(id_localitate),
                            telefon = VALUES(telefon),
                            telefon2 = VALUES(telefon2),
                            legitimatie = VALUES(legitimatie)";
        } else {
            $updateSQL = " ON DUPLICATE KEY UPDATE
                            prenume = VALUES(prenume),
                            nume = VALUES(nume),
                            id_judet = VALUES(id_judet),
                            specializari = VALUES(specializari),
                            subspecializari = VALUES(subspecializari),
                            adresa = VALUES(adresa),
                            id_localitate = VALUES(id_localitate),
                            telefon = VALUES(telefon),
                            telefon2 = VALUES(telefon2),
                            legitimatie = VALUES(legitimatie)";
        }

        $values = [];
        $insertCount = 0;
        $totalInserted = 0;
        $errorCount = 0;
        $batchSize = 100; // Batch size mai mic pentru o mai bună gestionare a erorilor
        $startTime = microtime(true);
        $lastReportTime = $startTime;

        $this->dbConnection->setAttribute(PDO::ATTR_AUTOCOMMIT, false);
        $this->dbConnection->beginTransaction();

        // Urmărește CNP-urile pentru a evita duplicatele
        $processedCnps = [];
        $newRecords = 0;
        $updatedRecords = 0;

        foreach ($processedData as $expert) {
            $cnp = $expert['cnp'];

            // Skip dacă am procesat deja acest CNP
            if (in_array($cnp, $processedCnps)) {
                echo "Skipping duplicate CNP during insert: $cnp\n";
                continue;
            }

            // Validează CNP
            if (empty($cnp) || strlen($cnp) != 13 || !is_numeric($cnp)) {
                echo "Skipping invalid CNP during insert: $cnp\n";
                continue;
            }

            if ($statusExists && $emailExists) {
                $rowValues = [
                    $this->dbConnection->quote($cnp),
                    $this->dbConnection->quote($expert['prenume'] ?? ''),
                    $this->dbConnection->quote($expert['nume'] ?? ''),
                    $expert['id_judet'] === null ? "NULL" : $this->dbConnection->quote($expert['id_judet']),
                    $this->dbConnection->quote($expert['specializari'] ?? ''),
                    $this->dbConnection->quote($expert['subspecializari'] ?? ''),
                    $this->dbConnection->quote($expert['adresa'] ?? ''),
                    $this->dbConnection->quote($expert['email'] ?? ''),
                    $expert['id_localitate'] === null ? "NULL" : $this->dbConnection->quote($expert['id_localitate']),
                    $this->dbConnection->quote($expert['telefon'] ?? ''),
                    $this->dbConnection->quote($expert['telefon2'] ?? ''),
                    $this->dbConnection->quote($expert['legitimatie'] ?? ''),
                    $this->dbConnection->quote('active')
                ];
            } else if ($statusExists) {
                $rowValues = [
                    $this->dbConnection->quote($cnp),
                    $this->dbConnection->quote($expert['prenume'] ?? ''),
                    $this->dbConnection->quote($expert['nume'] ?? ''),
                    $expert['id_judet'] === null ? "NULL" : $this->dbConnection->quote($expert['id_judet']),
                    $this->dbConnection->quote($expert['specializari'] ?? ''),
                    $this->dbConnection->quote($expert['subspecializari'] ?? ''),
                    $this->dbConnection->quote($expert['adresa'] ?? ''),
                    $expert['id_localitate'] === null ? "NULL" : $this->dbConnection->quote($expert['id_localitate']),
                    $this->dbConnection->quote($expert['telefon'] ?? ''),
                    $this->dbConnection->quote($expert['telefon2'] ?? ''),
                    $this->dbConnection->quote($expert['legitimatie'] ?? ''),
                    $this->dbConnection->quote('active')
                ];
            } else if ($emailExists) {
                $rowValues = [
                    $this->dbConnection->quote($cnp),
                    $this->dbConnection->quote($expert['prenume'] ?? ''),
                    $this->dbConnection->quote($expert['nume'] ?? ''),
                    $expert['id_judet'] === null ? "NULL" : $this->dbConnection->quote($expert['id_judet']),
                    $this->dbConnection->quote($expert['specializari'] ?? ''),
                    $this->dbConnection->quote($expert['subspecializari'] ?? ''),
                    $this->dbConnection->quote($expert['adresa'] ?? ''),
                    $this->dbConnection->quote($expert['email'] ?? ''),
                    $expert['id_localitate'] === null ? "NULL" : $this->dbConnection->quote($expert['id_localitate']),
                    $this->dbConnection->quote($expert['telefon'] ?? ''),
                    $this->dbConnection->quote($expert['telefon2'] ?? ''),
                    $this->dbConnection->quote($expert['legitimatie'] ?? '')
                ];
            } else {
                $rowValues = [
                    $this->dbConnection->quote($cnp),
                    $this->dbConnection->quote($expert['prenume'] ?? ''),
                    $this->dbConnection->quote($expert['nume'] ?? ''),
                    $expert['id_judet'] === null ? "NULL" : $this->dbConnection->quote($expert['id_judet']),
                    $this->dbConnection->quote($expert['specializari'] ?? ''),
                    $this->dbConnection->quote($expert['subspecializari'] ?? ''),
                    $this->dbConnection->quote($expert['adresa'] ?? ''),
                    $expert['id_localitate'] === null ? "NULL" : $this->dbConnection->quote($expert['id_localitate']),
                    $this->dbConnection->quote($expert['telefon'] ?? ''),
                    $this->dbConnection->quote($expert['telefon2'] ?? ''),
                    $this->dbConnection->quote($expert['legitimatie'] ?? '')
                ];
            }

            $values[] = "(" . implode(",", $rowValues) . ")";
            $processedCnps[] = $cnp;

            $insertCount++;
            if ($insertCount >= $batchSize) {
                try {
                    // Execută INSERT cu ON DUPLICATE KEY UPDATE
                    $sql = $insertSQL . implode(",", $values) . $updateSQL;
                    $stmt = $this->dbConnection->prepare($sql);
                    $stmt->execute();

                    // Calculează numărul de înregistrări noi vs. actualizate
                    $affectedRows = $stmt->rowCount();
                    $newInBatch = $insertCount - ($affectedRows / 2);
                    $updatedInBatch = $affectedRows / 2;

                    $newRecords += $newInBatch;
                    $updatedRecords += $updatedInBatch;
                    $totalInserted += $insertCount;

                    $this->dbConnection->commit();
                    $this->dbConnection->beginTransaction();

                    // Raportare progres
                    $currentTime = microtime(true);
                    $elapsedTime = round($currentTime - $startTime, 2);
                    ($elapsedTime > 0) ? $recordsPerSecond = round($totalInserted / $elapsedTime, 2) : $recordsPerSecond = 0;
                    echo "Processed $totalInserted records ($newRecords new, $updatedRecords updated) in $elapsedTime seconds ($recordsPerSecond records/sec)\n";
                    $lastReportTime = $currentTime;
                } catch (Exception $e) {
                    $errorCount++;
                    echo "Error during batch insert: " . $e->getMessage() . "\n";

                    // Încearcă inserarea una câte una pentru a identifica înregistrările problematice
                    foreach ($values as $value) {
                        try {
                            $singleSQL = $insertSQL . $value . $updateSQL;
                            $this->dbConnection->exec($singleSQL);
                            $totalInserted++;
                        } catch (Exception $innerE) {
                            echo "Error inserting record: " . $innerE->getMessage() . "\n";
                            echo "Problematic record: " . $value . "\n";
                        }
                    }

                    // Începe o nouă tranzacție
                    if ($this->dbConnection->inTransaction()) {
                        $this->dbConnection->rollBack();
                    }
                    $this->dbConnection->beginTransaction();
                }

                $values = [];
                $insertCount = 0;
                $lastReportTime = microtime(true);

                // Forțează colectarea gunoiului pentru a elibera memoria
                if ($totalInserted % 1000 == 0) {
                    gc_collect_cycles();
                }
            }
        }

        if (!empty($values)) {
            try {
                // Execută INSERT cu ON DUPLICATE KEY UPDATE pentru ultimele înregistrări
                $sql = $insertSQL . implode(",", $values) . $updateSQL;
                $stmt = $this->dbConnection->prepare($sql);
                $stmt->execute();

                // Calculează numărul de înregistrări noi vs. actualizate
                $affectedRows = $stmt->rowCount();
                $newInBatch = $insertCount - ($affectedRows / 2);
                $updatedInBatch = $affectedRows / 2;

                $newRecords += $newInBatch;
                $updatedRecords += $updatedInBatch;
                $totalInserted += $insertCount;
            } catch (Exception $e) {
                $errorCount++;
                echo "Error during final batch insert: " . $e->getMessage() . "\n";

                // Încearcă inserarea una câte una
                foreach ($values as $value) {
                    try {
                        $singleSQL = $insertSQL . $value . $updateSQL;
                        $this->dbConnection->exec($singleSQL);
                        $totalInserted++;
                    } catch (Exception $innerE) {
                        echo "Error inserting record: " . $innerE->getMessage() . "\n";
                    }
                }
            }
        }

        if ($this->dbConnection->inTransaction()) {
            $this->dbConnection->commit();
        }

        $this->dbConnection->setAttribute(PDO::ATTR_AUTOCOMMIT, true);

        // Marchează înregistrările care nu au fost actualizate ca fiind șterse (doar dacă există coloana status)
        $modifiedCount = 0;
        if ($statusExists) {
            $this->dbConnection->exec("UPDATE exp_jud.experti_tehnici SET status = 'modified' WHERE status = 'inactive'");
            $modifiedCount = $this->dbConnection->query("SELECT COUNT(*) FROM exp_jud.experti_tehnici WHERE status = 'modified'")->fetchColumn();
        }

        echo "Total records: $totalInserted ($newRecords new, $updatedRecords updated, $modifiedCount modified)\n";
        echo "Total errors during insert: $errorCount\n";
        return $totalInserted;
    }

    /**
     * Override the import method to create a backup table first and truncate the table
     * @return array Import result
     */
    public function import()
    {
        // Create backup table first - ALWAYS create a backup before import
        $backupResult = $this->createBackupTable();

        $this->getAllExperts();

        if (!$backupResult) {
            echo "WARNING: Failed to create backup table. Proceeding with import anyway, but this is risky.\n";
            echo "Consider cancelling the import and creating a backup first.\n";
        }

        // Always truncate the table before import
        try {
            $this->dbConnection->exec("TRUNCATE TABLE exp_jud.experti_tehnici");
            echo "Truncated experts table.\n";
        } catch (Exception $e) {
            echo "Error truncating experts table: " . $e->getMessage() . "\n";
        }

        // Call parent import method
        return parent::import();
    }

    private function getAllExperts()
    {
        $allExpertsSelect = "SELECT DISTINCT
            s.id id_etj, s.cnp, s.nume, s.prenume, s.adresa, s.telefon, s.telefon2, s.nr_dosar, s.nr_ordine,
            CASE
                WHEN s.nr_dosar IS NOT NULL AND s.nr_ordine IS NOT NULL THEN CAST(s.nr_dosar AS VARCHAR(50)) + '-' + CAST(s.nr_ordine AS VARCHAR(50))
                WHEN s.nr_dosar IS NOT NULL THEN CAST(s.nr_dosar AS VARCHAR(50)) + '-?'
                WHEN s.nr_ordine IS NOT NULL THEN '?-' + CAST(s.nr_ordine AS VARCHAR(50))
                ELSE NULL
            END AS legitimatie_compusa,
            s.id_localitate, j.id AS id_judet, s.email, ss.id_specializare, ss.id_subspecializare, sit.activ
        FROM [dbo].Specialist s WITH (NOLOCK)
        LEFT JOIN [dbo].NJudet j WITH (NOLOCK) ON s.id_judet = j.id
        LEFT JOIN [dbo].SituatieSpecialist sit WITH (NOLOCK) ON s.id_situatie = sit.id
		left join [dbo].[SpecializareSpecialist] ss with (NOLOCK) on ss.id_specialist = s.id
		where sit.activ != 1
		";
        $allExpertsSelect = sqlsrv_query($this->oldConnection, $allExpertsSelect, [], ['QueryTimeout' => 300]);
        if ($allExpertsSelect === false) {
            throw new Exception(print_r(sqlsrv_errors(), true));
        }

        $values = [];
        $params = [];
        while ($row = sqlsrv_fetch_array($allExpertsSelect, SQLSRV_FETCH_ASSOC)) {
            // Build placeholders for this row
            $rowPlaceholders = [];
            for ($i = 0; $i < 16; $i++) {
                $rowPlaceholders[] = '?';
            }
            $values[] = '(' . implode(',', $rowPlaceholders) . ')';

            // Add parameters for this row
            $params[] = $row['id_etj'];
            $params[] = $row['cnp'];
            $params[] = isset($row['nume']) && $row['nume'] !== null ? trim($row['nume']) : '';
            $params[] = isset($row['prenume']) && $row['prenume'] !== null ? trim($row['prenume']) : '';
            $params[] = $row['adresa'] ?? '';
            $params[] = isset($row['telefon']) && $row['telefon'] !== null ? trim($row['telefon']) : '';
            $params[] = isset($row['telefon2']) && $row['telefon2'] !== null ? trim($row['telefon2']) : '';
            $params[] = $row['nr_dosar'] ?? '';
            $params[] = $row['nr_ordine'] ?? '';
            $params[] = isset($row['legitimatie_compusa']) && $row['legitimatie_compusa'] !== null ? trim($row['legitimatie_compusa']) : '';
            $params[] = $row['id_localitate'] ?? 0;
            $params[] = $row['id_judet'] ?? 0;
            $params[] = $row['email'] ?? '';
            $params[] = $row['id_specializare'] ?? '';
            $params[] = $row['id_subspecializare'] ?? '';
            $params[] = $row['activ'] ?? '';
        }

        // Truncate table
        $this->dbConnection->exec("TRUNCATE TABLE exp_jud.experti_tehnici_inactivi");

        // Insert all data in one batch
        if (!empty($values)) {
            try {
                $sql = "INSERT INTO `exp_jud`.`experti_tehnici_inactivi` 
                (`id_etj`, `cnp`, `nume`, `prenume`, `adresa`, `telefon`, `telefon2`, `nr_dosar`, `nr_ordine`, `legitimatie_compusa`, `id_localitate`, `id_judet`, `email`, `id_specializare`, `id_subspecializare`, `activ`) 
                VALUES " . implode(',', $values);

                $stmt = $this->dbConnection->prepare($sql);
                $stmt->execute($params);

            } catch (Exception $e) {
                throw new Exception("Error inserting experts: " . $e->getMessage());
            }
        }

        sqlsrv_free_stmt($allExpertsSelect);
        return true;
    }
}

// Execute import if this file is called directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $importer = new ExpertiImporter();
    echo json_encode($importer->import());
}
