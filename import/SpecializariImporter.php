<?php
// Increase PHP execution time limit
ini_set('max_execution_time', 600); // 10 minutes
ini_set('memory_limit', '512M'); // Increase memory limit

require_once '../inc/cfg_db.php';
require_once '../inc/cfg_db_app_experti_old.php';
require_once 'utils.php';
require_once 'DataImporter.php';

// Check if the current IP is allowed to run imports
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    checkIPAccess(true);
}

/**
 * Class SpecializariImporter
 * Handles the import of specializations from the old database to the new one
 */
class SpecializariImporter extends DataImporter {
    // Constants
    private const TABLE_NAME = 'exp_jud.specializarisubspecializari';
    private const SPECIAL_CORRECTIONS = [
        ['field' => 'nume_specializare', 'old_value' => 'Horitcultură', 'new_value' => 'Horticultură', 'id_field' => 'id_specializare', 'id_value' => 228]
    ];

    /**
     * Create the specializations table
     */
    protected function createTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . self::TABLE_NAME . " (
            `id` int NOT NULL AUTO_INCREMENT,
            `id_specializare` int DEFAULT NULL,
            `nume_specializare` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
            `id_subspecializare` int DEFAULT NULL,
            `nume_subspecializare` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_specializare` (`id_specializare`),
            KEY `idx_subspecializare` (`id_subspecializare`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

        // Drop and recreate table for clean import
        $this->dbConnection->exec("DROP TABLE IF EXISTS " . self::TABLE_NAME);
        $this->dbConnection->exec($sql);
    }

    /**
     * Import specializations data
     * @return int Number of imported specializations
     */
    protected function importData() {
        // Get specializations query
        $query = "SELECT 
            s.id AS id_specializare, 
            s.nume AS nume_specializare,
            ss.id AS id_subspecializare,
            ss.nume AS nume_subspecializare
        FROM [dbo].[NSpecializare] s WITH (NOLOCK)
        LEFT JOIN [dbo].[NSubspecializare] ss WITH (NOLOCK) ON ss.id_specializare = s.id
        WHERE s.is_new = 1
        ORDER BY s.id, ss.id";

        // Execute query
        $stmt = sqlsrv_query($this->oldConnection, $query, [], ['QueryTimeout' => 300]);
        if ($stmt === false) {
            throw new Exception(print_r(sqlsrv_errors(), true));
        }

        // Prepare insert SQL
        $insertSQL = "INSERT INTO " . self::TABLE_NAME . " 
            (id_specializare, nume_specializare, id_subspecializare, nume_subspecializare) 
            VALUES ";

        // Process data
        $values = [];
        $insertCount = 0;
        $totalInserted = 0;
        $startTime = microtime(true);
        $lastReportTime = $startTime;

        // Disable autocommit for better performance
        $this->dbConnection->setAttribute(PDO::ATTR_AUTOCOMMIT, false);
        $this->dbConnection->beginTransaction();

        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // Apply special corrections
            foreach (self::SPECIAL_CORRECTIONS as $correction) {
                if ($row[$correction['id_field']] == $correction['id_value'] && $row[$correction['field']] == $correction['old_value']) {
                    $row[$correction['field']] = $correction['new_value'];
                }
            }

            // Fix encoding for text fields
            $row['nume_specializare'] = $this->fixEncoding($row['nume_specializare'] ?? '');
            $row['nume_subspecializare'] = $this->fixEncoding($row['nume_subspecializare'] ?? '');

            // Prepare values
            $rowValues = [
                $row['id_specializare'] === null ? "NULL" : $this->dbConnection->quote($row['id_specializare']),
                $this->dbConnection->quote($row['nume_specializare']),
                $row['id_subspecializare'] === null ? "NULL" : $this->dbConnection->quote($row['id_subspecializare']),
                $this->dbConnection->quote($row['nume_subspecializare'])
            ];

            $values[] = "(" . implode(",", $rowValues) . ")";

            $insertCount++;
            if ($insertCount >= $this->batchSize) {
                $this->executeBatch($insertSQL, $values, $insertCount, $totalInserted, $startTime, $lastReportTime);
                $values = [];
                $insertCount = 0;
                $lastReportTime = microtime(true);
            }
        }

        // Insert remaining records
        if (!empty($values)) {
            $this->executeBatch($insertSQL, $values, $insertCount, $totalInserted, $startTime, $lastReportTime);
        }

        // Re-enable autocommit
        $this->dbConnection->setAttribute(PDO::ATTR_AUTOCOMMIT, true);

        // Free statement
        sqlsrv_free_stmt($stmt);

        return $totalInserted;
    }
}

// Execute import if this file is called directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $importer = new SpecializariImporter();
    echo json_encode($importer->import());
}
