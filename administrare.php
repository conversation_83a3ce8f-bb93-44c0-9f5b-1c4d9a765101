<link rel="stylesheet" href="assets/css/jqueryDatatable.css">
<link href="vendor/select2/select2/dist/css/select2.min.css" rel="stylesheet"/>
<link rel="stylesheet" href="assets/css/buttons.dataTables.min.css">
<?php
require_once 'inc/cfg_head.php';
require_once 'inc/cfg_menu.php';
global $sesiune;
?>

<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Adăugare utilizator nou</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="utilizator" class="form-label">Utilizator</label>
                        <input type="text" class="form-control" id="utilizator" name="utilizator"
                               placeholder="Username domeniu just.ro" required>
                    </div>
                    <div class="mb-3">
                        <label for="idRol" class="form-label">Rol</label>
                        <select class="form-control select2" id="idRol" name="idRol" required>
                            <option value="">Selectează rolul</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="idInstanta" class="form-label">Instanță</label>
                        <select class="form-control select2" id="idInstanta" name="idInstanta" required>
                            <option value="">Selectează instanța</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="idSectie" class="form-label">Secție</label>
                        <select class="form-control select2" id="idSectie" required>
                            <option value="">Selectează secția</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                <button type="button" class="btn btn-primary" id="saveUser">Salvează</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">Editare utilizator</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="mb-3">
                        <label for="editUtilizator" class="form-label">Utilizator</label>
                        <input type="text" class="form-control" id="editUtilizator" name="editUtilizator" required>
                    </div>
                    <div class="mb-3">
                        <label for="editIdInstanta" class="form-label">Instanță</label>
                        <select class="form-control select2" id="editIdInstanta" name="editIdInstanta" required>
                            <option value="">Selectează instanța</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editIdSectie" class="form-label">Secție</label>
                        <div class="input-group">
                            <select class="form-control select2" id="editIdSectie" name="editIdSectie" required>
                                <option value="">Selectează secția</option>
                            </select>
                        </div>
                        <div class="form-text text-muted">
                            <small>
                                <i class="fa fa-info-circle"></i>
                                Nu ați definit încă nicio secție?
                                <a href="#" class="text-success" id="addSectieFromUserLink">
                                    Click pe butonul <i class="fa fa-plus"></i> pentru a adăuga
                                    <button type="button" class="btn btn-outline-success" id="addSectieFromUser">
                                        <i class="fa fa-plus"></i>
                                    </button>

                                </a>
                            </small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editIdRol" class="form-label">Rol</label>
                        <select class="form-control select2" id="editIdRol" name="editIdRol" required>
                            <option value="">Selectează rolul</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                <button type="button" class="btn btn-primary" id="saveEditUser">Salvează</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="manageSectiiModal" tabindex="-1" aria-labelledby="manageSectiiModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="manageSectiiModalLabel">Administrare secții</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <button class="btn btn-sm btn-outline-success" id="addSectieBtn">
                        <i class="fa fa-plus"></i> Adaugă secție
                    </button>
                    <div class="float-end">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showInactiveSectii">
                            <label class="form-check-label" for="showInactiveSectii">Afișează secții inactive</label>
                        </div>
                    </div>
                </div>
                <div class="table-responsive w-100">
                    <table id="tabelSectii" class="table table-hover display w-100">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Denumire secție</th>
                            <th>Instanță</th>
                            <th>Status</th>
                            <th style="width: 120px">Acțiuni</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="sectieFormModal" tabindex="-1" aria-labelledby="sectieFormModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sectieFormModalLabel">Adaugă secție</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sectieForm">
                    <input type="hidden" id="sectieId">
                    <div class="mb-3">
                        <label for="denumireSectie" class="form-label">Denumire secție</label>
                        <input type="text" class="form-control" id="denumireSectie" required>
                    </div>
                    <div class="mb-3">
                        <label for="idInstantaSectie" class="form-label">Instanță</label>
                        <select class="form-control select2" id="idInstantaSectie" required>
                            <option value="">Selectează instanța</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                <button type="button" class="btn btn-primary" id="saveSectie">Salvează</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="manageBLET" tabindex="-1" aria-labelledby="manageBLETLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <?php if( $sesiune->get('id_rol')==5 ):?> <h5 class="modal-title" id="manageBLETLabel">Administrare BLET <?php  echo $sesiune->get('judet') ?> </h5>
                <?php elseif( $sesiune->get('id_rol')==1 ):?> <h5 class="modal-title" id="manageBLETLabel">Administrare BLET </h5>
                <?php endif; ?>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <button class="btn btn-sm btn-outline-success" id="addEmailBLET">
                        <i class="fa fa-plus"></i> Adaugă email BLET
                    </button>
<!--                    <div class="float-end">-->
<!--                        <div class="form-check form-switch">-->
<!--                            <input class="form-check-input" type="checkbox" id="showInactiveSectii">-->
<!--                            <label class="form-check-label" for="showInactiveSectii">Afișează secții inactive</label>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <div class="table-responsive w-100">
                    <table id="tabelEmailBlet" class="table table-hover display w-100">
                        <thead>
                        <tr>
                            <th>Judet</th>
                            <th>Email</th>
                            <th style="width: 120px">Acțiuni</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editEmailModal" tabindex="-1" aria-labelledby="editEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEmailModalLabel">Editare Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editEmailForm">
                    <div class="mb-3">
                        <label for="emailInput" class="form-label">Email</label>
                        <input type="email" class="form-control" id="emailInput" required>
                    </div>
                    <input type="hidden" id="emailId"> <!-- Ascuns pentru a trimite id-ul la server -->
                    <input type="hidden" id="idJudetInput"> <!-- Ascuns pentru a trimite id_judet la server -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                <button type="button" class="btn btn-primary" id="saveEmailButton">Salvează modificările</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addEmailModal" tabindex="-1" aria-labelledby="addEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEmailModalLabel">Adăugare Email B.L.E.T.</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addEmailForm">
                    <div class="mb-3">
                        <label for="newEmailInput" class="form-label">Email</label>
                        <input type="email" class="form-control" id="newEmailInput" placeholder="Introdu emailul" required>
                    </div>

                    <?php if( $sesiune->get('id_rol')==1 ):?>
                    <div class="mb-3" id="judetField">
                        <label for="judetSelect" class="form-label">Selectează Județ</label>
                        <select id="judetSelect" class="form-select"></select>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                <button type="button" class="btn btn-primary" id="addEmailButton">Adaugă Email</button>
            </div>
        </div>
    </div>
</div>


<div class="container-xxl py-5">
    <div class="container">
        <div class="row align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <p>
                    <button class="btn btn-sm btn-outline-info rounded-pill" data-bs-toggle="modal"
                            data-bs-target="#addUserModal">
                        <i class="fa fa-user-plus"></i> Add utilizator
                    </button>
                    <button class="btn btn-sm btn-outline-primary rounded-pill ms-2" data-bs-toggle="modal"
                            data-bs-target="#manageSectiiModal">
                        <i class="fa fa-sitemap"></i> Administrare secții
                    </button>
                    <?php if (  ( $sesiune->get('id_nivelInstanta') == 2 && $sesiune->get('id_rol') == 5 ) || ($sesiune->get('id_rol') == 1) ): ?>
                        <button class="btn btn-sm btn-outline-secondary rounded-pill ms-2" data-bs-toggle="modal"
                                data-bs-target="#manageBLET">
                            <i class="fa fa-envelope"></i> Administrare B.L.E.T.
                        </button>
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row g-5 mb-5 align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <p class="text-center"><b>Administrare utilizatori</b></p>
            </div>
        </div>
        <div class="row g-5 mb-5 align-items-end wow fadeInUp" data-wow-delay="0.1s">
            <div class="col-md-12">
                <table id="tabelExperti" class="table table-hover display">
                    <thead>
                    <tr>
                        <th style="text-align: center; align-content: center;">User</th>
                        <th style="text-align: center; align-content: center;">Instanță</th>
                        <th style="text-align: center; align-content: center;">Rol</th>
                        <th style="text-align: center; align-content: center; width: 10%">Acțiuni</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once 'inc/cfg_footer.php'; ?>
<script src="assets/js/jqueryDatatable.js"></script>

<script src="assets/js/dataTables.buttons.min.js"></script>
<script src="assets/js/buttons.html5.min.js"></script>

<script src="vendor/select2/select2/dist/js/select2.min.js"></script>
<script src="assets/js/administrare.js"></script>
