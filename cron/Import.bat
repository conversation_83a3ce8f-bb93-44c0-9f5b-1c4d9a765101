@ECHO OFF
c:

:: Set variables for MySQL dump
set MYSQL_HOST=localhost
set MYSQL_USER=root
set MYSQL_PASS=
set MYSQL_DB=exp_jud
set TIMESTAMP=%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set EXPORT_PATH=C:\wamp64\www\exports
set DUMP_FILE=%EXPORT_PATH%\mysql_dump_%TIMESTAMP%.sql

:: Create MySQL dump
echo Creating backup for entire MySQL database...
C:\wamp64\bin\mysql\mysql9.1.0\bin\mysqldump.exe --host=%MYSQL_HOST% --user=%MYSQL_USER% --result-file="%DUMP_FILE%" %MYSQL_DB%
echo MySQL dump completed.
echo Starting import process...

:: Continue with the import process
cd C:\Program Files\Google\Chrome\Application
echo Creating backup table experti_tehnici_backup...
START chrome.exe http://localhost/import/createBackup.php
set SleepTime=2
Timeout /T %SleepTime% /NoBreak>NUL
echo Importing experts into experti_tehnici...
START chrome.exe http://localhost/import/?type=experti
set SleepTime=15
Timeout /T %SleepTime% /NoBreak>NUL
echo Generating report of differences (csv)...
START chrome.exe http://localhost/import/?report=differences^&export=all
Timeout /T %SleepTime% /NoBreak>NUL
START chrome.exe http://localhost/import/cnp_update.php?update=1
Timeout /T %SleepTime% /NoBreak>NUL
Taskkill /IM "chrome.exe" /F
