<?php
/**
 * SendEmails.php
 *
 * This script processes the email queue and sends pending emails.
 * It can be called via browser or cron job.
 */
set_time_limit(300);
$isCLI = (php_sapi_name() === 'cli');
$date = date('Y-m-d H:i:s');

require_once __DIR__ . '/../inc/cfg_db.php';
require_once __DIR__ . '/../inc/cfg_functions.php';
require_once __DIR__ . '/../inc/EmailService.php';
require_once __DIR__ . '/../inc/cfg_load_secrets.php';

// Security check for web access
if (!$isCLI) {
    // Check for a security token or admin session

    // Only allow access if user is logged in with admin role or if a valid token is provided
    $validToken = $env['EMAIL_ACCESS_TOKEN'] ?? '';
    $isAdmin = isset($SESSION_id_rol) && $SESSION_id_rol == 1; // Assuming role ID 1 is admin
    $hasValidToken = isset($_GET['token']) && $_GET['token'] === $validToken;

    if (!$isAdmin && !$hasValidToken) {
        header('HTTP/1.1 403 Forbidden');
        echo "Access denied. You need to be an logged in administrator or provide a valid token.";
        exit;
    }

    ob_start();
    header('Content-Type: text/plain; charset=utf-8');
}

function logMessage($message) {
    global $isCLI;

    $formattedMessage = $isCLI ? $message : $message;

    echo $formattedMessage . "\n";

    $logFile = __DIR__ . '/../logs/email_cron_start_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    file_put_contents(
        $logFile,
        date('Y-m-d H:i:s') . ' - ' . $message . "\n",
        FILE_APPEND
    );
}

$startTime = microtime(true);
$startDate = date('Y-m-d H:i:s');
logMessage("=== Email Queue Processing Started: $startDate ===");

try {
    // Check if PHPMailer is available
    if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer') && !class_exists('PHPMailer')) {
        logMessage("ERROR: PHPMailer class not found. Please install PHPMailer or check your includes.");
        logMessage("You can install PHPMailer using Composer: composer require phpmailer/phpmailer");
        exit;
    }

    $dbConnection = DatabasePool::getConnection();
    $emailService = new EmailService($dbConnection);

    $stmt = $dbConnection->query("SELECT COUNT(*) nr FROM exp_jud.email_queue WHERE status != 'sent' and retries > 5");
    $retriesCount = $stmt->fetchColumn();
    $retriesCount= 0; //skip sms pt ca nu merge .exe -  da eroare
    if ($retriesCount > 5) {
        logMessage("Found $retriesCount emails that failed to send more than 5 times...");
        logMessage("Sending sms...");
        //Daca nu merge emailul nu poate trimite notice-ul, asa ca trimite SMS
        $numarTelefon = formatNumarTelefon('0749607867');
        $sms_message = "Peste 5 incercari de trimitere esuate la aplicatia DAETJ exp_jud.email_queue.";
        $scriptFile = "C:\\wamp64\\www\\cron\\sendsms_1.exe";

        $output = [];
        $returnCode = null;
        exec("icacls $scriptFile /grant Everyone:(RX)", $output, $returnCode);
        $command = "powershell.exe $scriptFile -m '$sms_message' -nr '$numarTelefon' 2>&1";
        $out = shell_exec($command);
        logMessage("out: $out");
        logMessage("command: $command");
    }

    $stmt = $dbConnection->query("SELECT COUNT(*) nr FROM exp_jud.email_queue WHERE status != 'sent'");
    $emailCount = $stmt->fetchColumn();
    logMessage("Found $emailCount emails in queue to process");

    logMessage("Processing email queue...");
    $emailService->processEmailQueue();

    $stmt = $dbConnection->query("SELECT COUNT(*) nr FROM exp_jud.email_queue WHERE status != 'sent'");
    $remainingCount = $stmt->fetchColumn();
    $sentCount = $emailCount - $remainingCount;

    logMessage("Email queue processing completed. Sent: $sentCount, Remaining: $remainingCount");

    DatabasePool::releaseConnection($dbConnection);

    $executionTime = round(microtime(true) - $startTime, 2);

    logMessage("\nStarted: $startDate");
    logMessage("Finished: " . date('Y-m-d H:i:s'));
    logMessage("Execution time: $executionTime seconds");
    logMessage("Emails processed: $emailCount, Sent: $sentCount, Remaining: $remainingCount");
    logMessage("=== Email Queue Processing Completed Successfully ===");
} catch (Exception $e) {
    logMessage("ERROR: " . $e->getMessage());
    logMessage("File: " . $e->getFile() . " Line: " . $e->getLine());

    $executionTime = round(microtime(true) - $startTime, 2);

    logMessage("Started: $startDate");
    logMessage("Failed: " . date('Y-m-d H:i:s'));
    logMessage("Execution time: $executionTime seconds");
    logMessage("\n=== Email Queue Processing FAILED ===");

    if (isset($dbConnection)) {
        DatabasePool::releaseConnection($dbConnection);
    }
}

// Flush output buffer if not in CLI mode
if (!$isCLI) {
    ob_end_flush();
}

