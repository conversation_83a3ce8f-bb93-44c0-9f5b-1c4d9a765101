@ECHO OFF
setlocal enabledelayedexpansion

:: Set the URL and completion text
set "URL=http://localhost/cron/ecris.php?token=gZbrF4HzcpsVGDLAQTuRdIy8YtJB3afqPewC76kSnhj9MKUWNX"
set "COMPLETION_TEXT=Processing Completed Successfully"

:: Start Chrome
echo Starting browser to execute cron job...
start "" "%ProgramFiles%\Google\Chrome\Application\chrome.exe" "%URL%"

:: Wait a moment for the browser to start
timeout /t 3 /nobreak >nul

:: Monitor the URL content using PowerShell
:CHECK_LOOP
echo Checking for completion...

:: Use PowerShell to fetch page content
powershell -Command "try { $response = Invoke-WebRequest -Uri '%URL%' -TimeoutSec 30; $response.Content } catch { 'ERROR' }" > temp_response.txt

:: Check if the completion text is in the response
findstr /C:"%COMPLETION_TEXT%" temp_response.txt >nul
if !ERRORLEVEL! EQU 0 (
    echo Processing completed successfully detected!
    goto CLEANUP
)

:: Check if there was an error
findstr /C:"ERROR" temp_response.txt >nul
if !ERRORLEVEL! EQU 0 (
    echo Error accessing URL, retrying...
)

:: Wait before checking again
timeout /t 10 /nobreak >nul
goto CHECK_LOOP

:CLEANUP
:: Clean up temp file
if exist temp_response.txt del temp_response.txt

:: Close Chrome
echo Closing browser...
taskkill /IM "chrome.exe" /F >nul 2>&1

echo Script completed.