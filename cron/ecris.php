<?php
require_once '../inc/cfg_db.php';
require_once __DIR__ . '/../inc/cfg_load_secrets.php';

// Check for a security token

// Only allow access if user is logged in with admin role or if a valid token is provided
$validToken = $env['EMAIL_ACCESS_TOKEN'] ?? '';
$hasValidToken = isset($_GET['token']) && $_GET['token'] === $validToken;

if (!$hasValidToken) {
    header('HTTP/1.1 403 Forbidden');
    echo "Access denied. You need to provide a valid token.";
    exit;
}
ob_start();
header('Content-Type: text/plain; charset=utf-8');

$db = DatabasePool::getConnection();

// === Pregătește toate statement-urile o singură dată ===
$stmt_expertize = $db->prepare(
    "SELECT id, IdStatusExpertiza FROM expertize
     WHERE nrDosar = ? AND cnpExpert = ? AND idStatusExpertiza = ? LIMIT 1"
);

$stmt_workload = $db->prepare(
    "SELECT idLoad FROM workload WHERE CNP = ? LIMIT 1"
);

$stmt_update_expertiza_close = $db->prepare(
    "UPDATE expertize SET idStatusExpertiza = 0, idUtilizatorInchidere = ?, dataInchidere = NOW() WHERE id = ?"
);

$stmt_update_expertiza_open = $db->prepare(
    "UPDATE expertize SET idStatusExpertiza = 1, idUtilizatorInchidere = 0, dataInchidere = NULL WHERE id = ?"
);

$stmt_update_workload_dec_app = $db->prepare(
    "UPDATE workload SET incarcatura_app = GREATEST(incarcatura_app - 1, 0) WHERE idLoad = ?"
);

$stmt_update_workload_inc_app = $db->prepare(
    "UPDATE workload SET incarcatura_app = incarcatura_app + 1 WHERE idLoad = ?"
);

$stmt_update_workload_dec_etj = $db->prepare(
    "UPDATE workload SET incarcatura_ETJ = GREATEST(incarcatura_ETJ - 1, 0) WHERE idLoad = ?"
);

$stmt_update_workload_inc_etj = $db->prepare(
    "UPDATE workload SET incarcatura_ETJ = incarcatura_ETJ + 1 WHERE idLoad = ?"
);

$stmt_insert_audit = $db->prepare(
    "INSERT INTO audit_log (user_id, action_type, action_category, action_level, table_name, record_id, description, created_at)
     VALUES (?, ?, ?, ?, ?, ?, ?, NOW())"
);

$stmt_insert_err = $db->prepare(
    "INSERT INTO replicare_document_err (id_document, numar_national, cnp, id_institutie, ts, flag)
     VALUES (?, ?, ?, ?, ?, ?)"
);

$stmt_update_replicare = $db->prepare(
    "UPDATE replicare_document_expert SET scazut = 1 WHERE id_document = ?"
);

// === Select rândurile de procesat ===
$stmt = $db->query(
    "SELECT id_document, numar_national, cnp, id_institutie, ts, CAST(flag AS UNSIGNED) AS flag
     FROM replicare_document_expert
     WHERE scazut = 0
     GROUP BY id_document, flag"
);
$rows = $stmt->fetchAll();

foreach ($rows as $row) {
    $id_document    = $row['id_document'];
    $numar_national = $row['numar_national'];
    $cnp            = $row['cnp'];
    $id_institutie  = $row['id_institutie'];
    $ts             = $row['ts'];
    $flag           = (int)$row['flag'];

    // === Găsește expertiza ===
    $stmt_expertize->execute([$numar_national, $cnp, $flag]);
    $expertiza = $stmt_expertize->fetch();
    $expertize_id = $expertiza['id'] ?? null;
    $expertiza_status = $expertiza['IdStatusExpertiza'] ?? null;

    // === Găsește workload ===
    $stmt_workload->execute([$cnp]);
    $workload = $stmt_workload->fetch();
    $workload_id = $workload['idLoad'] ?? null;

    // === Procesare în funcție de FLAG ===
    if ($flag === 1) {
        if ($expertize_id && $expertiza_status == 1) {
            // CAZ I
            $stmt_update_expertiza_close->execute([$id_document, $expertize_id]);

            if ($workload_id) {
                $stmt_update_workload_dec_app->execute([$workload_id]);
            }

            $stmt_insert_audit->execute([
                $id_institutie, 'ecris/expertiza_inchisa', 'update', 'info',
                'expertize', $expertize_id,
                "Închidere expertiză pentru dosar $numar_national"
            ]);

        } elseif (!$expertize_id && $workload_id) {
            // CAZ II
            $stmt_update_workload_dec_etj->execute([$workload_id]);

            $stmt_insert_audit->execute([
                $id_institutie, 'ecris/scadere_ETJ', 'update', 'info',
                'workload', $workload_id,
                "Scădere ETJ pentru expert cu CNP $cnp"
            ]);

        } else {
            // CAZ III
            $stmt_insert_audit->execute([
                $id_institutie, 'ecris/caz_neidentificat', 'administrative', 'warning',
                'replicare_document_expert', null,
                "Nu s-a găsit expertiza sau workload pentru expert cu CNP $cnp"
            ]);

            $stmt_insert_err->execute([
                $id_document, $numar_national, $cnp, $id_institutie, $ts, $flag
            ]);
        }

    } else {
        if ($expertize_id && $expertiza_status == 0) {
            // CAZ I
            $stmt_update_expertiza_open->execute([$expertize_id]);

            if ($workload_id) {
                $stmt_update_workload_inc_app->execute([$workload_id]);
            }

            $stmt_insert_audit->execute([
                $id_institutie, 'ecris/expertiza_redeschisa', 'update', 'info',
                'expertize', $expertize_id,
                "Redeschidere expertiză pentru dosar $numar_national"
            ]);

        } elseif (!$expertize_id && $workload_id) {
            // CAZ II
            $stmt_update_workload_inc_etj->execute([$workload_id]);

            $stmt_insert_audit->execute([
                $id_institutie, 'ecris/crestere_ETJ', 'update', 'info',
                'workload', $workload_id,
                "Creștere ETJ pentru expert cu CNP $cnp"
            ]);

        } else {
            // CAZ III
            $stmt_insert_audit->execute([
                $id_institutie, 'ecris/caz_neidentificat', 'administrative', 'warning',
                'replicare_document_expert', null,
                "Nu s-a găsit expertiza sau workload pentru expert cu CNP $cnp"
            ]);

            $stmt_insert_err->execute([
                $id_document, $numar_national, $cnp, $id_institutie, $ts, $flag
            ]);
        }
    }

    // === Marchează ca procesat ===
    $stmt_update_replicare->execute([$id_document]);
}
	echo '=== Ecris Update Processing Completed Successfully ===';