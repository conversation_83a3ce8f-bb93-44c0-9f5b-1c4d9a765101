<?php
// Enable error display for debugging (remove in production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Locate and load .env file
$envPaths = ['.env', '../.env', dirname(__DIR__) . '/.env', '/.env'];
$env = null;
foreach ($envPaths as $path) {
    if (file_exists($path)) {
        $env = parse_ini_file($path);
        break;
    }
}

if (!$env) {
    die('Error: .env file not found in the specified paths.');
}
