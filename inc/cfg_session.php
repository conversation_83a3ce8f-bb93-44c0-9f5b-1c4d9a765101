<?php
// Only start the session if it hasn't been started already
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define global session timeout (in seconds)
define('SESSION_TIMEOUT', 60 * 60); // 60 sec * 60 min = 1 ora

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../controller/AccessControl.php';
require_once __DIR__ . '/cfg_db.php';
require_once __DIR__ . '/cfg_functions.php';

use Aura\Session\SessionFactory;

if (!isset($session_factory)) {
    $session_factory = new SessionFactory();
    $session = $session_factory->newInstance($_COOKIE); // Use $_COOKIE instead of $_SESSION
    $sesiune = $session->getSegment('Vendor\Package\ClassName');
}

$logged_in = $sesiune->get('logged_in');
if ($logged_in == true) {
    // Check if session has expired
    $lastActivity = $sesiune->get('last_activity');
    $currentTime = time();
    $sessionTimeout = SESSION_TIMEOUT;

    // If last_activity is not set, set it now
    if (!$lastActivity) {
        $sesiune->set('last_activity', $currentTime);
    }
    // If session has been inactive for too long
    elseif ($currentTime - $lastActivity > $sessionTimeout) {
        // Session has expired
        $username = $sesiune->get('username');
        $user_id = $sesiune->get('id_utilizator');

        // Log session expiration if we have user info
        if ($username && $user_id) {
            $dbConnection = DatabasePool::getConnection();
            log_auth_event($dbConnection, 'session_expired', [
                'user_id' => $user_id,
                'username' => $username,
                'description' => 'Sesiune expirată după ' . round(($currentTime - $lastActivity) / 60) . ' minute de inactivitate'
            ]);
        }

        $session->destroy();
        header("location:login.php");
        exit();
    }
    // Only update last_activity on user interaction, not on page refresh
    // This is handled by session-verification.js

    // Session is valid, set global variables
    $SESSION_username = $sesiune->get('username');
    $SESSION_instanta = $sesiune->get('instanta');
    $SESSION_id_instanta = $sesiune->get('id_instanta');
    $SESSION_id_sectie = $sesiune->get('id_sectie');
    $SESSION_judet = $sesiune->get('judet');
    $SESSION_id_rol = $sesiune->get('id_rol');
    $SESSION_rol = $sesiune->get('rol');
    $SESSION_id_utilizator = $sesiune->get('id_utilizator');
    $SESSION_id_judet = $sesiune->get('id_judet');

    // Log page access (optional - can be resource intensive if enabled for all pages)
    $dbConnection = DatabasePool::getConnection();
    $current_page = basename($_SERVER['PHP_SELF']);
//    $query_params = $_GET;
//    log_page_access($dbConnection, $current_page, [
//        'query_params' => $query_params
//    ]);
} else {
    $session->destroy();
    header("location:login.php");
    exit();
}
