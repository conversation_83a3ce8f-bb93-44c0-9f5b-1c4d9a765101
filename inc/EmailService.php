<?php
// Check if running from CLI
$isCLI = (php_sapi_name() === 'cli');

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// Include PHPMailer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// If autoloader is not available, try direct includes
if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';
    require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/SMTP.php';
    require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/Exception.php';
}

/**
 * EmailService Class
 *
 * Handles all email-related functionality including:
 * - Checking available email hosts
 * - Sending emails
 * - Queuing emails for later sending
 * - Processing email queue
 * - Audit logging of email operations
 */
class EmailService
{
    private $dbConnection;
    private $date;
    private $semnaturaDTI;
    private $connection_from_pool = false;

    /**
     * Constructor
     *
     * @param PDO $dbConnection Database connection
     */
    public function __construct($dbConnection = null)
    {
        global $date, $semnaturaDTI;
        global $SESSION_username, $SESSION_instanta, $SESSION_id_instanta, $SESSION_id_sectie,
               $SESSION_judet, $SESSION_id_rol, $SESSION_rol, $SESSION_id_utilizator, $SESSION_id_judet;

        // If PDO is not provided, get it from DatabasePool
        if ($dbConnection === null) {
            require_once __DIR__ . '/cfg_db.php';
            $this->dbConnection = DatabasePool::getConnection();
            $this->connection_from_pool = true;
        } else {
            $this->dbConnection = $dbConnection;
            $this->connection_from_pool = false;
        }

        // Set date if not already set
        if (!isset($date)) {
            $date = date('Y-m-d H:i:s');
        }

        $this->date = $date;
        $this->semnaturaDTI = $semnaturaDTI;
        $this->semnaturaDTI = isset($semnaturaDTI) ? $semnaturaDTI : '';

        // Check if running from CLI
        $isCLI = (php_sapi_name() === 'cli');

        // Log session information for debugging if needed (only if not in CLI mode)
        if (!$isCLI && $this->dbConnection && isset($SESSION_id_utilizator)) {
            $this->logEmailOperation('email_service_init', 'constructor', 'EmailService Initialization', [
                'description' => "Clasa EmailService a fost instantiata de catre utilizatorul: $SESSION_username",
                'data_after' => [
                    'user_id' => $SESSION_id_utilizator,
                    'username' => $SESSION_username,
                    'instanta' => $SESSION_instanta,
                    'judet' => $SESSION_judet,
                    'rol' => $SESSION_rol
                ]
            ]);
        } elseif ($isCLI && $this->dbConnection) {
            // Log initialization from CLI
            $this->logEventEmail("EmailService initialized from CLI at {$this->date}");
        }
    }


    /**
     * Check available email hosts
     *
     * @param int $port Port to check
     * @return string|null Host name if available, null otherwise
     */
    public
    function verificareServerEmail($port)
    {
        $hosts = ['***********', 'mail.just.ro', '***********', '***********'];
        $timeout = 2; // seconds

        foreach ($hosts as $host) {
            $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
            if (is_resource($connection)) {
                fclose($connection);
                return $host;
            }
        }

        return null; // if none of the hosts are available
    }


    /**
     * Queue email for later sending
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $content Email content
     * @return bool|string True if queued successfully, error message otherwise
     */
    public
    function queueEmail($to, $subject, $content)
    {
        $text = null;
        $stmt_check = $this->dbConnection->prepare("SELECT id, retries FROM exp_jud.email_queue WHERE to_email = :to_email AND subject = :subject AND body = :body and status != 'sent'");
        $stmt_check->execute([
            ':to_email' => $to,
            ':subject' => $subject,
            ':body' => $content
        ]);
        $nrStmt_check = $stmt_check->rowCount();
        $stmt_check = $stmt_check->fetchAll(PDO::FETCH_ASSOC);

        if ($nrStmt_check > 0) {
            foreach ($stmt_check as $s) {
                $id = $s['id'];

                $updateStmt = $this->dbConnection->prepare("UPDATE exp_jud.email_queue SET retries = retries + 1 WHERE id = :id");
                $updateStmt->execute([
                    ':id' => $id,
                ]);

                $text = "Se încearcă retrimiterea e-mail-ului...\n<br>";
                $this->logEventEmail($text);
            }
        } else {
            $stmt = $this->dbConnection->prepare("INSERT INTO exp_jud.email_queue (to_email, subject, body) VALUES (:to_email, :subject, :body)");
            if (
                $stmt->execute([
                    ':to_email' => $to,
                    ':subject' => $subject,
                    ':body' => $content
                ])
            ) {
                $text = "-- Efectuata adaugarea in queue a emailului (adaugat in exp_jud.email_queue cu succes) catre = $to / subiect = $subject / continut = $content.";
                $this->logEventEmail($text);
                // Log email queued successfully
                $this->logEmailOperation('email_queued', $to, $subject, [
                    'description' => "Email adăugat în coada de așteptare pentru $to",
                    'data_after' => ['recipient' => $to, 'subject' => $subject]
                ]);
                return true;
            } else {
                $text = "Nu s-a reușit trimiterea e-mail-ului catre = $to / subiect = $subject / continut = $content.";
                $this->logEventEmail($text);
                // Log email queue failure
                $this->logEmailOperation('email_queue_failed', $to, $subject, [
                    'description' => "Eroare la adăugarea email-ului în coada de așteptare pentru $to",
                    'data_after' => ['recipient' => $to, 'subject' => $subject]
                ]);
                return false;
            }
        }
    }


    /**
     * Process email queue to see which emails need to be sent
     */
    public
    function processEmailQueue()
    {
        $sql = "SELECT id, to_email, subject, body, retries FROM exp_jud.email_queue WHERE status != 'sent'";
        $sql = $this->dbConnection->query($sql);
        $nrSql = $sql->rowCount();
        $sql = $sql->fetchAll();

        if ($nrSql > 0) {
            $mailRadu = null;
            foreach ($sql as $row) {
                $id = $row['id'];
                $to = $row['to_email'];
                $subject = $row['subject'];
                $content = $row['body'];
                $this->sendEmail($to, $subject, $content, $id);
            }

            $retries = 0;
            foreach ($sql as $row) {
                $id = $row['id'];
                $to = $row['to_email'];
                $retries = $row['retries'];
                if ($to == '<EMAIL>') {
                    $this->updateEmailStatus($id, 'sent');
                }
                if ($retries > 5) {
                    $mailRadu .= "select * from exp_jud.email_queue where id = $id;";
                }
            }
            if ($retries > 5) {
                //Daca nu merge emailul nu poate trimite notice-ul, asa ca trimite SMS
                if (function_exists('formatNumarTelefon')) {
                    $numarTelefon = formatNumarTelefon('0749607867');
                    $sms_message = "Peste 5 incercari de trimitere esuate. Am modificat statusul din failed in sent pentru a nu mai incerca trimiterile la urmatoarele id-uri din DAETJ $mailRadu";
                    $scriptFile = "C:\\wamp64\\www\\cron\\sendsms_1.exe";

                    $output = [];
                    $returnCode = null;
                    exec("icacls $scriptFile /grant Everyone:(RX)", $output, $returnCode);
                    $command = "powershell.exe C:\\wamp64\\www\\cron\\sendsms_1.exe -m '$sms_message' -nr '$numarTelefon' 2>&1";
                    $out = shell_exec($command);
                }
            }
        }
    }


    /**
     * Send email
     *
     * @param string $to Recipient email
     * @param string $subiect Email subject
     * @param string $continut Email content
     * @param int $id Email queue ID
     * @return string Result message
     */
    public
    function sendEmail($to, $subiect, $continut, $id)
    {
        $continut = "
        <!DOCTYPE html>
            <html>
            <head>
                <title>Direcția Tehnologia Informației</title>
            </head>
            <body>
              $continut
              {$this->semnaturaDTI}
            </body>
        </html>";

        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        try {
            // Set UTF-8 character encoding
            $mail->CharSet = 'UTF-8';
            $mail->Encoding = 'base64';

            $port = 25;
            $mail->isSMTP();
            $mail->Timeout = 30;
            $hostEmail = $this->verificareServerEmail($port);
            if ($hostEmail === null) {
                throw new \PHPMailer\PHPMailer\Exception('No available email server - eroare CAS e-mail');
            }

            $mail->Host = $hostEmail;
            //$mail->Host = '***********';//da
            //$mail->Host = '***********';
            //$mail->Host = '***********';
            //$mail->Host = 'mail.just.ro';

            $mail->SMTPAuth = false;
            $mail->Port = $port;

            // $mail->SMTPAuth = true;
            //$mail->SMTPSecure = 'tls';
            //$mail->Port = 587;

            // $mail->Port = 465;
            // $mail->SMTPSecure = 'ssl';

            // $mail->Username = '<EMAIL>';
            // $mail->Password = 'P@ssw0rd20131';
            $mail->SMTPOptions = [
                'ssl' => [
                    'verify_peer' => true,
                    'verify_peer_name' => false,
                    'allow_self_signed' => false,
                ]
            ];

            // Set debug level (0 = off, 1 = client, 2 = client/server, 3 = client/server/connection, 4 = low level)
//            $mail->SMTPDebug = 1;

            // Start output buffering to capture debug output
            ob_start();

            // Set debug output to echo (will be captured by output buffering)
            $mail->Debugoutput = 'echo';

            $mail->setFrom('<EMAIL>', 'DTI');
            $mail->addAddress($to);
            if (str_contains($subiect, 'Credentiale noi cont')) {
                //fara BCC
            } else {
//                $mail->addBCC('<EMAIL>');
            }

            $mail->isHTML(true);
            $mail->Subject = $subiect;
            $mail->Body = $continut;

            if ($mail->send()) {
                $this->updateEmailStatus($id, 'sent');
                $text = "-- E-mail (cu id $id) trimis catre $to -- {$this->date}<br>";
                // Log successful email send
                $this->logEmailOperation('email_sent', $to, $subiect, [
                    'description' => "Email (cu id $id) trimis cu succes către $to",
                    'data_after' => ['recipient' => $to, 'subject' => $subiect]
                ]);
            } else {
                $text = "Failed to send email. Error: {$mail->ErrorInfo}<br>";
                $this->updateEmailStatus($id, 'failed');
                // Log failed email send
                $this->logEmailOperation('email_failed', $to, $subiect, [
                    'description' => "Eroare la trimiterea email-ului către $to: {$mail->ErrorInfo}",
                    'data_after' => ['recipient' => $to, 'subject' => $subiect, 'error' => $mail->ErrorInfo]
                ]);
            }
        } catch (Exception $e) {
            $text = "Message could not be sent. Mailer Error: {$mail->ErrorInfo}<br>";
            $this->updateEmailStatus($id, 'failed');
            // Log exception during email send
            $this->logEmailOperation('email_exception', $to, $subiect, [
                'description' => "Excepție la trimiterea email-ului către $to: {$e->getMessage()}",
                'data_after' => ['recipient' => $to, 'subject' => $subiect, 'error' => $e->getMessage()]
            ]);
        }

        $this->logEventEmail($text);

        return $text;
    }


    /**
     * Update email status in queue
     *
     * @param int $id Email queue ID
     * @param string $status New status
     */
    public
    function updateEmailStatus($id, $status)
    {
        $updateStmt = "UPDATE exp_jud.email_queue SET status = :status WHERE id = :id";
        if ($status == 'failed') {
            $updateStmt = "UPDATE exp_jud.email_queue SET status = :status, retries = retries + 1 WHERE id = :id";
        }
        $updateStmt = $this->dbConnection->prepare($updateStmt);
        $updateStmt->execute([
            ':id' => $id,
            ':status' => $status
        ]);
    }


    /**
     * Send email to technical expert
     *
     * @param string $cnp Expert CNP
     * @param string $subject Email subject
     * @param string $content Email content
     * @param array $expertData Additional expert data
     * @return bool|string True if queued successfully, error message otherwise
     */
    public
    function sendEmailToExpert($cnp, $subject, $content, $expertData = [])
    {
        // Get expert email if not provided in expertData
        if (empty($expertData['email'])) {
            $stmt = $this->dbConnection->prepare("SELECT email FROM exp_jud.experti_tehnici WHERE cnp = :cnp");
            $stmt->execute([':cnp' => $cnp]);
            $expertEmail = $stmt->fetchColumn();

            if (empty($expertEmail)) {
                return "Expertul cu CNP $cnp nu are adresă de email înregistrată.";
            }

            $to = $expertEmail;
        } else {
            $to = $expertData['email'];
        }

        // Add expert details to content if available
        if (!empty($expertData)) {
            $expertInfo = "<p><strong>Detalii expert:</strong><br>";
            if (!empty($expertData['nume']) && !empty($expertData['prenume'])) {
                $expertInfo .= "Nume: {$expertData['nume']} {$expertData['prenume']}<br>";
            }
            if (!empty($expertData['judet'])) {
                $expertInfo .= "Județ: {$expertData['judet']}<br>";
            }
            if (!empty($expertData['localitate'])) {
                $expertInfo .= "Localitate: {$expertData['localitate']}<br>";
            }
            if (!empty($expertData['legitimatie'])) {
                $expertInfo .= "Legitimație: {$expertData['legitimatie']}<br>";
            }

            $content .= "$expertInfo</p>";
            $content .= "<p>Acest email este generat automat. Vă rugăm să nu răspundeți la acest email.</p>";
        }

        // Queue the email
        return $this->queueEmail($to, $subject, $content);
    }


    /**
     * Get BLET email and county name for a given county ID
     *
     * @param int $id_judet County ID
     * @return array|null Array with 'email' and 'numeJudet' keys, or null if not found
     */
    private
    function getBLETEmailData($id_judet)
    {
        // Get BLET email for the specified county
        $select = "SELECT e.email, j.numeJudet
                   FROM exp_jud.e_blet e
                   JOIN exp_jud.njudete j ON e.id_judet = j.idJudet
                   WHERE e.id_judet = :id_judet";
        $stmt = $this->dbConnection->prepare($select);
        $stmt->execute([':id_judet' => $id_judet]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }


    /**
     * Format experts list for email
     *
     * @param array $experts Array of expert strings or expert data arrays
     * @return string Formatted HTML string with numbered and bold experts
     */
    public
    function formatExpertsForEmail($experts)
    {
        $formattedExperts = [];
        $counter = 1;

        foreach ($experts as $expert) {
            // If expert is a string, parse it
            if (is_string($expert)) {
                // Extract the expert name and details
                if (preg_match('/(.*?)\s+\(Județ: (.*?), Localitate: (.*?)\)/', $expert, $matches)) {
                    $expertName = trim($matches[1]);
                    $judet = trim($matches[2]);
                    $localitate = trim($matches[3]);
                    $formattedExperts[] = "$counter. <b>$expertName</b> (Județ: $judet, Localitate: $localitate)";
                } else {
                    // Fallback if the pattern doesn't match
                    $formattedExperts[] = "$counter. $expert";
                }
            } // If expert is an array with name, prenume, judet, localitate
            else if (is_array($expert) && isset($expert['nume']) && isset($expert['prenume'])) {
                $expertName = $expert['nume'] . ' ' . $expert['prenume'];
                $judet = $expert['judet'] ?? '';
                $localitate = $expert['localitate'] ?? '';
                $formattedExperts[] = "$counter. <b>$expertName</b> (Județ: $judet, Localitate: $localitate)";
            }
            $counter++;
        }

        // Join with line breaks
        return implode("<br>\n", $formattedExperts);
    }


    /**
     * Send email to BLET based on user's county
     *
     * This method is similar to sendEmailToBLETByUserCounty but with a different naming convention.
     * It's maintained for backward compatibility with existing code.
     *
     * @param string $subject Email subject
     * @param string $content Email content
     * @param array $additionalData Additional data to include in the email
     * @param int|null $override_id_judet Override the user's county ID (optional)
     * @return bool|string True if queued successfully, error message otherwise
     */
    public
    function sendBletEmail($subject, $content, array $additionalData = [], $override_id_judet = null)
    {
        global $SESSION_id_judet, $SESSION_instanta, $SESSION_judet;

        // Get user's county ID from session or use override if provided
        $id_judet = $override_id_judet;
        if ($id_judet === null) {
            // Try to get county ID from session
            if (isset($SESSION_id_judet)) {
                $id_judet = $SESSION_id_judet;
            } else {
                // Log the failure to find user's county
                $this->logEmailOperation('blet_user_county_not_found', "session", $subject, [
                    'description' => "Nu s-a putut determina județul utilizatorului din sesiune",
                    'data_after' => ['subject' => $subject]
                ]);
                return "Nu s-a putut determina județul utilizatorului din sesiune";
            }
        }

        // Get BLET email data
        $bletData = $this->getBLETEmailData($id_judet);

        if (!$bletData || empty($bletData['email'])) {
            // Log the failure to find BLET email
            $this->logEmailOperation('blet_email_not_found', "id_judet:$id_judet", $subject, [
                'description' => "Nu s-a găsit adresa de email BLET pentru județul cu ID $id_judet",
                'data_after' => ['id_judet' => $id_judet]
            ]);
            return "Nu s-a găsit adresa de email BLET pentru județul cu ID $id_judet<br>";
        }

        // Add operation info to additional data
        $additionalData['Desemnare operată de'] = "$SESSION_instanta (Județ $SESSION_judet)";

        // Format the email content
        $bletInfo = '';
        $judetName = $bletData['numeJudet'] ?? "Județul cu ID $id_judet";

        // Update the greeting in the content if it contains the placeholder
        if (strpos($content, 'Stimate Birou Local pentru Expertize Tehnice') !== false) {
            $content = preg_replace(
                '/Stimate Birou Local pentru Expertize Tehnice[^<]*/',
                "Stimate Birou Local pentru Expertize Tehnice $judetName ({$bletData['email']})",
                $content
            );
        }

        // Add additional data if provided
        if (!empty($additionalData)) {
            $bletInfo .= "<b>Informații suplimentare:</b><br>";

            // Process each key-value pair
            $lastKey = array_key_last($additionalData);
            foreach ($additionalData as $key => $value) {
                // Special formatting for experts
                if (is_array($value)) {
                    if ($key == 'Experți Desemnați') {
                        // Use our dedicated method for formatting experts
                        $value = $this->formatExpertsForEmail($value);
                    } else {
                        $value = implode("<br>", $value);
                    }
                }

                // Add the key-value pair to the email
                if ($key == 'Experți Desemnați') {
                    $bletInfo .= "$key: <br>$value<br>";
                } else {
                    $bletInfo .= "$key: $value<br>";
                }

                // Add an extra line break before 'Desemnare operată de'
                if ($key == 'Experți Desemnați' && $lastKey != $key) {
                    $bletInfo .= "<br>";
                }
            }
        }

        $bletInfo .= "<p>Acest email este generat automat. Vă rugăm să nu răspundeți la acest email.</p>";

        // Queue the email
        $fullContent = $content . "<br>" . $bletInfo;
        $result = $this->queueEmail($bletData['email'], $subject, $fullContent);

        return $result;
    }


    /**
     * Send notification email for expert assignment
     *
     * This is the main method for sending emails to experts who have been assigned.
     *
     * @param array $expertData Expert data
     * @param string $nrDosar Case number
     * @param string $specializari Specializations
     * @param int $onorariu Fee
     * @return bool|string True if queued successfully, error message otherwise
     */
    public
    function sendEmailCatreExpert($expertData, $nrDosar, $specializari, $onorariu)
    {
        return;//todo de scos cand dam drumul la notificarea expertilor
        if (empty($expertData['cnp'])) {
            return "Date expert incomplete.";
        }

        global $SESSION_instanta, $SESSION_judet;

        $subject = "Desemnare expert pentru dosarul $nrDosar";
        $content = "
        <p>Stimate expert,</p>
        <p>
            Vă informăm că ați fost desemnat pentru a efectua o expertiză în dosarul <strong>$nrDosar</strong>.<br>
            <strong>Specializări:</strong> $specializari<br>
            <strong>Onorariu:</strong> $onorariu RON<br>
            Vă rugăm să contactați instanța ($SESSION_instanta, județ $SESSION_judet) pentru detalii suplimentare.<br>
        </p>";

        $result = $this->sendEmailToExpert($expertData['cnp'], $subject, $content, [
            'nume' => $expertData['nume'],
            'prenume' => $expertData['prenume'],
            'judet' => $expertData['judet'],
            'localitate' => $expertData['localitate'],
            'legitimatie' => $expertData['nr_legitimatie']
        ]);

        // Log random expert notification
        $this->logEmailOperation('expert_notification', $expertData['cnp'], $subject, [
            'description' => "Notificare expert desemnat pentru dosarul $nrDosar",
            'data_after' => [
                'expert_cnp' => $expertData['cnp'],
                'expert_nume' => $expertData['nume'] . ' ' . $expertData['prenume'],
                'dosar' => $nrDosar,
                'specializari' => $specializari,
                'onorariu' => $onorariu
            ]
        ]);

        return $result;
    }


    /**
     * Log email operations to audit log
     *
     * @param string $action_type Type of email operation
     * @param string $recipient Email recipient or expert CNP
     * @param string $subject Email subject
     * @param array $details Additional details
     */
    private
    function logEmailOperation($action_type, $recipient, $subject, array $details = [])
    {
        if (!function_exists('log_audit_action') || !function_exists('log_data_operation')) {
            return; // Skip logging if functions don't exist
        }

        $description = $details['description'] ?? "Operație email: $action_type";
        $data_after = $details['data_after'] ?? null;

        // log as audit action for detailed tracking
        log_audit_action($this->dbConnection, [
            'action_type' => $action_type,
            'action_category' => 'email',
            'action_level' => strpos($action_type, 'failed') !== false ? 'warning' : 'info',
            'description' => $description,
            'data_after' => $data_after
        ]);

        // For certain operations, log as admin action
        if (in_array($action_type, ['expert_notification', 'email_sent'])) {
            if (function_exists('log_admin_action')) {
                log_admin_action($this->dbConnection, $action_type, [
                    'description' => $description,
                    'data_after' => $data_after
                ]);
            }
        }
    }


    /**
     * Write message to cron file
     *
     * @param string $text Text to write
     */
    private
    function logEventEmail($text)
    {
        $text = strip_tags($text);
        writeToLogFile($text);
    }


    /**
     * Trimite email de notificare către BLET pentru desemnarea aleatorie a unui expert
     *
     * @param string $nrDosar Numărul dosarului
     * @param string $specializariAlese Specializările alese (string separat prin virgulă)
     * @param int $onorariu Onorariul stabilit
     * @param int $nrExperti Numărul de experți desemnați
     * @param array $expertData Array cu datele experților desemnați
     * @return bool|string Rezultatul trimiterii email-ului
     */
    public function sendEmailCatreBLET($nrDosar, $specializariAlese, $onorariu, $nrExperti, $expertData)
    {
        $bletSubject = "Desemnare expert pentru dosarul $nrDosar";
        $bletContent = "
        <p>Stimate Birou Local pentru Expertize Tehnice</p>
        <p>
            Vă informăm că a fost efectuată o desemnare în dosarul <b>$nrDosar</b><br>
            <b>Specializare:</b> $specializariAlese<br>
            <b>Onorariu:</b> $onorariu RON<br>
            <b>Număr experți desemnați:</b> $nrExperti
        </p>";
        $expertInfo = "{$expertData['nume']} {$expertData['prenume']} (Județ: {$expertData['judet']}, Localitate: {$expertData['localitate']})";
        $catreBLETdinCareApartineExpertul = $expertData['id_judet'];

        return $this->sendBletEmail($bletSubject, $bletContent, [
            'Dosar' => $nrDosar,
            "Experți Desemnați" => $expertInfo
        ], $catreBLETdinCareApartineExpertul);
    }

    public function sendEmailInlocuireBLET(array $expertData, mixed $nrDosar, $specializari = null)
    {
        $bletSubject = "Înlocuire expert în dosarul $nrDosar";
        $bletContent = "
        <p>Stimate Birou Local pentru Expertize Tehnice</p>
        <p>
            Vă informăm că a fost efectuată o înlocuire a unui expert în dosarul <b>$nrDosar</b><br>
            Specializarea: $specializari.<br>
        </p>";

        $expertInfo = "{$expertData['nume']} {$expertData['prenume']} (Județ: {$expertData['judet']}, Localitate: {$expertData['localitate']})";
        $catreBLETdinCareApartineExpertul = $expertData['id_judet'];

        return $this->sendBletEmail($bletSubject, $bletContent, [
            'Dosar' => $nrDosar,
            "Expert înlocuit" => $expertInfo
        ], $catreBLETdinCareApartineExpertul);

    }


    /**
     * Send notification email for expert replacement
     *
     * This is the main method for sending emails to experts who have been replaced.
     *
     * @param array $expertData Expert data
     * @param string $nrDosar Case number
     * @return bool|string True if queued successfully, error message otherwise
     */
    public
    function sendEmailInlocuireExpert($expertData, $nrDosar, $specializari = null)
    {
        return;//todo de scos cand dam drumul la notificarea expertilor
        if (empty($expertData['cnp'])) {
            writeToLogFile("sendEmailInlocuireExpert Date expert incomplete");
            return "Date expert incomplete.";
        }

        global $SESSION_instanta, $SESSION_judet;

        $subject = "Înlocuire expert în dosarul $nrDosar";
        $content = "
        <p>Stimate expert,</p>
        <p>
            Vă informăm că ați fost înlocuit din a efectua expertiza în dosarul <strong>$nrDosar</strong>.<br>
            Specializarea: $specializari.<br>
            Vă rugăm să contactați instanța ($SESSION_instanta, județ $SESSION_judet) pentru detalii suplimentare.<br>
        </p>";

        $result = $this->sendEmailToExpert($expertData['cnp'], $subject, $content, [
            'nume' => $expertData['nume'],
            'prenume' => $expertData['prenume'],
            'judet' => $expertData['judet'],
            'localitate' => $expertData['localitate'],
            'legitimatie' => $expertData['nr_legitimatie']
        ]);

        writeToLogFile("inlocuire expert---------$subject  --- $content");
        $this->logEmailOperation('expert_notification', $expertData['cnp'], $subject, [
            'description' => "Notificare expert înlocuit în dosarul $nrDosar",
            'data_after' => [
                'expert_cnp' => $expertData['cnp'],
                'expert_nume' => $expertData['nume'] . ' ' . $expertData['prenume'],
                'dosar' => $nrDosar
            ]
        ]);

        return $result;
    }

}

