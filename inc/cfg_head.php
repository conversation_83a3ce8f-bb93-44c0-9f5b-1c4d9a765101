<?php
require_once 'inc/cfg_session.php';
require_once 'inc/cfg_functions.php';

//AccessControl
$currentPage = basename($_SERVER['PHP_SELF']);
if (!AccessControl::checkPageAccess($currentPage)) {
    if (!isset($SESSION_id_rol)) {
        header('Location: login.php');
    } else {
        header('Location: ' . AccessControl::getDefaultPageForRole($SESSION_id_rol));
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="utf-8"/>
    <title>Aplicație desemnare experți tehnici judiciari</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <meta content="desemnare experți tehnici judiciari" name="keywords"/>
    <meta content="Aplicație desemnare experți tehnici judiciari" name="description"/>

    <link href="../assets/img/icon/var1.ico" rel="icon"/>
    <link href="../assets/css/googlefonts.css" rel="stylesheet"/>

    <link href="../assets/css/font-awesome-all.min.css" rel="stylesheet"/>
    <link href="../assets/css/bootstrap-icons.css" rel="stylesheet"/>

    <link href="../assets/lib/animate/animate.min.css" rel="stylesheet"/>
    <link href="../assets/lib/lightbox/css/lightbox.min.css" rel="stylesheet"/>
    <link href="../assets/lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet"/>

    <link href="../assets/css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../assets/css/style.css" rel="stylesheet"/>
    <link href="../assets/css/expert-list-custom.css" rel="stylesheet"/>

    <?php
    require 'vendor/autoload.php';
    ?>
</head>

<body>
<!-- Spinner Start -->
<div id="spinner"
     class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
    <div class="spinner-border text-primary" style="width: 3rem; height: 3rem" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>
<!-- Spinner End -->
