<?php
/**
 * Session check helper
 * Include this file at the beginning of all controller files to ensure session validity
 */

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/cfg_db.php';
require_once __DIR__ . '/cfg_functions.php';

// Include session configuration with SESSION_TIMEOUT constant
require_once __DIR__ . '/cfg_session.php';

// Only start the session if it hasn't been started already
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize Aura session
if (!isset($session_factory)) {
    $session_factory = new \Aura\Session\SessionFactory;
    $session = $session_factory->newInstance($_COOKIE);
    $sesiune = $session->getSegment('Vendor\Package\ClassName');
}

// Check if user is logged in
$logged_in = $sesiune->get('logged_in');

// Check session timeout
$lastActivity = $sesiune->get('last_activity');
$currentTime = time();
$sessionTimeout = SESSION_TIMEOUT;
$sessionValid = false;

if ($logged_in && $lastActivity && ($currentTime - $lastActivity <= $sessionTimeout)) {
    // Session is valid, but don't update last_activity here
    // This is only done in update_activity.php when user interacts
    $sessionValid = true;
} else if ($logged_in) {
    // Session has expired, log the event
    $username = $sesiune->get('username');
    $user_id = $sesiune->get('id_utilizator');

    // Log session expiration if we have user info
    if ($username && $user_id) {
        $dbConnection = DatabasePool::getConnection();
        log_auth_event($dbConnection, 'session_expired', [
            'user_id' => $user_id,
            'username' => $username,
            'description' => 'Sesiune expirată după ' . round(($currentTime - $lastActivity) / 60) . ' minute de inactivitate'
        ]);
    }

    // Destroy the session
    $session->destroy();

    // Session is invalid, return JSON response for AJAX requests
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode([
            'status' => 'error',
            'message' => 'Sesiunea dumneavoastră a expirat. Vă rugăm să vă autentificați din nou.',
            'code' => 'session_expired'
        ]);
        exit;
    } else {
        // For non-AJAX requests, redirect to login page
        header('Location: ../login.php');
        exit;
    }
} else {
    // User not logged in, redirect to login page
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode([
            'status' => 'error',
            'message' => 'Sesiunea dumneavoastră a expirat. Vă rugăm să vă autentificați din nou.',
            'code' => 'session_expired'
        ]);
        exit;
    } else {
        // For non-AJAX requests, redirect to login page
        header('Location: ../login.php');
        exit;
    }
}
