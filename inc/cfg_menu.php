<?php
require_once 'cfg_functions.php';
global $SESSION_id_rol, $SESSION_username, $SESSION_instanta, $SESSION_rol, $SESSION_judet;

$menuItems = AccessControl::getUserMenu($SESSION_id_rol);
$titleTooltip = "<small>$SESSION_instanta<br>Județ $SESSION_judet<br>$SESSION_rol</small>";
?>

<nav class="navbar navbar-expand-lg bg-light navbar-light sticky-top py-lg-0 px-4 px-lg-5 wow">

    <div class="position-absolute top-0 start-50 translate-middle-x" style="z-index: 1000;">
        <span class="badge bg-success bg-opacity-50 text-warden px-3 py-2" style="font-size: 0.8rem;">
            Aplicație în producție. Mediu real.
        </span>
    </div>


    <a href="index.php" class="navbar-brand p-0">
        <img class="img-fluid me-3" src="assets/img/logo-mj.png" alt="Icon"/>
        <h1 class="m-0 text-primary small">Desemnare Experți Tehnici Judiciari</h1>
    </a>
    <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse py-4 py-lg-0" id="navbarCollapse">
        <div class="navbar-nav ms-auto">
            <?php
            foreach ($menuItems as $menuKey => $item) {
                echo '<a href="' . $item['url'] . '" class="nav-item nav-link">';
                if ($item['icon']) {
                    echo '<i class="' . $item['icon'] . '"></i> ';
                }
                echo $item['text'] . '</a>';
            }
            ?>
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle d-flex align-items-center position-relative"
                   data-bs-toggle="dropdown"
                   style="padding: 0.5rem 1rem; background: rgba(var(--bs-primary-rgb), 0.05); border-radius: 6px; margin-left: 0.5rem;">
                    <div class="d-flex flex-column">
                        <div class="d-flex align-items-center">
                            <i class="fa fa-user-circle me-2" data-bs-toggle="tooltip" data-bs-html="true"
                               data-bs-placement="left"
                               title="<?=$titleTooltip?>"></i>
                            <span style="text-transform: lowercase; font-weight: 500;">
                                <?php echo $SESSION_username; ?>
                            </span>
                        </div>
                        <div class="small text-muted"
                             style="line-height: 1.2; margin-top: 0.25rem; max-width: 180px;
                                    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            <?php echo $SESSION_instanta; ?>
                        </div>
                    </div>
                </a>
                <div class="dropdown-menu rounded-0 rounded-bottom m-0 shadow-sm" style="min-width: 220px;">
                    <?php
                    $userDropdownItems = AccessControl::getUserDropdownItems($SESSION_id_rol);
                    $itemCount = count($userDropdownItems);
                    $i = 0;

                    foreach ($userDropdownItems as $key => $item):
                        $i++;
                        $borderClass = ($i < $itemCount) ? 'border-bottom' : '';
                    ?>
                    <a href="<?php echo $item['url']; ?>" <?php echo !empty($item['target']) ? 'target="'.$item['target'].'"' : ''; ?>
                       class="dropdown-item d-flex align-items-center py-2 px-3 <?php echo $borderClass; ?>"
                       style="transition: all 0.2s ease;">
                        <div class="icon-wrapper me-3" style="width: 24px; text-align: center;">
                            <i class="<?php echo $item['icon']; ?>"></i>
                        </div>
                        <div class="menu-text">
                            <div class="fw-medium"><?php echo $item['text']; ?></div>
                            <small class="text-muted"><?php echo $item['description']; ?></small>
                        </div>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</nav>