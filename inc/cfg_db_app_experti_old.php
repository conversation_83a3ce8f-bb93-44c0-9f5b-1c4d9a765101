<?php
require_once 'cfg_load_secrets.php';

class ConnectionPool
{
    private $pool;
    private $maxPoolSize;
    private $dbADDR;
    private $dbNAME;
    private $dbUSER;
    private $dbPASS;

    public function __construct($dbADDR, $dbNAME, $dbUSER, $dbPASS, $maxPoolSize = 10)
    {
        $this->pool = [];
        $this->maxPoolSize = $maxPoolSize;
        $this->dbADDR = $dbADDR;
        $this->dbNAME = $dbNAME;
        $this->dbUSER = $dbUSER;
        $this->dbPASS = $dbPASS;
    }

    public function getConnection()
    {
        $maxAttempts = 3;
        $attempt = 0;
        while ($attempt < $maxAttempts) {
            try {
                if (!empty($this->pool)) {
                    return array_pop($this->pool);
                }

                if (count($this->pool) < $this->maxPoolSize) {
                    $connectionOptions = [
                        "Database" => $this->dbNAME,
                        "Uid" => $this->dbUSER,
                        "PWD" => $this->dbPASS,
                        "ConnectionPooling" => 1, // Ensure connection pooling
                        "LoginTimeout" => 5, // Set timeout (in seconds)
                        "CharacterSet" => "UTF-8"
                        //"Encrypt" => "yes",
                        //"TrustServerCertificate" => "yes"
                    ];

                    $conn = sqlsrv_connect($this->dbADDR, $connectionOptions);

                    if ($conn === false) {
                        $errors = sqlsrv_errors();
                        $errorMessage = "SQL Server Connection Error: ";

                        if (!empty($errors)) {
                            foreach ($errors as $error) {
                                $errorCode = $error['code'];
                                $errorMsg = $error['message'];

                                // Categorizing common errors
                                if ($errorCode == 0 || stripos($errorMsg, 'timeout') !== false) {
                                    $errorMessage .= "Timeout occurred while connecting.";
                                } elseif (stripos($errorMsg, 'Login failed') !== false) {
                                    $errorMessage .= "Invalid credentials.";
                                } elseif (stripos($errorMsg, 'server is not reachable') !== false) {
                                    $errorMessage .= "Server is unreachable.";
                                } else {
                                    $errorMessage .= "Error Code: $errorCode - $errorMsg";
                                }
                            }
                        } else {
                            $errorMessage .= "Unknown error occurred.";
                        }
                        throw new Exception($errorMessage);
                    }
                    return $conn;
                }

                throw new Exception("Connection pool limit reached.");
            } catch (Exception $e) {
                // Get a database connection for logging if possible
                try {
                    $dbConnection = DatabasePool::getConnection();
                    log_audit_action($dbConnection, [
                        'action_type' => 'database_error',
                        'action_category' => 'error',
                        'action_level' => 'error',
                        'description' => "SQL Connection Attempt $attempt Failed: " . $e->getMessage()
                    ]);
                    DatabasePool::releaseConnection($dbConnection);
                } catch (Exception $logEx) {
                    // If we can't log to the database, fall back to error_log as a last resort
                    error_log("SQL Connection Attempt $attempt Failed: " . $e->getMessage());
                }

                if ($attempt === $maxAttempts - 1) {
                    return null; // Give up after max attempts
                }
                $attempt++;
                sleep(2); // Wait 2 seconds before retrying
            }
        }
        return null;
    }


    public function releaseConnection($conn)
    {
        if (count($this->pool) < $this->maxPoolSize) {
            $this->pool[] = $conn;
        } else {
            sqlsrv_close($conn);
        }
    }

    function getActiveConnections($conn)
    {
        $sql = "SELECT COUNT(*) AS ActiveConnections FROM sys.dm_exec_sessions WHERE is_user_process = 1;";
        $stmt = sqlsrv_query($conn, $sql);

        if ($stmt === false) {
            die(print_r(sqlsrv_errors(), true));
        }

        $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
        return $row['ActiveConnections'];
    }

}

// Usage
$dbADDR = $env['DB_APP_EXP_TEHNICI_OLD_SERVER'] ?? '';
$dbNAME = $env['DB_APP_EXP_TEHNICI_OLD_SCHEMA'] ?? '';
$dbUSER = $env['DB_APP_EXP_TEHNICI_OLD_USER'] ?? '';
$dbPASS = $env['DB_APP_EXP_TEHNICI_OLD_PASS'] ?? '';

$pool_EXP_TEHNICI_OLD = new ConnectionPool($dbADDR, $dbNAME, $dbUSER, $dbPASS, 100); // Max 100 connections
$conn_EXP_TEHNICI_OLD = $pool_EXP_TEHNICI_OLD->getConnection();

//$activeConnections = $pool_EXP_TEHNICI_OLD->getActiveConnections($conn_EXP_TEHNICI_OLD);echo "Number of active connections: " . $activeConnections;

//// Example query
//$sql = "SELECT * FROM NSpecializare"; // Replace with your table name
//$stmt = sqlsrv_query($conn_EXP_TEHNICI_OLD, $sql);
//
//if ($stmt === false) {
//   die(print_r(sqlsrv_errors(), true));
//}
//
//while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
//   echo "<pre>";
//   print_r($row);
//   echo "</pre>";
//}

//// Free the statement resource
//sqlsrv_free_stmt($stmt);
//
//// Release the connection back to the pool
//$pool_EXP_TEHNICI_OLD->releaseConnection($conn_EXP_TEHNICI_OLD);


?>