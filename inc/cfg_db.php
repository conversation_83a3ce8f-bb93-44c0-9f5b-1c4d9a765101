<?php
require_once 'cfg_load_secrets.php';

class DatabasePool {
    private static array $connections = [];
    private static array $availableConnections = [];
    private static int $maxConnections = 5;  // Set the max connections limit

    private static string $dbADDR;
    private static string $dbNAME;
    private static string $dbUSER;
    private static string $dbPASS;

    private function __construct() {} // Prevent instance creation

    public static function init(): void {
        self::$dbADDR = $GLOBALS['env']['DB_ADDR'] ?? '';
        self::$dbNAME = $GLOBALS['env']['DB_NAME'] ?? '';
        self::$dbUSER = $GLOBALS['env']['DB_USER'] ?? '';
        self::$dbPASS = $GLOBALS['env']['DB_PASS'] ?? '';

        // Pre-create a pool of connections
        for ($i = 0; $i < self::$maxConnections; $i++) {
            self::$connections[] = self::createConnection();
            self::$availableConnections[] = $i;
        }
    }

    private static function createConnection(): PDO {
        $dsn = "mysql:host=" . self::$dbADDR . ";dbname=" . self::$dbNAME;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,  // Enable error exceptions
            PDO::ATTR_PERSISTENT => true,                 // Enable persistent connections
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true    // Use buffered queries
        ];
        return new PDO($dsn, self::$dbUSER, self::$dbPASS, $options);
    }

    public static function getConnection(): ?PDO {
        if (empty(self::$availableConnections)) {
            throw new Exception("No available database connections.");
        }

        $connIndex = array_pop(self::$availableConnections);
        return self::$connections[$connIndex];
    }


    public static function releaseConnection(PDO $conn): void {
        if (!$conn) {
            return;
        }

        $index = array_search($conn, self::$connections, true);

        if ($index === false) {
            // Connection not from this pool - log warning but don't crash
            error_log("Warning: Attempted to release unknown database connection");
            return;
        }

        if (in_array($index, self::$availableConnections)) {
            // Already released - log warning but don't crash
            error_log("Warning: Attempted to release already available database connection");
            return;
        }

        self::$availableConnections[] = $index;
    }


    public static function getPoolStatus(): array {
        return [
            'total_connections' => count(self::$connections),
            'available_connections' => count(self::$availableConnections),
            'in_use_connections' => count(self::$connections) - count(self::$availableConnections)
        ];
    }

    public static function getAvailableCount(): int {
        return count(self::$availableConnections);
    }
}

// Initialize the connection pool
DatabasePool::init();
?>